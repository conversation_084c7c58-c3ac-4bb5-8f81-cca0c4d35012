"""Advanced group management features"""

from typing import Optional, List, Dict
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ChatPermissions
from telegram.ext import ContextTypes
from telegram.constants import ChatType, ParseMode
from ..database.db import Database
from ..utils.decorators import admin_only, group_only

class GroupManager:
    def __init__(self, db: Database):
        self.db = db

    @admin_only()
    @group_only()
    async def set_group_title(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Change group title"""
        if not context.args:
            await update.message.reply_text("Please provide a new title for the group.")
            return
            
        new_title = " ".join(context.args)
        try:
            await context.bot.set_chat_title(update.effective_chat.id, new_title)
            await update.message.reply_text("✅ Group title has been updated!")
        except Exception as e:
            await update.message.reply_text(f"Failed to update group title: {str(e)}")

    @admin_only()
    @group_only()
    async def set_group_description(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Set group description"""
        if not context.args:
            await update.message.reply_text("Please provide a description for the group.")
            return
            
        description = " ".join(context.args)
        try:
            await context.bot.set_chat_description(update.effective_chat.id, description)
            await update.message.reply_text("✅ Group description has been updated!")
        except Exception as e:
            await update.message.reply_text(f"Failed to update description: {str(e)}")

    @admin_only()
    @group_only()
    async def set_chat_photo(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Set new group photo"""
        if not update.message.reply_to_message or not update.message.reply_to_message.photo:
            await update.message.reply_text("Please reply to an image to set it as group photo.")
            return
            
        try:
            photo_file = await update.message.reply_to_message.photo[-1].get_file()
            await context.bot.set_chat_photo(
                update.effective_chat.id,
                photo_file
            )
            await update.message.reply_text("✅ Group photo has been updated!")
        except Exception as e:
            await update.message.reply_text(f"Failed to update group photo: {str(e)}")

    @admin_only()
    @group_only()
    async def pin_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Pin a message in the group"""
        if not update.message.reply_to_message:
            await update.message.reply_text("Please reply to a message to pin it.")
            return
            
        try:
            await context.bot.pin_chat_message(
                update.effective_chat.id,
                update.message.reply_to_message.message_id,
                disable_notification=False
            )
            await update.message.reply_text("✅ Message has been pinned!")
        except Exception as e:
            await update.message.reply_text(f"Failed to pin message: {str(e)}")

    @admin_only()
    @group_only()
    async def unpin_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Unpin a message in the group"""
        if not update.message.reply_to_message:
            await update.message.reply_text("Please reply to a pinned message to unpin it.")
            return
            
        try:
            await context.bot.unpin_chat_message(
                update.effective_chat.id,
                update.message.reply_to_message.message_id
            )
            await update.message.reply_text("✅ Message has been unpinned!")
        except Exception as e:
            await update.message.reply_text(f"Failed to unpin message: {str(e)}")

    @admin_only()
    @group_only()
    async def unpin_all_messages(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Unpin all messages in the group"""
        try:
            await context.bot.unpin_all_chat_messages(update.effective_chat.id)
            await update.message.reply_text("✅ All pinned messages have been unpinned!")
        except Exception as e:
            await update.message.reply_text(f"Failed to unpin messages: {str(e)}")

    @admin_only()
    @group_only()
    async def set_chat_permissions(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Set default chat permissions"""
        if not context.args or len(context.args) < 2:
            await update.message.reply_text(
                "Usage: /setperms [permission] [on/off]\n"
                "Available permissions:\n"
                "• messages - Send messages\n"
                "• media - Send media\n"
                "• polls - Create polls\n"
                "• links - Send links\n"
                "• invite - Add members\n"
                "• pin - Pin messages\n"
                "• info - Change info\n"
                "• preview - Web page previews"
            )
            return

        permission = context.args[0].lower()
        state = context.args[1].lower() == "on"
        
        permission_map = {
            "messages": "can_send_messages",
            "media": [
                "can_send_audios",
                "can_send_documents",
                "can_send_photos",
                "can_send_videos",
                "can_send_video_notes",
                "can_send_voice_notes",
            ],
            "polls": "can_send_polls",
            "links": "can_send_other_messages",
            "invite": "can_invite_users",
            "pin": "can_pin_messages",
            "info": "can_change_info",
            "preview": "can_add_web_page_previews",
        }
        
        if permission not in permission_map:
            await update.message.reply_text(f"Invalid permission. Choose from: {', '.join(permission_map.keys())}")
            return
            
        try:
            current_permissions = await context.bot.get_chat(update.effective_chat.id)
            perms_dict = current_permissions.permissions.to_dict()

            mapped = permission_map[permission]
            if isinstance(mapped, list):
                for key in mapped:
                    perms_dict[key] = state
            else:
                perms_dict[mapped] = state

            new_permissions = ChatPermissions(**perms_dict)
            await context.bot.set_chat_permissions(update.effective_chat.id, new_permissions)
            
            await update.message.reply_text(
                f"✅ Permission '{permission}' has been {'enabled' if state else 'disabled'}!"
            )
        except Exception as e:
            await update.message.reply_text(f"Failed to update permissions: {str(e)}")

    @admin_only()
    @group_only()
    async def set_chat_sticker_set(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Set group sticker set"""
        if not context.args:
            await update.message.reply_text("Please provide the sticker set name.")
            return
            
        sticker_set_name = context.args[0]
        try:
            await context.bot.set_chat_sticker_set(update.effective_chat.id, sticker_set_name)
            await update.message.reply_text("✅ Group sticker set has been updated!")
        except Exception as e:
            await update.message.reply_text(f"Failed to set sticker set: {str(e)}")

    @admin_only()
    @group_only()
    async def delete_chat_sticker_set(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Remove group sticker set"""
        try:
            await context.bot.delete_chat_sticker_set(update.effective_chat.id)
            await update.message.reply_text("✅ Group sticker set has been removed!")
        except Exception as e:
            await update.message.reply_text(f"Failed to remove sticker set: {str(e)}")

    @admin_only()
    @group_only()
    async def set_chat_username(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Set group username"""
        if not context.args:
            await update.message.reply_text("Please provide a username for the group.")
            return
            
        username = context.args[0]
        if not username.startswith("@"):
            username = "@" + username
            
        try:
            await context.bot.set_chat_username(update.effective_chat.id, username[1:])
            await update.message.reply_text("✅ Group username has been updated!")
        except Exception as e:
            await update.message.reply_text(f"Failed to set username: {str(e)}")

    @group_only()
    async def chat_info(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Get detailed chat information"""
        chat = await context.bot.get_chat(update.effective_chat.id)
        member_count = await context.bot.get_chat_member_count(update.effective_chat.id)
        
        # Witty, savage group info with HTML formatting
        info = (
            f"<b>📊 Group Flex Report</b>\n\n"
            f"<b>Title:</b> {chat.title} — Where legends gather!\n"
            f"<b>Type:</b> {chat.type.title()} — Only the bold allowed.\n"
            f"<b>ID:</b> <code>{chat.id}</code> — Not just numbers, it's a legacy.\n"
            f"<b>Members:</b> {member_count} — More squad than your rival's DMs!\n"
        )
        if chat.username:
            info += f"<b>Username:</b> @{chat.username} — Branded and proud.\n"
        try:
            chat_info = await context.bot.get_chat(chat.id)
            if hasattr(chat_info, 'description') and chat_info.description:
                info += f"<b>Description:</b> {chat_info.description} — Poetry in motion.\n"
        except Exception as e:
            logger.error(f"Error getting chat description: {e}")
        if chat.invite_link:
            info += f"<b>Invite Link:</b> {chat.invite_link} — VIP access only.\n"
        if chat.sticker_set_name:
            info += f"<b>Sticker Set:</b> {chat.sticker_set_name} — Express yourself, champ!\n"
        if chat.linked_chat_id:
            info += f"<b>Linked Chat ID:</b> <code>{chat.linked_chat_id}</code> — Connections on another level.\n"
        permissions = chat.permissions.to_dict() if chat.permissions else {}
        if permissions:
            info += "\n<b>Default Permissions:</b>\n"
            for perm, value in permissions.items():
                if perm != "is_anonymous":
                    pretty = perm.replace('can_', '').replace('_', ' ').title()
                    status = '✅' if value else '❌'
                    info += f"• {pretty}: {status} — {'Let them cook!' if value else 'Not in this house!'}\n"
        await update.message.reply_text(info, parse_mode=ParseMode.HTML)
