from dataclasses import dataclass
from typing import Dict, Optional, TypedDict, BinaryIO, Union
import requests
import time
import os
from pathlib import Path
from fake_useragent import UserAgent
import random
from rich import print as rprint

# >>>>>>>>>DERESPOCT<<<<<<<<<<<
# Old TranscriptionResult keeping for reference
"""
class TranscriptionResult(TypedDict):
    text: str
    confidence: float
    audio_duration: float
    language_code: str
    status: str
"""

class TranscriptionResult(TypedDict):
    text: str
    status: str
    confidence: float
    audio_duration: Optional[float]
    language_code: Optional[str]
    text_segments: Optional[list]
    audio_events: Optional[list]


@dataclass
class TranscriptionConfig:
    elevenlabs_api_url: str = "https://api.elevenlabs.io/v1/speech-to-text"
    model_id: str = "scribe_v1"
    allow_unauthenticated: bool = True
    tag_audio_events: bool = True
    diarize: bool = True
    timeout: int = 60  # 60 second timeout for requests
    
    # >>>>>>>>>DERESPOCT<<<<<<<<<<<
    # Old AssemblyAI config kept for reference
    """
    base_url: str = "https://www.assemblyai.com/playground/api"
    speech_model: str = "best"  # or "best"
    language_detection: bool = True
    timeout: int = 60  # 60 second timeout for requests
    """


class TranscriptionError(Exception):
    """Base exception for transcription errors"""
    pass


class UploadError(TranscriptionError):
    """Raised when file upload fails"""
    pass


class TranscriptionServiceError(TranscriptionError):
    """Raised when transcription service fails"""
    pass


class TimeoutError(TranscriptionError):
    """Raised when a request times out"""
    pass


class ElevenLabsTranscriber:
    def __init__(self, config: Optional[TranscriptionConfig] = None) -> None:
        self.config = config or TranscriptionConfig()
        self.user_agent = UserAgent()
        self.headers: Dict[str, str] = self._generate_headers()

    def _generate_headers(self) -> Dict[str, str]:
        """Generate headers with a random user agent"""
        return {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'User-Agent': self.user_agent.random
        }

    def _refresh_user_agent(self) -> None:
        """Refresh the user agent string"""
        self.headers['User-Agent'] = self.user_agent.random

    def transcribe_file(self, audio_path: Union[Path, str]) -> Dict:
        """
        Synchronous method to transcribe a local audio file using ElevenLabs API.
        
        Args:
            audio_path: Path to the local audio file
            
        Returns:
            Dictionary containing transcription result
            
        Raises:
            TranscriptionError: If transcription fails
            TimeoutError: If any request times out
        """
        try:
            audio_path = Path(audio_path)
            if not audio_path.is_file():
                raise FileNotFoundError(f"The file '{audio_path}' does not exist.")

            # Refresh user agent before request
            self._refresh_user_agent()
            
            # Construct the API URL with parameters
            api_url = f"{self.config.elevenlabs_api_url}"
            if self.config.allow_unauthenticated:
                api_url += "?allow_unauthenticated=1"
            
            rprint(f"[bold green]Transcribing audio using ElevenLabs API: {audio_path}[/]")
            
            # Read the file content
            with open(audio_path, 'rb') as audio_file:
                files = {
                    'file': audio_file,
                    'model_id': (None, self.config.model_id),
                    'tag_audio_events': (None, 'true' if self.config.tag_audio_events else 'false'),
                    'diarize': (None, 'true' if self.config.diarize else 'false')
                }
                
                response = requests.post(
                    api_url,
                    files=files,
                    headers=self.headers,
                    timeout=self.config.timeout
                )
                
                rprint(f"[bold blue]API Response Status: {response.status_code}[/]")
                
                if response.status_code != 200:
                    raise TranscriptionServiceError(f"ElevenLabs API returned error: {response.status_code} - {response.text}")
                
                result = response.json()
                
                # Format result to match our expected structure
                formatted_result = {
                    "text": result.get("text", ""),
                    "status": "completed",
                    "confidence": result.get("confidence", 1.0),
                    "audio_duration": result.get("audio_duration", None),
                    "language_code": result.get("language", "en"),
                    "text_segments": result.get("text_segments", None),
                    "audio_events": result.get("audio_events", None)
                }
                
                return formatted_result
                
        except requests.exceptions.Timeout:
            raise TimeoutError("Transcription request timed out after 60 seconds")
        except Exception as e:
            if isinstance(e, TimeoutError):
                raise
            raise TranscriptionError(f"Transcription failed: {str(e)}")


# >>>>>>>>>DERESPOCT<<<<<<<<<<<
# Old AssemblyAI implementation kept for reference
"""
class AssemblyAITranscriber:
    def __init__(self, config: Optional[TranscriptionConfig] = None) -> None:
        self.config = config or TranscriptionConfig()
        self.user_agent = UserAgent()
        self.headers: Dict[str, str] = self._generate_headers()

    def _generate_headers(self) -> Dict[str, str]:
        '''Generate headers with a random user agent'''
        return {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Content-Type': 'application/json',
            'User-Agent': self.user_agent.random
        }

    def _refresh_user_agent(self) -> None:
        '''Refresh the user agent string'''
        self.headers['User-Agent'] = self.user_agent.random

    def upload_audio_to_catbox(self, audio_path: Path | str) -> str:
        '''
        Uploads an audio file to catbox.moe and returns the public URL.

        Args:
            audio_path: Path to the local audio file

        Returns:
            Public URL of the uploaded audio file

        Raises:
            FileNotFoundError: If the audio file does not exist
            UploadError: If the upload fails
            TimeoutError: If the request times out
        '''
        audio_path = Path(audio_path)
        if not audio_path.is_file():
            raise FileNotFoundError(f"The file '{audio_path}' does not exist.")

        # Refresh user agent before request
        self._refresh_user_agent()

        # Get file extension
        file_extension = audio_path.suffix.lower()
        
        # Ensure the file has a valid extension for audio
        valid_extensions = ['.mp3', '.wav', '.ogg', '.m4a', '.flac', '.aac']
        if file_extension not in valid_extensions:
            # Default to .mp3 if extension is not recognized
            file_extension = '.mp3'
        
        # Generate a random filename
        random_filename = f"audio_{int(time.time())}_{random.randint(1000, 9999)}{file_extension}"

        with open(audio_path, 'rb') as audio_file:
            try:
                files = {
                    'reqtype': (None, 'fileupload'),
                    'fileToUpload': (random_filename, audio_file, f'audio/{file_extension[1:]}')
                }
                
                response = requests.post(
                    'https://catbox.moe/user/api.php',
                    files=files,
                    timeout=self.config.timeout,
                    headers={'User-Agent': self.headers['User-Agent']}
                )
                
                response.raise_for_status()
                audio_url = response.text.strip()
                
                if not audio_url.startswith('http'):
                    raise UploadError("Received an invalid URL from catbox.moe")
                    
                return audio_url
                
            except requests.exceptions.Timeout:
                raise TimeoutError("Upload to catbox.moe timed out after 60 seconds")
            except requests.exceptions.RequestException as e:
                raise UploadError(f"Failed to upload audio to catbox.moe: {e}") from e

    def submit_transcription(self, audio_url: str) -> str:
        '''Submit audio for transcription and return the transcript ID'''
        payload = {
            "audio_url": audio_url,
            "speech_model": self.config.speech_model,
            "language_detection": self.config.language_detection
        }

        # Refresh user agent before request
        self._refresh_user_agent()

        try:
            response = requests.post(
                f"{self.config.base_url}/transcript",
                headers=self.headers,
                json=payload,
                timeout=self.config.timeout
            )
            response.raise_for_status()
            return response.json()['data']['id']
        except requests.exceptions.Timeout:
            raise TimeoutError("Transcription submission timed out after 60 seconds")
        except requests.exceptions.RequestException as e:
            raise TranscriptionServiceError(f"Failed to submit transcription: {e}") from e

    def get_transcription_result(self, transcript_id: str) -> TranscriptionResult:
        '''Get transcription result for a given ID'''
        # Refresh user agent before request
        self._refresh_user_agent()

        try:
            response = requests.get(
                f"{self.config.base_url}/transcript/{transcript_id}",
                headers=self.headers,
                timeout=self.config.timeout
            )
            response.raise_for_status()
            return response.json()['data']
        except requests.exceptions.Timeout:
            raise TimeoutError("Getting transcription result timed out after 60 seconds")
        except requests.exceptions.RequestException as e:
            raise TranscriptionServiceError(f"Failed to get transcription result: {e}") from e

    def transcribe_file(self, audio_path: Path | str) -> Dict:
        '''
        Synchronous method to transcribe a local audio file.
        
        Args:
            audio_path: Path to the local audio file
            
        Returns:
            Dictionary containing transcription result
            
        Raises:
            TranscriptionError: If transcription fails
            TimeoutError: If any request times out
        '''
        try:
            # Upload the file
            audio_url = self.upload_audio_to_catbox(audio_path)
            
            # Submit for transcription
            transcript_id = self.submit_transcription(audio_url)
            
            # Poll for results with timeout handling
            start_time = time.time()
            max_poll_time = 300  # 5 minutes max polling time
            
            while True:
                # Check if we've exceeded maximum polling time
                if time.time() - start_time > max_poll_time:
                    raise TimeoutError(f"Transcription polling exceeded maximum time of {max_poll_time} seconds")
                
                # Get result with timeout handling
                result = self.get_transcription_result(transcript_id)
                
                if result['status'] == 'completed':
                    return result
                elif result['status'] == 'error':
                    raise TranscriptionError(f"Transcription failed: {result.get('error', 'Unknown error')}")
                
                # Wait before polling again (with jitter to avoid rate limiting)
                time.sleep(3 + random.uniform(0, 1))
                
        except Exception as e:
            if isinstance(e, TimeoutError):
                raise
            raise TranscriptionError(f"Transcription failed: {str(e)}")
"""

# For backward compatibility, use the new transcriber class
AssemblyAITranscriber = ElevenLabsTranscriber
