"""
TypeGPT Moderation Service
-------------------------
Modern content moderation using TypeGPT API for text, image, and video content
"""

import logging
import asyncio
import aiohttp
import base64
from typing import Dict, Optional, Tuple, Union
import tempfile
import os
from pathlib import Path

logger = logging.getLogger(__name__)

class TypeGPTModerator:
    """Content moderation using TypeGPT API."""
    
    API_URL = "https://mono.typegpt.net/v3/moderations"
    
    def __init__(self):
        """Initialize the TypeGPT moderator."""
        self.session = None
        self._temp_files = set()
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session."""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession()
        return self.session
    
    async def _make_moderation_request(
        self, 
        text: Optional[str] = None,
        image: Optional[str] = None,
        video: Optional[str] = None,
        voice: Optional[str] = None,
        language: str = "en",
        video_frame_count: int = 8
    ) -> Optional[Dict]:
        """Make a moderation request to TypeGPT API."""
        try:
            session = await self._get_session()
            
            payload = {
                "model": "nai-moderation-latest",
                "language": language,
                "video_frame_count": video_frame_count
            }
            
            # Add content based on what's provided
            input_data = []
            if text:
                input_data.append(text)
                payload["input"] = input_data
            
            if image:
                payload["image"] = image
            
            if video:
                payload["video"] = video
                
            if voice:
                payload["voice"] = voice
            
            headers = {
                'accept': 'application/json',
                'Content-Type': 'application/json'
            }
            
            async with session.post(self.API_URL, json=payload, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"TypeGPT moderation response: {result}")
                    return result
                else:
                    logger.error(f"TypeGPT API error: {response.status} - {await response.text()}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error making TypeGPT moderation request: {e}")
            return None
    
    async def moderate_text(self, text: str) -> Tuple[bool, Dict]:
        """
        Moderate text content.
        
        Returns:
            Tuple[bool, Dict]: (is_flagged, moderation_details)
        """
        try:
            result = await self._make_moderation_request(text=text)
            
            if not result or 'results' not in result or not result['results']:
                logger.warning("No moderation results received for text")
                return False, {}
            
            moderation_result = result['results'][0]
            is_flagged = moderation_result.get('flagged', False)
            
            return is_flagged, {
                'categories': moderation_result.get('categories', {}),
                'category_scores': moderation_result.get('category_scores', {}),
                'reason': moderation_result.get('reason'),
                'moderation_type': moderation_result.get('moderation_type', 'text'),
                'transcribed_text': moderation_result.get('transcribed_text')
            }
            
        except Exception as e:
            logger.error(f"Error moderating text: {e}")
            return False, {}
    
    async def moderate_image(self, image_path: str) -> Tuple[bool, Dict]:
        """
        Moderate image content.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Tuple[bool, Dict]: (is_flagged, moderation_details)
        """
        try:
            # Convert image to base64
            image_base64 = await self._file_to_base64(image_path)
            if not image_base64:
                return False, {}
            
            result = await self._make_moderation_request(image=image_base64)
            
            if not result or 'results' not in result or not result['results']:
                logger.warning("No moderation results received for image")
                return False, {}
            
            moderation_result = result['results'][0]
            is_flagged = moderation_result.get('flagged', False)
            
            return is_flagged, {
                'categories': moderation_result.get('categories', {}),
                'category_scores': moderation_result.get('category_scores', {}),
                'reason': moderation_result.get('reason'),
                'moderation_type': moderation_result.get('moderation_type', 'image'),
                'transcribed_text': moderation_result.get('transcribed_text')
            }
            
        except Exception as e:
            logger.error(f"Error moderating image: {e}")
            return False, {}
    
    async def moderate_video(self, video_path: str, frame_count: int = 8) -> Tuple[bool, Dict]:
        """
        Moderate video content.
        
        Args:
            video_path: Path to the video file
            frame_count: Number of frames to analyze
            
        Returns:
            Tuple[bool, Dict]: (is_flagged, moderation_details)
        """
        try:
            # Convert video to base64
            video_base64 = await self._file_to_base64(video_path)
            if not video_base64:
                return False, {}
            
            result = await self._make_moderation_request(
                video=video_base64, 
                video_frame_count=frame_count
            )
            
            if not result or 'results' not in result or not result['results']:
                logger.warning("No moderation results received for video")
                return False, {}
            
            moderation_result = result['results'][0]
            is_flagged = moderation_result.get('flagged', False)
            
            return is_flagged, {
                'categories': moderation_result.get('categories', {}),
                'category_scores': moderation_result.get('category_scores', {}),
                'reason': moderation_result.get('reason'),
                'moderation_type': moderation_result.get('moderation_type', 'video'),
                'transcribed_text': moderation_result.get('transcribed_text')
            }
            
        except Exception as e:
            logger.error(f"Error moderating video: {e}")
            return False, {}
    
    async def moderate_voice(self, voice_path: str) -> Tuple[bool, Dict]:
        """
        Moderate voice content.
        
        Args:
            voice_path: Path to the voice file
            
        Returns:
            Tuple[bool, Dict]: (is_flagged, moderation_details)
        """
        try:
            # Convert voice to base64
            voice_base64 = await self._file_to_base64(voice_path)
            if not voice_base64:
                return False, {}
            
            result = await self._make_moderation_request(voice=voice_base64)
            
            if not result or 'results' not in result or not result['results']:
                logger.warning("No moderation results received for voice")
                return False, {}
            
            moderation_result = result['results'][0]
            is_flagged = moderation_result.get('flagged', False)
            
            return is_flagged, {
                'categories': moderation_result.get('categories', {}),
                'category_scores': moderation_result.get('category_scores', {}),
                'reason': moderation_result.get('reason'),
                'moderation_type': moderation_result.get('moderation_type', 'voice'),
                'transcribed_text': moderation_result.get('transcribed_text')
            }
            
        except Exception as e:
            logger.error(f"Error moderating voice: {e}")
            return False, {}
    
    async def moderate_sticker(self, sticker_path: str) -> Tuple[bool, Dict]:
        """
        Moderate sticker content (treats as image or video based on format).
        
        Args:
            sticker_path: Path to the sticker file
            
        Returns:
            Tuple[bool, Dict]: (is_flagged, moderation_details)
        """
        try:
            file_ext = Path(sticker_path).suffix.lower()
            
            # Animated stickers (treat as video)
            if file_ext in ['.webm', '.tgs', '.gif']:
                return await self.moderate_video(sticker_path, frame_count=4)
            else:
                # Static stickers (treat as image)
                return await self.moderate_image(sticker_path)
                
        except Exception as e:
            logger.error(f"Error moderating sticker: {e}")
            return False, {}
    
    async def _file_to_base64(self, file_path: str) -> Optional[str]:
        """Convert file to base64 string."""
        try:
            with open(file_path, 'rb') as file:
                file_content = file.read()
                base64_string = base64.b64encode(file_content).decode('utf-8')
                return base64_string
        except Exception as e:
            logger.error(f"Error converting file to base64: {e}")
            return None
    
    async def cleanup(self):
        """Clean up resources."""
        try:
            # Close aiohttp session
            if self.session and not self.session.closed:
                await self.session.close()
            
            # Clean up temporary files
            for temp_file in self._temp_files.copy():
                try:
                    if os.path.exists(temp_file):
                        os.unlink(temp_file)
                    self._temp_files.discard(temp_file)
                except Exception as e:
                    logger.error(f"Error cleaning up temp file {temp_file}: {e}")
            
            logger.info("TypeGPT moderator cleanup completed")
        except Exception as e:
            logger.error(f"Error during TypeGPT moderator cleanup: {e}")


# Singleton instance
_moderator_instance = None

async def get_typegpt_moderator() -> TypeGPTModerator:
    """Get singleton instance of TypeGPT moderator."""
    global _moderator_instance
    if _moderator_instance is None:
        _moderator_instance = TypeGPTModerator()
    return _moderator_instance


# Convenience functions for backward compatibility
async def check_text(text: str) -> bool:
    """Quick function to check if text is flagged."""
    moderator = await get_typegpt_moderator()
    is_flagged, _ = await moderator.moderate_text(text)
    return is_flagged

async def check_image(image_path: str) -> bool:
    """Quick function to check if image is flagged."""
    moderator = await get_typegpt_moderator()
    is_flagged, _ = await moderator.moderate_image(image_path)
    return is_flagged

async def check_video(video_path: str) -> bool:
    """Quick function to check if video is flagged."""
    moderator = await get_typegpt_moderator()
    is_flagged, _ = await moderator.moderate_video(video_path)
    return is_flagged