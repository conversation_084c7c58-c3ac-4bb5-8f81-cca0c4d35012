"""
⚙️ Configuration Module
-------------------
Handles bot configuration and environment variables.
Powered by <PERSON>ing<PERSON><PERSON> BOT (AI ADMIN)
"""

import os
import logging
from typing import Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Configuration class for the bot."""
    
    def __init__(self):
        """Initialize configuration from environment variables."""
        # Bot Token (Required)
        self.TELEGRAM_BOT_TOKEN: str = os.getenv('TELEGRAM_BOT_TOKEN')
        if not self.TELEGRAM_BOT_TOKEN:
            raise ValueError("❌ TELEGRAM_BOT_TOKEN not found in environment variables. Please set it in your .env file.")
        
        # Bot Configuration
        self.BOT_USERNAME: str = os.getenv('BOT_USERNAME', 'OEvortex')
        self.BOT_OWNER: str = os.getenv('BOT_OWNER', 'OEvortex')
        self.DEBUG_MODE: bool = os.getenv('DEBUG_MODE', 'False').lower() == 'true'
        
        # Feature Flags
        self.ENABLE_AI_FEATURES: bool = os.getenv('ENABLE_AI_FEATURES', 'True').lower() == 'true'
        self.ENABLE_TRANSLATION: bool = os.getenv('ENABLE_TRANSLATION', 'True').lower() == 'true'
        self.ENABLE_CONTENT_FILTER: bool = os.getenv('ENABLE_CONTENT_FILTER', 'True').lower() == 'true'
        
        # Command Cooldowns (in seconds)
        self.FORTUNE_COOLDOWN: int = int(os.getenv('FORTUNE_COOLDOWN', '30'))
        self.ROAST_COOLDOWN: int = int(os.getenv('ROAST_COOLDOWN', '10'))
        self.JOKE_COOLDOWN: int = int(os.getenv('JOKE_COOLDOWN', '30'))
        self.DICE_COOLDOWN: int = int(os.getenv('DICE_COOLDOWN', '30'))
        self.COIN_COOLDOWN: int = int(os.getenv('COIN_COOLDOWN', '30'))
        self.HUG_COOLDOWN: int = int(os.getenv('HUG_COOLDOWN', '30'))
        
        # Spam Protection
        self.MAX_MESSAGES: int = int(os.getenv('MAX_MESSAGES', '10'))
        self.TIME_WINDOW: int = int(os.getenv('TIME_WINDOW', '60'))
        
        # API Keys (Optional)
        self.OPENAI_API_KEY: Optional[str] = os.getenv('OPENAI_API_KEY')
        
        # Database Configuration
        self.SQLITE_DB_PATH: str = os.getenv('SQLITE_DB_PATH', 'data/bot_database.db')
        
        # Validate configuration
        self._validate_config()
    
    def _validate_config(self) -> None:
        """Validate the configuration values."""
        if not self.TELEGRAM_BOT_TOKEN:
            raise ValueError("❌ TELEGRAM_BOT_TOKEN is required")
            
        if not self.BOT_USERNAME:
            raise ValueError("⚠️ BOT_USERNAME not set, using default")
            
        if not self.BOT_OWNER:
            raise ValueError("⚠️ BOT_OWNER not set, using default")

# Create a global instance
config = Config()
