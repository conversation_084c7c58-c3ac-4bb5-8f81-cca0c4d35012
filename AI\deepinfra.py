from typing import Dict, List, Optional, Generator, Union, Set, Any
import json
import base64
import requests
from pydantic import BaseModel
from .logger import log_model_request, log_model_response, log_stream_chunk


DEFAULT_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
    'Accept-Language': 'en,fr-FR;q=0.9,fr;q=0.8,es-ES;q=0.7,es;q=0.6,en-US;q=0.5,am;q=0.4,de;q=0.3',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Content-Type': 'application/json',
    'Origin': 'https://deepinfra.com',
    'Pragma': 'no-cache',
    'Referer': 'https://deepinfra.com/',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-site',
    'X-Deepinfra-Source': 'web-embed',
    'accept': 'text/event-stream',
    'sec-ch-ua': '"Google Chrome";v="119", "Chromium";v="119", "Not?A_Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"'
}


class Message(BaseModel):
    role: str
    content: str


class Usage(BaseModel):
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int


class Choice(BaseModel):
    index: int
    delta: Dict[str, str]
    logprobs: Optional[Dict] = None
    finish_reason: Optional[str] = None


class ChatCompletionChunk(BaseModel):
    id: str
    object: str
    created: int
    model: str
    choices: List[Choice]
    usage: Optional[Usage] = None


class DeepInfraAI:
    label: str = "DeepInfra AI"
    url: str = "https://deepinfra.com"
    working: bool = True
    supports_system_message: bool = True
    supports_message_history: bool = True

    # Available models
    AVAILABLE_MODELS: Set[str] = {
        "deepseek-ai/DeepSeek-R1",
        "deepseek-ai/DeepSeek-R1-Distill-Llama-70B",
        "deepseek-ai/DeepSeek-V3",
        "meta-llama/Llama-3.3-70B-Instruct-Turbo",
        "mistralai/Mistral-Small-24B-Instruct-2501",
        "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B",
        "microsoft/phi-4",
        "meta-llama/Meta-Llama-3.1-70B-Instruct",
        "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo",
        "Qwen/Qwen2.5-Coder-32B-Instruct",
        "nvidia/Llama-3.1-Nemotron-70B-Instruct",
        "Qwen/Qwen2.5-72B-Instruct",
        "NousResearch/Hermes-3-Llama-3.1-405B",
        "NovaSky-AI/Sky-T1-32B-Preview,"
    }

    # API endpoint for chat completions
    chat_api_endpoint: str = "https://api.deepinfra.com/v1/openai/chat/completions"

    @classmethod
    def get_available_models(cls) -> Set[str]:
        """Returns the set of available models."""
        return cls.AVAILABLE_MODELS

    @classmethod
    def generate_text(
        cls,
        model: str,
        messages: List[Dict[str, str]],
        temperature: Optional[float] = 0.7,
        max_tokens: Optional[int] = 2049,
        top_p: Optional[float] = 1,
        frequency_penalty: Optional[float] = None,
        stop: Optional[List[str]] = None,
        stream: Optional[bool] = False,

    ) -> Union[str, Generator[str, None, None]]:
        # Log model request
        log_model_request(model, messages, temperature=temperature, max_tokens=max_tokens)

        if model not in cls.AVAILABLE_MODELS:
            error_msg = f"Invalid model: {model}. Available models: {', '.join(sorted(cls.AVAILABLE_MODELS))}"
            log_model_response(model, error_msg, error=True)
            raise ValueError(error_msg)

        headers = DEFAULT_HEADERS.copy()
        
        payload = {
            'model': model,
            'messages': messages,
            'temperature': temperature,
            'max_tokens': max_tokens,
            'top_p': top_p,
            'frequency_penalty': frequency_penalty,
            'stop': stop,
            'stream': True  # Always use streaming internally
        }
        # Remove keys with None values
        payload = {k: v for k, v in payload.items() if v is not None}

        def process_stream():
            try:
                response = requests.post(
                    cls.chat_api_endpoint,
                    headers=headers,
                    json=payload,
                    stream=True
                )
                response.raise_for_status()
                
                full_text = ""
                for line in response.iter_lines(decode_unicode=True):
                    if line and line.startswith("data: "):
                        if line.strip() == "data: [DONE]":
                            break
                        try:
                            chunk_data = json.loads(line[6:])  # Skip "data: " prefix
                            if 'choices' in chunk_data and chunk_data['choices']:
                                choice = chunk_data['choices'][0]
                                if 'delta' in choice and 'content' in choice['delta']:
                                    content = choice['delta']['content']
                                    full_text += content
                                    log_stream_chunk(model, content)
                                    yield content
                        except json.JSONDecodeError:
                            continue
                # Log successful response
                log_model_response(model, full_text)
                return full_text
            except Exception as e:
                error_msg = str(e)
                log_model_response(model, error_msg, error=True)
                raise

        if stream:
            # For streaming, return the generator directly
            return process_stream()
        else:
            # For non-streaming, collect all chunks and return the complete text
            full_text = ""
            for chunk in process_stream():
                full_text += chunk
            return full_text


class DeepInfraVLM:
    """Vision-Language Model implementation for DeepInfra API.
    
    Supports:
    - Image understanding and analysis
    - Visual question answering
    - Image-based chat
    - Streaming responses
    """
    
    # Available VLM models
    AVAILABLE_MODELS: Set[str] = {
        "meta-llama/Llama-3.2-90B-Vision-Instruct",
        "meta-llama/Llama-3.2-11B-Vision-Instruct",
    }
    
    # API endpoint
    chat_api_endpoint: str = "https://api.deepinfra.com/v1/openai/chat/completions"

    def __init__(
        self,
        model: str = "meta-llama/Llama-3.2-90B-Vision-Instruct",
        system_message: str = "You are a helpful visual AI assistant."
    ):
        """Initialize the VLM with specified model and settings.
        
        Args:
            model: The vision-language model to use
            system_message: System message to set assistant's behavior
        """
        if model not in self.AVAILABLE_MODELS:
            raise ValueError(f"Invalid model: {model}. Available models: {', '.join(sorted(self.AVAILABLE_MODELS))}")
        
        self.model = model
        self.headers = DEFAULT_HEADERS.copy()
        self.system_message = system_message

    @staticmethod
    def encode_image(image_path: str) -> str:
        """Convert an image file to base64 encoding.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Base64 encoded string of the image
            
        Raises:
            IOError: If image file cannot be read
        """
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode("utf-8")
        except IOError as e:
            raise IOError(f"Failed to read image file: {str(e)}")

    def chat(
        self,
        messages: List[Dict[str, Union[str, List[Dict[str, Union[str, str]]]]]],
        stream: bool = False,
        temperature: float = 0.7,
        max_tokens: int = 8028,
    ) -> Union[str, Generator[str, None, None]]:
        """Chat about images with the model.
        
        Args:
            messages: List of messages including text and image content
            stream: Whether to stream the response
            temperature: Controls response randomness (0-1)
            max_tokens: Maximum tokens to generate
            
        Returns:
            Either complete response text or streaming generator
            
        Example:
            ```python
            vlm = DeepInfraVLM()
            messages = [{
                "role": "user",
                "content": [
                    {
                        "type": "image",
                        "image_url": {"url": "data:image/jpeg;base64," + vlm.encode_image("image.jpg")}
                    },
                    {
                        "type": "text",
                        "text": "What's in this image?"
                    }
                ]
            }]
            response = vlm.chat(messages)
            print(response)
            ```
        """
        # Prepare the payload
        payload = {
            "model": self.model,
            "messages": [{"role": "system", "content": self.system_message}] + messages,
            "stream": True,  # Always use streaming internally
            "temperature": temperature,
            "max_tokens": max_tokens
        }

        def process_stream():
            response = requests.post(
                self.chat_api_endpoint,
                headers=self.headers,
                json=payload,
                stream=True
            )
            response.raise_for_status()
            
            full_text = ""
            for line in response.iter_lines(decode_unicode=True):
                if line and line.startswith("data: "):
                    if line.strip() == "data: [DONE]":
                        break
                    try:
                        chunk_data = json.loads(line[6:])  # Skip "data: " prefix
                        if 'choices' in chunk_data and chunk_data['choices']:
                            choice = chunk_data['choices'][0]
                            if 'delta' in choice and 'content' in choice['delta']:
                                content = choice['delta']['content']
                                full_text += content
                                yield content
                    except json.JSONDecodeError:
                        continue
            return full_text

        if stream:
            # For streaming, return the generator directly
            return process_stream()
        else:
            # For non-streaming, collect all chunks and return the complete text
            full_text = ""
            for chunk in process_stream():
                full_text += chunk
            return full_text


if __name__ == "__main__":
    # Example usage with a system message
    
    # Test text-only model
    print("Testing text-only model:")
    model = "Qwen/Qwen2.5-72B-Instruct"
    messages = [
        {'role': 'system', 'content': 'You are a helpful AI assistant.'},
        {'role': 'user', 'content': 'Write a short poem about AI.'}
    ]
    
    print("\nAvailable text models:", ", ".join(sorted(DeepInfraAI.get_available_models())))
    print("Streaming response:")
    for chunk in DeepInfraAI.generate_text(model, messages, stream=True):
        print(chunk, end='', flush=True)
    
    # Test vision-language model
    print("\n\nTesting vision-language model:")
    vlm = DeepInfraVLM()
    print("\nAvailable VLM models:", ", ".join(sorted(vlm.AVAILABLE_MODELS)))
    
    # Example of how to use VLM (commented out as it requires an actual image)
    """
    messages = [{
        "role": "user",
        "content": [
            {
                "type": "image",
                "image_url": {"url": "data:image/jpeg;base64," + vlm.encode_image("path/to/image.jpg")}
            },
            {
                "type": "text",
                "text": "What's in this image?"
            }
        ]
    }]
    
    print("\nStreaming response:")
    for chunk in vlm.chat(messages, stream=True):
        print(chunk, end='', flush=True)
    """