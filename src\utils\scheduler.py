"""
Task scheduler for handling reminders and periodic tasks.
"""
import asyncio
from datetime import datetime, timedelta
import logging
from typing import Dict, Any, Optional, Callable
from telegram.ext import Application
from ..database.db import Database

logger = logging.getLogger(__name__)

class TaskScheduler:
    def __init__(self, application: Application, db: Database):
        self.application = application
        self.db = db
        self.running = False
        self.check_interval = 60  # Check every minute

    async def start(self):
        """Start the scheduler"""
        self.running = True
        asyncio.create_task(self._scheduler_loop())
        logger.info("Task scheduler started")

    async def stop(self):
        """Stop the scheduler"""
        self.running = False
        logger.info("Task scheduler stopped")

    async def _scheduler_loop(self):
        """Main scheduler loop"""
        while self.running:
            try:
                await self._check_reminders()
                await self._run_periodic_tasks()
                await asyncio.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"Error in scheduler loop: {e}")
                await asyncio.sleep(self.check_interval)

    async def _check_reminders(self):
        """Check and process due reminders"""
        try:
            due_reminders = await self.db.get_due_reminders()
            for reminder in due_reminders:
                try:
                    await self.application.bot.send_message(
                        chat_id=reminder['user_id'],
                        text=f"⏰ Reminder: {reminder['message']}"
                    )
                    # Mark reminder as completed in database
                    # (You'll need to add this method to LocalDatabase)
                    await self.db.mark_reminder_completed(reminder['id'])
                except Exception as e:
                    logger.error(f"Error processing reminder {reminder['id']}: {e}")
        except Exception as e:
            logger.error(f"Error checking reminders: {e}")

    async def _run_periodic_tasks(self):
        """Run periodic maintenance tasks"""
        current_hour = datetime.now().hour
        
        # Daily tasks at midnight
        if current_hour == 0:
            await self._daily_maintenance()
        
        # Hourly tasks
        await self._hourly_maintenance()

    async def _daily_maintenance(self):
        """Run daily maintenance tasks"""
        try:
            # Clean up old reminders
            await self._cleanup_old_reminders()
            
            # Generate and store usage statistics
            await self._generate_daily_stats()
            
            # Backup database (if implemented)
            await self._backup_database()
            
            logger.info("Daily maintenance tasks completed")
        except Exception as e:
            logger.error(f"Error in daily maintenance: {e}")

    async def _hourly_maintenance(self):
        """Run hourly maintenance tasks"""
        try:
            # Update active user status
            await self._update_active_users()
            
            # Check system health
            await self._check_system_health()
            
            logger.info("Hourly maintenance tasks completed")
        except Exception as e:
            logger.error(f"Error in hourly maintenance: {e}")

    async def _cleanup_old_reminders(self):
        """Clean up old completed reminders"""
        try:
            # Add cleanup logic here
            # For example, delete reminders older than 30 days
            thirty_days_ago = datetime.now() - timedelta(days=30)
            # You'll need to add this method to LocalDatabase
            await self.db.delete_old_reminders(thirty_days_ago)
        except Exception as e:
            logger.error(f"Error cleaning up old reminders: {e}")

    async def _generate_daily_stats(self):
        """Generate and store daily usage statistics"""
        try:
            # Add statistics generation logic here
            # For example, calculate daily command usage
            # You'll need to add these methods to LocalDatabase
            stats = await self.db.get_daily_stats()
            await self.db.store_daily_stats(stats)
        except Exception as e:
            logger.error(f"Error generating daily stats: {e}")

    async def _backup_database(self):
        """Backup the database"""
        try:
            # Add database backup logic here
            # For example, create a timestamped copy of the database file
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            # You'll need to add this method to LocalDatabase
            await self.db.create_backup(f"backup_{timestamp}.db")
        except Exception as e:
            logger.error(f"Error backing up database: {e}")

    async def _update_active_users(self):
        """Update active user status"""
        try:
            # Add active user update logic here
            # For example, mark users as inactive if not seen in 24 hours
            one_day_ago = datetime.now() - timedelta(days=1)
            # You'll need to add this method to LocalDatabase
            await self.db.update_inactive_users(one_day_ago)
        except Exception as e:
            logger.error(f"Error updating active users: {e}")

    async def _check_system_health(self):
        """Check system health metrics"""
        try:
            # Add system health check logic here
            # For example, check database connection, API availability, etc.
            # You'll need to add these methods to LocalDatabase
            db_health = await self.db.check_health()
            if not db_health['healthy']:
                logger.warning(f"Database health check failed: {db_health['message']}")
        except Exception as e:
            logger.error(f"Error checking system health: {e}")

    def add_periodic_task(self, interval: int, task: Callable, name: str):
        """Add a new periodic task to the scheduler"""
        async def wrapped_task():
            while self.running:
                try:
                    await task()
                except Exception as e:
                    logger.error(f"Error in periodic task {name}: {e}")
                await asyncio.sleep(interval)
        
        asyncio.create_task(wrapped_task())
        logger.info(f"Added periodic task: {name}")

    def schedule_task(self, when: datetime, task: Callable, name: str):
        """Schedule a one-time task"""
        async def scheduled_task():
            now = datetime.now()
            if when > now:
                delay = (when - now).total_seconds()
                await asyncio.sleep(delay)
                try:
                    await task()
                except Exception as e:
                    logger.error(f"Error in scheduled task {name}: {e}")
        
        asyncio.create_task(scheduled_task())
        logger.info(f"Scheduled task: {name} for {when}")
