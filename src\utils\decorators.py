"""
🎯 Decorator Module
---------------
Command decorators for permission checking and error handling.
"""

from functools import wraps
from telegram import Update
from telegram.ext import ContextTypes
import sqlite3
import logging

logger = logging.getLogger(__name__)

def admin_only():
    """Decorator for admin-only commands"""
    def decorator(func):
        @wraps(func)
        async def wrapped(*args, **kwargs):
            # Handle both class methods and standalone functions
            if len(args) >= 2 and isinstance(args[1], Update):
                # Class method: (self, update, context, ...)
                self = args[0]
                update = args[1]
                context = args[2] if len(args) > 2 else kwargs.get('context')
                other_args = args[3:]
            else:
                # Standalone function: (update, context, ...)
                self = None
                update = args[0]
                context = args[1] if len(args) > 1 else kwargs.get('context')
                other_args = args[2:]
            
            if not update or not update.effective_user or not update.effective_chat:
                await update.message.reply_text(
                    "❌ Could not identify chat or user."
                )
                return
                
            user_id = update.effective_user.id
            chat_id = update.effective_chat.id
            
            try:
                user = await context.bot.get_chat_member(chat_id, user_id)
                if user.status not in ('administrator', 'creator'):
                    await update.message.reply_text(
                        "⛔ This command is for administrators only!"
                    )
                    return
                
                if self:
                    return await func(self, update, context, *other_args, **kwargs)
                else:
                    return await func(update, context, *other_args, **kwargs)
            except Exception as e:
                await update.message.reply_text(
                    f"❌ An error occurred while checking permissions: {str(e)}"
                )
                return
        return wrapped
    return decorator

def owner_only():
    """Decorator for bot owner only commands"""
    def decorator(func):
        @wraps(func)
        async def wrapped(update: Update, context: ContextTypes.DEFAULT_TYPE, *args, **kwargs):
            user_id = update.effective_user.id
            owner_id = context.bot_data.get('owner_id')
            
            if user_id != owner_id:
                await update.message.reply_text(
                    "⛔ This command is for the bot owner only!"
                )
                return
            return await func(update, context, *args, **kwargs)
        return wrapped
    return decorator

def group_only():
    """Decorator for group-only commands"""
    def decorator(func):
        @wraps(func)
        async def wrapped(*args, **kwargs):
            # Handle both class methods and standalone functions
            if len(args) >= 2 and isinstance(args[1], Update):
                # Class method: (self, update, context, ...)
                self = args[0]
                update = args[1]
                context = args[2] if len(args) > 2 else kwargs.get('context')
                other_args = args[3:]
            else:
                # Standalone function: (update, context, ...)
                self = None
                update = args[0]
                context = args[1] if len(args) > 1 else kwargs.get('context')
                other_args = args[2:]
            
            if not update or not update.effective_chat:
                await update.message.reply_text(
                    "❌ Could not identify chat."
                )
                return
                
            if update.effective_chat.type not in ('group', 'supergroup'):
                await update.message.reply_text(
                    "⛔ This command can only be used in groups!"
                )
                return
            
            if self:
                return await func(self, update, context, *other_args, **kwargs)
            else:
                return await func(update, context, *other_args, **kwargs)
        return wrapped
    return decorator

def private_only():
    """Decorator for private-only commands"""
    def decorator(func):
        @wraps(func)
        async def wrapped(update: Update, context: ContextTypes.DEFAULT_TYPE, *args, **kwargs):
            if update.effective_chat.type != 'private':
                await update.message.reply_text(
                    "⚠️ This command can only be used in private chat!"
                )
                return
            return await func(update, context, *args, **kwargs)
        return wrapped
    return decorator

def log_command():
    """Decorator to log command usage"""
    def decorator(func):
        @wraps(func)
        async def wrapped(update: Update, context: ContextTypes.DEFAULT_TYPE, *args, **kwargs):
            user = update.effective_user
            command = update.message.text
            chat = update.effective_chat
            
            print(f"📝 Command: {command}")
            print(f"👤 User: {user.first_name} ({user.id})")
            print(f"💭 Chat: {chat.title if chat.title else 'Private'} ({chat.id})")
            
            return await func(update, context, *args, **kwargs)
        return wrapped
    return decorator

def log_command_usage(db=None):
    """Decorator to log command usage for statistics"""
    def decorator(func):
        @wraps(func)
        async def wrapped(update: Update, context: ContextTypes.DEFAULT_TYPE, *args, **kwargs):
            if update.effective_message and update.effective_message.text and update.effective_message.text.startswith('/'):
                # Extract command name without arguments
                command = update.effective_message.text.split()[0].split('@')[0][1:]
                
                # Log command usage if database is available
                if db:
                    try:
                        user_id = update.effective_user.id if update.effective_user else None
                        chat_id = update.effective_chat.id if update.effective_chat else None
                        chat_type = update.effective_chat.type if update.effective_chat else None
                        
                        # Use the new log_command method
                        await db.log_command(command, user_id, chat_id, chat_type)
                    except Exception as e:
                        logger.error(f"Error logging command usage: {e}")
            
            return await func(update, context, *args, **kwargs)
        return wrapped
    return decorator
