import React from 'react';
import { Box, Typography, But<PERSON>, Container, Grid } from '@mui/material';
import { styled } from '@mui/material/styles';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import ChatIcon from '@mui/icons-material/Chat';
import AnalyticsIcon from '@mui/icons-material/Analytics';

const HeroContainer = styled(Box)(({ theme }) => ({
  background: 'linear-gradient(135deg, #fafbff 0%, #f1f5f9 100%)',
  position: 'relative',
  overflow: 'hidden',
  padding: theme.spacing(8, 0),
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      radial-gradient(circle at 30% 20%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 70% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%)
    `,
    pointerEvents: 'none',
  },
}));

const <PERSON>rad<PERSON><PERSON><PERSON><PERSON> = styled(But<PERSON>)(({ theme }) => ({
  background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
  color: 'white',
  padding: theme.spacing(1.5, 4),
  fontSize: '1.1rem',
  fontWeight: 600,
  borderRadius: 12,
  textTransform: 'none',
  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  '&:hover': {
    background: 'linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%)',
    boxShadow: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    transform: 'translateY(-2px)',
  },
  transition: 'all 0.3s ease',
}));

const SecondaryButton = styled(Button)(({ theme }) => ({
  color: theme.palette.primary.main,
  borderColor: theme.palette.primary.main,
  padding: theme.spacing(1.5, 4),
  fontSize: '1.1rem',
  fontWeight: 600,
  borderRadius: 12,
  textTransform: 'none',
  '&:hover': {
    backgroundColor: theme.palette.primary.main,
    color: 'white',
    transform: 'translateY(-2px)',
  },
  transition: 'all 0.3s ease',
}));

const HeroSection = ({ onChatClick, onAnalyticsClick }) => {
  return (
    <HeroContainer>
      <Container maxWidth="lg">
        <Box sx={{ position: 'relative', zIndex: 1 }}>
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12} md={8}>
              <Box sx={{ mb: 4 }}>
                <Typography
                  variant="h1"
                  sx={{
                    background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text',
                    mb: 2,
                    fontWeight: 800,
                  }}
                >
                  HelpingAI Bot Dashboard
                </Typography>
                <Typography
                  variant="h3"
                  sx={{
                    color: 'text.primary',
                    mb: 3,
                    fontWeight: 600,
                    lineHeight: 1.3,
                  }}
                >
                  Redefining How AI Thinks and Feels
                </Typography>
                <Typography
                  variant="h6"
                  sx={{
                    color: 'text.secondary',
                    mb: 4,
                    maxWidth: '600px',
                    lineHeight: 1.6,
                  }}
                >
                  We build AI that doesn't just answer — it reasons, listens, and understands you. 
                  Monitor and manage your intelligent bot with advanced analytics and emotional intelligence.
                </Typography>
              </Box>
              
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <GradientButton
                  startIcon={<ChatIcon />}
                  onClick={onChatClick}
                  size="large"
                >
                  Chat with Bot
                </GradientButton>
                <SecondaryButton
                  variant="outlined"
                  startIcon={<AnalyticsIcon />}
                  onClick={onAnalyticsClick}
                  size="large"
                >
                  View Analytics
                </SecondaryButton>
              </Box>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '300px',
                }}
              >
                <Box
                  sx={{
                    width: 200,
                    height: 200,
                    borderRadius: '50%',
                    background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
                    animation: 'fadeIn 1s ease-out',
                  }}
                >
                  <SmartToyIcon sx={{ fontSize: 80, color: 'white' }} />
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </Container>
    </HeroContainer>
  );
};

export default HeroSection;
