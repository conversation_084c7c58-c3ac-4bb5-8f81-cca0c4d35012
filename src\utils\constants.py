"""
🛠️ Constants Module
---------------
Contains constant values and emoji definitions used throughout the bot.
Powered by NexusAI
"""

# Emoji constants for bot responses
EMOJI = {
    'check': '✅',
    'error': '❌',
    'warning': '⚠️',
    'ban': '🚫',
    'mute': '🤐',
    'kick': '👢',
    'pin': '📌',
    'clean': '🧹',
    'announcement': '📢',
    'settings': '⚙️',
    'help': '❓',
    'info': 'ℹ️',
    'time': '⏰',
    'shield': '🛡️',
    'night': '🌙',
    'sun': '☀️',
    'spam': '🚯',
    'filter': '🔍',
    'lock': '🔒',
    'unlock': '🔓',
    'rules': '📜',
    'notes': '📝',
    'stats': '📊',
    'admin': '👮‍♂️',
    'user': '👤',
    'group': '👥',
    'bot': '🤖',
    'success': '✅',
    'fail': '❌',
    'alert': '🚨',
    'delete': '🗑️',
    'edit': '✏️',
    'save': '💾',
    'link': '🔗',
    'star': '⭐',
    'heart': '❤️',
    'mail': '📧',
    'clock': '🕒',
    'calendar': '📅',
    'tag': '🏷️',
    'folder': '📁',
    'file': '📄',
    'image': '🖼️',
    'video': '🎥',
    'audio': '🎵',
    'voice': '🎤',
    'sticker': '🎨',
    'animation': '🎞️',
    'location': '📍',
    'contact': '📱',
    'poll': '📊',
    'game': '🎮',
    'dice': '🎲',
    'loading': '⏳',
    'done': '✅',
    'error_circle': '⭕',
    'warning_triangle': '⚠️',
    'info_circle': 'ℹ️',
    'question': '❓',
    'exclamation': '❗',
    'plus': '➕',
    'minus': '➖',
    'multiply': '✖️',
    'divide': '➗',
    'equals': '=',
    'back': '⬅️',
    'next': '➡️',
    'up': '⬆️',
    'down': '⬇️',
    'refresh': '🔄',
    'search': '🔍',
    'key': '🔑',
    'gear': '⚙️',
    'tools': '🛠️',
    'wrench': '🔧',
    'hammer': '🔨',
    'chain': '⛓️',
    'globe': '🌐',
    'world': '🗺️',
    'chart': '📈',
    'graph': '📊',
    'trophy': '🏆',
    'medal': '🎖️',
    'crown': '👑',
    'fire': '🔥',
    'sparkles': '✨',
    'zap': '⚡',
    'rainbow': '🌈',
    'cloud': '☁️',
    'umbrella': '☔',
    'snowflake': '❄️',
    'clover': '🍀',
    'spades': '♠️',
    'hearts': '♥️',
    'diamonds': '♦️',
    'clubs': '♣️',
    # Fun emojis
    'fun': '🎮',
    'coin': '🪙',
    'love': '💝',
    'hug': '🤗',
    'roast': '🔥',
    'joke': '😄',
    'fortune': '🥠',
    'magic': '✨',
    'party': '🎉',
    'cool': '😎',
    'think': '🤔',
    'laugh': '😂',
    'wink': '😉',
    'robot': '🤖',
    'brain': '🧠',
    'crystal_ball': '🔮',
    'gift': '🎁'
}

# Time constants
SECONDS_IN_MINUTE = 60
SECONDS_IN_HOUR = 3600
SECONDS_IN_DAY = 86400

# Default values
DEFAULT_BAN_DURATION = SECONDS_IN_DAY  # 24 hours
DEFAULT_MUTE_DURATION = SECONDS_IN_HOUR * 12  # 12 hours
DEFAULT_WARN_LIMIT = 3
DEFAULT_SPAM_WINDOW = SECONDS_IN_MINUTE * 5  # 5 minutes
DEFAULT_SPAM_LIMIT = 5  # messages
DEFAULT_RAID_THRESHOLD = 5  # users
DEFAULT_RAID_TIME = SECONDS_IN_MINUTE  # 1 minute

# Command prefixes
COMMAND_PREFIXES = ['/', '!', '.']

# Permission levels
PERMISSION_LEVELS = {
    'owner': 100,
    'admin': 50,
    'moderator': 30,
    'trusted': 20,
    'user': 10,
    'banned': 0
}

# Message types
MESSAGE_TYPES = {
    'text': 'text',
    'photo': 'photo',
    'video': 'video',
    'document': 'document',
    'audio': 'audio',
    'voice': 'voice',
    'sticker': 'sticker',
    'animation': 'animation',
    'location': 'location',
    'contact': 'contact',
    'poll': 'poll'
}

# Database collections
DB_COLLECTIONS = {
    'settings': 'settings',
    'users': 'users',
    'warns': 'warns',
    'notes': 'notes',
    'filters': 'filters',
    'admin_actions': 'admin_actions',
    'spam_tracking': 'spam_tracking',
    'raid_tracking': 'raid_tracking'
}

# Cache timeouts (in seconds)
CACHE_TIMEOUTS = {
    'settings': 300,  # 5 minutes
    'user_info': 600,  # 10 minutes
    'admin_list': 900,  # 15 minutes
    'banned_list': 1800  # 30 minutes
}

# Bot information
BOT_INFO = {
    'name': 'NexusAI',
    'company': 'HelpingAI (formerly NexusAI)',
    'version': '1.0.0',
    'description': 'Advanced Telegram Group Management Bot with AI-powered features',
    'website': '',
    'github': '',
    'support': '',
    'channel': '',
    'powered_by': 'Powered by NexusAI 🤖'
}

# Command categories
COMMAND_CATEGORIES = {
    'admin': '👮‍♂️ Admin Commands',
    'fun': '🎮 Fun Commands',
    'util': '🛠️ Utility Commands',
    'info': 'ℹ️ Information',
    'settings': '⚙️ Settings'
}

# Fun command descriptions
FUN_COMMANDS = {
    'dice': '🎲 Roll a dice',
    'coin': '🪙 Flip a coin',
    'hug': '🤗 Send a virtual hug',
    'joke': '😄 Tell a joke',
    'fortune': '🥠 Get your fortune',
    'roast': '🔥 Generate a playful roast'
}

# Error messages
ERROR_MESSAGES = {
    'api_error': '❌ API Error: {}',
    'cooldown': '⏳ Please wait {} before using this command again.',
    'permission_denied': '🚫 You do not have permission to use this command.',
    'invalid_format': '❌ Invalid command format. {}',
    'not_found': '❌ {} not found.',
    'missing_args': '❌ Missing required arguments: {}',
    'internal_error': '❌ An internal error occurred. Please try again later.',
    'feature_disabled': '❌ This feature is currently disabled.',
    'bot_blocked': '❌ The bot has been blocked by the user.',
    'user_not_found': '❌ User not found.',
    'chat_not_found': '❌ Chat not found.',
    'message_too_long': '❌ Message is too long.',
    'unknown_error': '❌ An unknown error occurred.'
}

# Success messages
SUCCESS_MESSAGES = {
    'command_success': '✅ Command executed successfully.',
    'settings_updated': '✅ Settings have been updated.',
    'feature_enabled': '✅ Feature has been enabled.',
    'feature_disabled': '✅ Feature has been disabled.',
    'user_banned': '🚫 User has been banned.',
    'user_unbanned': '✅ User has been unbanned.',
    'user_muted': '🤐 User has been muted.',
    'user_unmuted': '🔊 User has been unmuted.',
    'message_pinned': '📌 Message has been pinned.',
    'message_unpinned': '✅ Message has been unpinned.',
    'rules_updated': '📜 Rules have been updated.',
    'notes_saved': '📝 Note has been saved.'
}
