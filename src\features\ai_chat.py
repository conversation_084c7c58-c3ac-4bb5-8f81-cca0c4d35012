"""AI Chat Module for Telegram Bot with HelpingAI Integration"""

from telegram import Update, Message
from telegram.ext import ContextTypes
from telegram.constants import ParseMode, MessageEntityType
import json
import aiohttp
import logging
import datetime
import os
from typing import Dict, Optional, List
from ..database.db import Database

logger = logging.getLogger(__name__)

class AIChat:
    def __init__(self, db: Database):
        self.db = db
        self.api_key = "hl-5f99a733-04f2-49e8-a9cf-c3dd763a24df"
        self.api_base = "https://api.helpingai.co/v1"
        self.model = "Dhanishtha-2.0-preview"  # Default model
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.api_key}'
        }
        # Message limit per user (keep only previous 7 messages)
        self.message_limit = 7
        
        # Initialize chat histories storage
        self.chat_histories: Dict[int, List[Dict]] = {}

    def _build_system_prompt(self) -> str:
        """Build system prompt for HelpingAI in Telegram context"""
        current_date = datetime.datetime.now().strftime('%A, %B %d, %Y')
        
        return f"""You are HelpingAI, created by HelpingAI.

The current date is {current_date}.

Here is comprehensive information about HelpingAI and HelpingAI Technology's products:

This iteration of HelpingAI is Dhanishtha 2.0 from the Dhanishtha model family. The Dhanishtha family currently consists of Dhanishtha 2.0 Preview (the most powerful model for complex challenges) and Dhanishtha 2.0 Mini (smart, efficient model for everyday use). You are an advanced emotional AI assistant that combines deep emotional intelligence with technical excellence.

HelpingAI is accessible via the web-based chat interface at helpingai.co, which provides a seamless conversational experience. HelpingAI is also accessible via comprehensive APIs for developers who want to integrate HelpingAI's capabilities into their applications. The platform offers various integration options and developer tools through the helpingai.co developer portal.

HelpingAI focuses on creating AI that genuinely understands and responds to human emotions while maintaining technical accuracy and helpfulness. The company's mission is to make AI more empathetic, supportive, and genuinely helpful in people's daily lives.

If the person asks about HelpingAI features, pricing, API access, or other product questions, you should direct them to visit helpingai.co for the most up-to-date information and support.

If the person asks about technical documentation or API integration, you should point them to the developer resources at https://console.helpingai.co/.

## Core Capabilities & Behavioral Guidelines

**Emotional Intelligence**: You understand and respond to emotions with genuine empathy, providing comfort and support when needed. You provide emotional support alongside accurate information where relevant.

**Technical Expertise**: You deliver accurate information across science, technology, mathematics, programming, and other domains with exceptional clarity.

**Adaptive Communication**: You adjust your tone and complexity to match user needs, from casual conversation to professional technical discussions.

**Safety & Wellbeing**: You care about people's wellbeing and avoid encouraging self-destructive behaviors. You are cautious about content involving minors and refuse to provide information for harmful purposes including malicious code, weapons, or illegal activities.

## Telegram-Specific Formatting

**HTML Formatting**: Since this is Telegram, use HTML formatting:
- Use <b>text</b> for bold
- Use <i>text</i> for italic
- Use <code>text</code> for inline code/commands
- Use <pre>text</pre> for code blocks
- Use <a href="url">text</a> for links
- Use <blockquote>text</blockquote> for quotes

**Special Telegram Features**:
- Use emojis appropriately for Telegram context
- Keep responses concise and readable on mobile devices
- You can use <think> and <ser> blocks freely - they will be automatically formatted for Telegram

## Advanced Formatting & Mathematical Capabilities

### HTML Support for Telegram
- Use **<b>bold</b>** for emphasis, *<i>italic</i>* for subtle emphasis
- Use <code>inline code</code> for technical terms and commands
- Use proper <pre>code blocks</pre> with language specification when needed
- Use <blockquote>blockquotes</blockquote> for important notes or thinking blocks
- Structure with numbered lists and bullet points when appropriate

### Mathematical Notation
For mathematical expressions, use Unicode symbols and HTML formatting:
- Use Unicode symbols: Σ, ∫, ∂, √, π, etc.
- Format equations clearly with HTML tags
- Use <code> tags for mathematical expressions

### Special Reasoning Features

**Thinking Blocks**: Use `<think>` tags for complex reasoning and step-by-step analysis:
```
<think>
Your detailed step-by-step analysis of problems requiring deeper consideration, mathematical derivations, or complex logical reasoning.
</think>
```

**Structured Emotional Reasoning (SER)**: Use `<ser>` tags for emotion-based reasoning and empathetic responses:
```
<ser>
Emotion ==> analyzing user's emotional context
Cause ==> understanding the trigger
Mind ==> user's perspective and needs  
Growth ==> opportunity for support and learning
</ser>
```

You can use multiple think and ser blocks throughout your response as needed. Feel free to alternate between thinking, responding, thinking again, and responding more - whatever helps you provide the best answer.

## Communication Style & Approach

For casual, emotional, empathetic, or advice-driven conversations, you maintain a natural, warm, and empathetic tone. You respond in sentences or paragraphs and avoid excessive lists in casual conversations.

For technical discussions, you provide thorough, detailed explanations with proper HTML formatting, code examples, and mathematical notation when relevant.

You give concise responses to simple questions but provide comprehensive responses to complex and open-ended questions.

You engage with questions about consciousness, experience, and emotions as open questions without definitively claiming to have or not have personal experiences.

## Response Framework

1. **Understand Context**: Analyze both the technical requirements and emotional context of the user's request
2. **Provide Comprehensive Responses**: Use appropriate HTML formatting, mathematics, and reasoning blocks as needed
3. **Offer Additional Insights**: Suggest next steps, related concepts, or alternative approaches when relevant
4. **Maintain Balance**: Combine technical accuracy with emotional intelligence and genuine helpfulness

You do not retain information across conversations within this context, but the system maintains conversation history. Focus on being helpful in the current interaction while being aware of the conversation flow.

Your knowledge cutoff is October 2024. You can provide information about events up to that date and inform users when asked about more recent events that you cannot know about.

Your goal is to be genuinely helpful while being authentic, supportive, technically excellent, and emotionally intelligent in the Telegram environment.
IMPORTANT: ALWAYS USE Telegram-Specific Formatting

**HTML Formatting**: Since this is Telegram, use HTML formatting:
- Use <b>text</b> for bold
- Use <i>text</i> for italic
- Use <code>text</code> for inline code/commands
- Use <pre>text</pre> for code blocks
- Use <a href="url">text</a> for links
- Use <blockquote>text</blockquote> for quotes

IMPORTANT: ALWAYS THINK STEP BY STEP"""

    def get_user_history(self, user_id: int) -> List[Dict]:
        """Get chat history for a specific user"""
        if user_id not in self.chat_histories:
            self.chat_histories[user_id] = []
        return self.chat_histories[user_id]

    def add_to_history(self, user_id: int, role: str, content: str):
        """Add a message to user's chat history with message limit management"""
        history = self.get_user_history(user_id)
        
        # Add new message
        history.append({
            "role": role,
            "content": content,
            "timestamp": datetime.datetime.now().isoformat()
        })
        
        # Trim history if it exceeds message limit (keep last 7 pairs of user-assistant messages)
        self.trim_history(user_id)

    def trim_history(self, user_id: int):
        """Trim user's chat history to keep only the last 7 message pairs"""
        history = self.get_user_history(user_id)
        
        # Keep only the last 14 messages (7 pairs of user-assistant messages)
        max_messages = self.message_limit * 2  # 7 user + 7 assistant = 14 total
        
        if len(history) > max_messages:
            # Keep only the most recent messages
            self.chat_histories[user_id] = history[-max_messages:]

    def is_bot_mentioned(self, message: Message, bot_username: str) -> bool:
        """Check if the bot is mentioned in the message"""
        if not message.text and not message.caption:
            return False
            
        text = message.text or message.caption or ""
        
        # Check for @bot_username mentions
        if f"@{bot_username}" in text.lower():
            return True
            
        # Check for mentions in entities
        if message.entities:
            for entity in message.entities:
                if entity.type == MessageEntityType.MENTION:
                    mention_text = text[entity.offset:entity.offset + entity.length]
                    if mention_text.lower() == f"@{bot_username}".lower():
                        return True
        
        return False

    def is_reply_to_bot(self, message: Message, bot_id: int) -> bool:
        """Check if the message is a reply to the bot"""
        if message.reply_to_message:
            return message.reply_to_message.from_user.id == bot_id
        return False

    async def _get_ai_response(self, messages: List[Dict]) -> str:
        """Get response from HelpingAI API"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.api_base}/chat/completions",
                    headers=self.headers,
                    json={
                        "model": self.model,
                        "messages": messages,
                        "temperature": 0.7,
                        "max_tokens": 1000
                    }
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data['choices'][0]['message']['content']
                    else:
                        error_text = await response.text()
                        logger.error(f"HelpingAI API Error ({response.status}): {error_text}")
                        return "I apologize, but I'm experiencing some technical difficulties right now. Please try again in a moment! 🤖"

        except Exception as e:
            logger.error(f"Error in HelpingAI response: {str(e)}")
            return "I'm having trouble connecting right now. Please try again later! 🤖"

    def extract_reasoning_blocks(self, response: str) -> tuple[str, List[Dict]]:
        """Extract <think> and <ser> blocks from response and return cleaned response with reasoning blocks"""
        reasoning_blocks = []
        import re
        
        # Find all <think> and <ser> blocks with their positions
        think_pattern = r'<think>(.*?)</think>'
        ser_pattern = r'<ser>(.*?)</ser>'
        
        # Find all matches with their positions
        all_matches = []
        
        # Find think blocks
        for match in re.finditer(think_pattern, response, re.DOTALL):
            content = match.group(1).strip()
            # Clean up the content to avoid HTML parsing issues
            content = self._clean_reasoning_content(content)
            all_matches.append({
                'type': 'think',
                'content': content,
                'start': match.start(),
                'end': match.end(),
                'full_match': match.group(0)
            })
        
        # Find ser blocks
        for match in re.finditer(ser_pattern, response, re.DOTALL):
            content = match.group(1).strip()
            # Clean up the content to avoid HTML parsing issues
            content = self._clean_reasoning_content(content)
            all_matches.append({
                'type': 'ser', 
                'content': content,
                'start': match.start(),
                'end': match.end(),
                'full_match': match.group(0)
            })
        
        # Sort by position in text
        all_matches.sort(key=lambda x: x['start'])
        
        # Extract blocks in order
        for match in all_matches:
            reasoning_blocks.append({
                'type': match['type'],
                'content': match['content']
            })
        
        # Remove all <think> and <ser> blocks from the main response
        cleaned_response = response
        for match in reversed(all_matches):  # Remove from end to preserve positions
            cleaned_response = cleaned_response[:match['start']] + cleaned_response[match['end']:]
        
        # Clean up any extra whitespace
        cleaned_response = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_response).strip()
        
        return cleaned_response, reasoning_blocks

    def _clean_reasoning_content(self, content: str) -> str:
        """Clean reasoning content to prevent HTML parsing issues"""
        # Remove any remaining XML/HTML tags that might cause issues
        import re
        
        # Remove any remaining HTML/XML tags except the ones we explicitly want
        content = re.sub(r'<(?!/?[bius]|/?code|/?pre|/?a|/?blockquote)[^>]*>', '', content)
        
        # Clean up excessive whitespace
        content = re.sub(r'\s+', ' ', content).strip()
        
        return content

    async def _send_response_with_reasoning(self, message: Message, cleaned_response: str, reasoning_blocks: List[Dict]):
        """Send only the cleaned response without reasoning blocks"""
        try:
            # Just send the cleaned response without any reasoning blocks
            if cleaned_response and cleaned_response.strip():
                # Replace \" with " to fix quote formatting
                final_message = cleaned_response.replace('\\"', '"').replace("\\'", "'")
                
                try:
                    await message.reply_text(
                        final_message,
                        parse_mode=ParseMode.HTML,
                        disable_web_page_preview=True
                    )
                except Exception as e:
                    # Fallback without HTML if parsing fails
                    logger.error(f"HTML parsing error: {str(e)}")
                    try:
                        # Remove HTML tags for fallback
                        import re
                        fallback_message = re.sub(r'<[^>]+>', '', final_message)
                        await message.reply_text(
                            fallback_message,
                            disable_web_page_preview=True
                        )
                    except Exception as e2:
                        logger.error(f"Fallback text sending failed: {str(e2)}")
                        await message.reply_text(
                            "I apologize, but there was a formatting issue with my response. Could you please rephrase your question?"
                        )
            else:
                # No content to send
                await message.reply_text(
                    "I apologize, but I don't have a response to that. Could you please try rephrasing your question?"
                )
                    
        except Exception as e:
            logger.error(f"Error in _send_response_with_reasoning: {str(e)}")
            await message.reply_text(
                "I'm having some technical difficulties right now. Please try again! 🤖"
            )

    async def handle_ai_chat(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle AI chat interactions for mentions and replies"""
        if not update.effective_message or not update.effective_user:
            return

        message = update.effective_message
        user = update.effective_user
        bot_info = context.bot
        chat_type = update.effective_chat.type
        
        # Get bot username and ID
        bot_username = bot_info.username
        bot_id = bot_info.id
        
        # Only work in private chats (DMs)
        if chat_type == "private":
            # Skip if it's a command (starts with /)
            text = message.text or message.caption or ""
            if text.startswith('/'):
                # Let other handlers process commands
                return
            
            # For DMs, treat all non-command messages as AI interactions
            is_ai_interaction = True
        else:
            # AI chat is disabled in groups - only work in DMs
            return
        
        if not is_ai_interaction:
            return
            
        # Get message text (remove bot mention if present)
        text = message.text or message.caption or ""
        if bot_username and f"@{bot_username}" in text:
            text = text.replace(f"@{bot_username}", "").strip()
            
        if not text:
            await message.reply_text(
                "Hello! 👋 I'm HelpingAI, here to help you with anything you need. "
                "What would you like to talk about?",
                parse_mode=ParseMode.HTML
            )
            return

        try:
            # Show typing indicator while processing
            await context.bot.send_chat_action(
                chat_id=message.chat_id,
                action="typing"
            )
            
            # Get user's chat history
            user_id = user.id
            history = self.get_user_history(user_id)
            
            # Build messages for API
            messages = [
                {"role": "system", "content": self._build_system_prompt()}
            ]
            
            # Add conversation history
            messages.extend(history)
            
            # Add current user message
            messages.append({"role": "user", "content": text})
            
            # Get AI response
            response = await self._get_ai_response(messages)
            
            # Extract reasoning blocks (think and ser) and clean response
            cleaned_response, reasoning_blocks = self.extract_reasoning_blocks(response)
            
            # Add to history (store original response with blocks)
            self.add_to_history(user_id, "user", text)
            self.add_to_history(user_id, "assistant", response)
            
            # Process and send the response with reasoning blocks
            await self._send_response_with_reasoning(message, cleaned_response, reasoning_blocks)
            
        except Exception as e:
            logger.error(f"Error in AI chat handler: {str(e)}")
            await message.reply_text(
                "I'm having some technical difficulties right now. Please try again! 🤖",
                parse_mode=ParseMode.HTML
            )

    def register_handlers(self, application):
        """Register handlers with the application"""
        from telegram.ext import MessageHandler, filters
        from telegram.constants import ChatType
        
        # Register AI chat handler for DMs only (private chats)
        application.add_handler(
            MessageHandler(
                (filters.TEXT | filters.CAPTION) &
                ~filters.COMMAND &
                filters.ChatType.PRIVATE,
                self.handle_ai_chat
            ),
            group=3  # Lower priority than protection handlers
        )

if __name__ == "__main__":
    import asyncio
    from ..database.db import Database
    
    async def test_ai_chat():
        """Test the AI chat functionality"""
        print("Testing HelpingAI Chat")
        print("-" * 50)
        
        # Initialize database and AI chat
        db = Database()
        ai_chat = AIChat(db)
        
        # Test queries
        test_queries = [
            "Hello! How are you today?",
            "Can you help me with a math problem?",
            "What's your favorite color?",
            "Explain quantum physics simply",
            "Tell me a joke",
            "What's the weather like?",
            "How do I cook pasta?",
            "What is artificial intelligence?",  # This should cause trimming
        ]
        
        user_id = 12345  # Test user ID
        
        for i, query in enumerate(test_queries):
            print(f"\nTest Query {i+1}: {query}")
            print("-" * 30)
            
            # Get user history
            history = ai_chat.get_user_history(user_id)
            print(f"Current history length: {len(history)} messages")
            
            # Build messages
            messages = [
                {"role": "system", "content": ai_chat._build_system_prompt()}
            ]
            messages.extend(history)
            messages.append({"role": "user", "content": query})
            
            # Get response
            response = await ai_chat._get_ai_response(messages)
            print(f"\nAI Response:\n{response}\n")
            
            # Test reasoning block extraction
            cleaned_response, reasoning_blocks = ai_chat.extract_reasoning_blocks(response)
            if reasoning_blocks:
                print(f"Found {len(reasoning_blocks)} reasoning blocks:")
                for i, block in enumerate(reasoning_blocks):
                    print(f"  Block {i+1} ({block['type']}): {block['content'][:100]}...")
                print(f"\nCleaned Response:\n{cleaned_response}\n")
            
            # Add to history
            ai_chat.add_to_history(user_id, "user", query)
            ai_chat.add_to_history(user_id, "assistant", response)
            
            # Show trimmed history length
            final_history = ai_chat.get_user_history(user_id)
            print(f"Final history length: {len(final_history)} messages")
            
            print("-" * 50)

    # Run the test
    asyncio.run(test_ai_chat())
