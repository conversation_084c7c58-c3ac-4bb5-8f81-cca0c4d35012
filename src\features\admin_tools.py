"""
👑 Admin Tools Module
--------------------
Enhanced administrative tools and management features.
"""

import asyncio
import html
import logging
import random
from re import escape
import time
from datetime import datetime, timedelta, timezone
from telegram import ChatMemberAdministrator, Update, InlineKeyboardButton, InlineKeyboardMarkup, ChatPermissions, ChatMember, User, ChatAdministratorRights
from telegram.constants import ParseMode, ChatMemberStatus
from telegram.ext import CommandHandler, CallbackQueryHandler, ContextTypes
from telegram.error import TelegramError
from typing import Optional, List, Dict
from src.database.db import Database


class AdminTools:
    def __init__(self, db: Database):
        self.db = db
        self.action_cooldowns = {}

    async def _delete_after_delay(self, message, delay: int) -> None:
        """Delete a message after a specified delay in seconds."""
        if message:
            await asyncio.sleep(delay)
            try:
                await message.delete()
            except TelegramError as e:
                logging.debug(f"Failed to delete message: {e}")

    async def admin_commands(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Display comprehensive admin panel with Rose-bot style interface 👑"""
        if not await self._check_admin_rights(update, context):
            return

        # Create interactive admin panel with buttons
        keyboard = [
            [
                InlineKeyboardButton("👥 User Management", callback_data="admin_users"),
                InlineKeyboardButton("⚠️ Warning System", callback_data="admin_warnings")
            ],
            [
                InlineKeyboardButton("💬 Message Tools", callback_data="admin_messages"),
                InlineKeyboardButton("🛡️ Protection", callback_data="admin_protection")
            ],
            [
                InlineKeyboardButton("⚙️ Group Settings", callback_data="admin_settings"),
                InlineKeyboardButton("📊 Analytics", callback_data="admin_analytics")
            ],
            [
                InlineKeyboardButton("🎯 Quick Actions", callback_data="admin_quick"),
                InlineKeyboardButton("📚 Help Guide", callback_data="admin_help")
            ],
            [InlineKeyboardButton("🔄 Refresh Panel", callback_data="admin_refresh")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        admin_panel = (
            "╭─「 <b>👑 HelpingAI BOT Admin Panel</b> 」\n"
            "│\n"
            "│ 🎯 <b>Welcome to the control center!</b>\n"
            "│ Here you can manage everything about your group\n"
            "│ with professional-grade tools and features.\n"
            "│\n"
            "├─「 <b>🚀 Quick Stats</b> 」\n"
            f"│ 👥 Chat ID: <code>{update.effective_chat.id}</code>\n"
            f"│ 📊 Total Members: <b>{await context.bot.get_chat_member_count(update.effective_chat.id)}</b>\n"
            "│ 🛡️ Protection: <b>Active</b>\n"
            "│ 🤖 AI Moderation: <b>Online</b>\n"
            "│\n"
            "├─「 <b>💡 Navigation</b> 」\n"
            "│ Use the buttons below to access different\n"
            "│ admin features and management tools.\n"
            "│\n"
            "│ 🔹 Each section is organized for easy access\n"
            "│ 🔹 All actions are logged for transparency\n"
            "│ 🔹 Real-time updates and notifications\n"
            "│\n"
            "╰─「 <i>Select a category to get started</i> 」"
        )

        message = await update.message.reply_text(
            admin_panel,
            parse_mode=ParseMode.HTML,
            reply_markup=reply_markup,
            disable_web_page_preview=True
        )
        
        # Auto-delete after 5 minutes for admin panel
        asyncio.create_task(self._delete_after_delay(message, 300))

    async def handle_admin_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle admin panel button callbacks with Rose-bot style responses"""
        query = update.callback_query
        await query.answer()
        
        if not await self._check_admin_rights_callback(query, context):
            return
        
        action = query.data.replace("admin_", "")
        
        if action == "users":
            await self._show_user_management(query, context)
        elif action == "warnings":
            await self._show_warning_system(query, context)
        elif action == "messages":
            await self._show_message_tools(query, context)
        elif action == "protection":
            await self._show_protection_panel(query, context)
        elif action == "settings":
            await self._show_group_settings(query, context)
        elif action == "analytics":
            await self._show_analytics_panel(query, context)
        elif action == "quick":
            await self._show_quick_actions(query, context)
        elif action == "help":
            await self._show_help_guide(query, context)
        elif action == "refresh":
            await self._refresh_admin_panel(query, context)

    async def _show_user_management(self, query, context):
        """Display user management panel"""
        keyboard = [
            [
                InlineKeyboardButton("⛔ Ban User", callback_data="action_ban"),
                InlineKeyboardButton("🔇 Mute User", callback_data="action_mute")
            ],
            [
                InlineKeyboardButton("⏳ Temp Ban", callback_data="action_tban"),
                InlineKeyboardButton("🌊 Soft Ban", callback_data="action_sban")
            ],
            [
                InlineKeyboardButton("✅ Unban", callback_data="action_unban"),
                InlineKeyboardButton("🔊 Unmute", callback_data="action_unmute")
            ],
            [
                InlineKeyboardButton("👑 Promote", callback_data="action_promote"),
                InlineKeyboardButton("👎 Demote", callback_data="action_demote")
            ],
            [InlineKeyboardButton("◀ Back", callback_data="admin_refresh")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        text = (
            "╭─「 <b>👥 User Management Panel</b> 」\n"
            "│\n"
            "│ 🎯 <b>Manage users with precision</b>\n"
            "│\n"
            "├─「 <b>📋 Quick Instructions</b> 」\n"
            "│ 🔹 Reply to a message + click button\n"
            "│ 🔹 Or use commands with @username\n"
            "│ 🔹 All actions are logged automatically\n"
            "│\n"
            "├─「 <b>⚡ Available Actions</b> 」\n"
            "│ ⛔ <b>Ban</b> - Permanent removal\n"
            "│ ⏳ <b>Temp Ban</b> - Time-limited ban\n"
            "│ 🌊 <b>Soft Ban</b> - Ban + immediate unban\n"
            "│ 🔇 <b>Mute</b> - Restrict messaging\n"
            "│ 👑 <b>Promote</b> - Grant admin rights\n"
            "│\n"
            "╰─「 <i>Select an action below</i> 」"
        )
        
        await query.edit_message_text(text, parse_mode=ParseMode.HTML, reply_markup=reply_markup)

    async def _show_warning_system(self, query, context):
        """Display warning system panel"""
        keyboard = [
            [
                InlineKeyboardButton("⚠️ Warn User", callback_data="action_warn"),
                InlineKeyboardButton("🗑️ Delete & Warn", callback_data="action_dwarn")
            ],
            [
                InlineKeyboardButton("🤫 Silent Warn", callback_data="action_swarn"),
                InlineKeyboardButton("🔄 Remove Warning", callback_data="action_unwarn")
            ],
            [
                InlineKeyboardButton("📊 Warning Stats", callback_data="action_warn_stats"),
                InlineKeyboardButton("⚙️ Warn Settings", callback_data="action_warn_config")
            ],
            [InlineKeyboardButton("◀ Back", callback_data="admin_refresh")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        text = (
            "╭─「 <b>⚠️ Warning System</b> 」\n"
            "│\n"
            "│ 🎯 <b>Smart disciplinary system</b>\n"
            "│\n"
            "├─「 <b>📋 How It Works</b> 」\n"
            "│ 🔹 3 warnings = auto-action\n"
            "│ 🔹 Customizable punishment levels\n"
            "│ 🔹 Track user behavior patterns\n"
            "│\n"
            "├─「 <b>🛡️ Warning Types</b> 」\n"
            "│ ⚠️ <b>Standard</b> - Public warning with reason\n"
            "│ 🗑️ <b>Delete & Warn</b> - Remove message too\n"
            "│ 🤫 <b>Silent</b> - No public notification\n"
            "│\n"
            "╰─「 <i>Choose your action wisely</i> 」"
        )
        
        await query.edit_message_text(text, parse_mode=ParseMode.HTML, reply_markup=reply_markup)

    async def _refresh_admin_panel(self, query, context):
        """Refresh the main admin panel"""
        keyboard = [
            [
                InlineKeyboardButton("👥 User Management", callback_data="admin_users"),
                InlineKeyboardButton("⚠️ Warning System", callback_data="admin_warnings")
            ],
            [
                InlineKeyboardButton("💬 Message Tools", callback_data="admin_messages"),
                InlineKeyboardButton("🛡️ Protection", callback_data="admin_protection")
            ],
            [
                InlineKeyboardButton("⚙️ Group Settings", callback_data="admin_settings"),
                InlineKeyboardButton("📊 Analytics", callback_data="admin_analytics")
            ],
            [
                InlineKeyboardButton("🎯 Quick Actions", callback_data="admin_quick"),
                InlineKeyboardButton("📚 Help Guide", callback_data="admin_help")
            ],
            [InlineKeyboardButton("🔄 Refresh Panel", callback_data="admin_refresh")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        admin_panel = (
            "╭─「 <b>👑 HelpingAI BOT Admin Panel</b> 」\n"
            "│\n"
            "│ 🎯 <b>Welcome to the control center!</b>\n"
            "│ Here you can manage everything about your group\n"
            "│ with professional-grade tools and features.\n"
            "│\n"
            "├─「 <b>🚀 Quick Stats</b> 」\n"
            f"│ 👥 Chat ID: <code>{query.message.chat.id}</code>\n"
            f"│ 📊 Total Members: <b>{await context.bot.get_chat_member_count(query.message.chat.id)}</b>\n"
            "│ 🛡️ Protection: <b>Active</b>\n"
            "│ 🤖 AI Moderation: <b>Online</b>\n"
            "│\n"
            "├─「 <b>💡 Navigation</b> 」\n"
            "│ Use the buttons below to access different\n"
            "│ admin features and management tools.\n"
            "│\n"
            "│ 🔹 Each section is organized for easy access\n"
            "│ 🔹 All actions are logged for transparency\n"
            "│ 🔹 Real-time updates and notifications\n"
            "│\n"
            "╰─「 <i>Select a category to get started</i> 」"
        )

        await query.edit_message_text(admin_panel, parse_mode=ParseMode.HTML, reply_markup=reply_markup)

    async def _check_admin_rights_callback(self, query, context) -> bool:
        """Check admin rights for callback queries"""
        try:
            user_id = query.from_user.id
            chat_id = query.message.chat.id
            
            member = await context.bot.get_chat_member(chat_id, user_id)
            if member.status not in [ChatMemberStatus.ADMINISTRATOR, ChatMemberStatus.OWNER]:
                await query.answer("❌ You need admin rights to use this!", show_alert=True)
                return False
            return True
        except Exception as e:
            await query.answer("❌ Error checking permissions!", show_alert=True)
            return False

    def register_handlers(self, application):
        """Register all admin command handlers."""
        # Main admin panel command
        application.add_handler(CommandHandler("admin", self.admin_commands))
        application.add_handler(CommandHandler("adminpanel", self.admin_commands))
        
        # Admin panel callbacks
        application.add_handler(CallbackQueryHandler(self.handle_admin_callback, pattern="^admin_"))
        
        # User Management Commands
        application.add_handler(CommandHandler("ban", self.ban_user))
        application.add_handler(CommandHandler("tban", self.ban_user))
        application.add_handler(CommandHandler("sban", self.ban_user))
        application.add_handler(CommandHandler("unban", self.unban_user))
        application.add_handler(CommandHandler("kick", self.kick_user))
        application.add_handler(CommandHandler("mute", self.mute_user))
        application.add_handler(CommandHandler("tmute", self.tmute_user))
        application.add_handler(CommandHandler("unmute", self.unmute_user))
        application.add_handler(CommandHandler("promote", self.promote_user))
        application.add_handler(CommandHandler("demote", self.demote_user))

        # Warning System Commands
        application.add_handler(CommandHandler("warn", self.warn_user))
        application.add_handler(CommandHandler("dwarn", self.dwarn_user))
        application.add_handler(CommandHandler("swarn", self.swarn_user))
        application.add_handler(CommandHandler("unwarn", self.unwarn_user))
        application.add_handler(CommandHandler("warnings", self.warnings_command))
        application.add_handler(CommandHandler("resetwarn", self.resetwarn_command))

        # Message Management Commands
        application.add_handler(CommandHandler("del", self.deletion))
        application.add_handler(CommandHandler("purge", self.purge))
        application.add_handler(CommandHandler("pin", self.pin_message))
        application.add_handler(CommandHandler("unpin", self.unpin_message))
        application.add_handler(CommandHandler("unpinall", self.unpin_all_messages))

        # Poll Command
        application.add_handler(CommandHandler("poll", self.create_poll))

        # Admin Help Command
        application.add_handler(CommandHandler("admin", self.admin_commands))
        
        # Admin List Commands
        application.add_handler(CommandHandler("adminlist", self.adminlist_command))
        application.add_handler(CommandHandler("admins", self.admins_command))
        
        # Callback Handlers
        application.add_handler(CallbackQueryHandler(self.mute_user, pattern="^mute_"))
        application.add_handler(CallbackQueryHandler(self.unmute_user, pattern="^unmute_"))


    async def mute_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle both mute command and mute callbacks."""
        if update.callback_query:
            query = update.callback_query
            if not await self._check_admin_rights(update, context):
                await query.answer("❌ Only admins can use these buttons!", show_alert=True)
                return

            await query.answer()
            data = query.data.split('_')
            action = data[0]

            if action == "mute":
                if len(data) < 3:
                    return

                user_id = int(data[1])
                duration = int(data[2])
                chat = update.effective_chat
                user = await context.bot.get_chat_member(chat.id, user_id)
                user_mention = user.user.mention_html()

                permissions = ChatPermissions(
                    can_send_messages=False,
                    can_send_audios=False,
                    can_send_documents=False,
                    can_send_photos=False,
                    can_send_videos=False,
                    can_send_video_notes=False,
                    can_send_voice_notes=False,
                    can_send_polls=False,
                    can_send_other_messages=False,
                    can_add_web_page_previews=False,
                    can_invite_users=False
                )

                until_date = int(time.time() + duration) if duration > 0 else None

                try:
                    await context.bot.restrict_chat_member(
                        chat.id,
                        user_id,
                        permissions,
                        until_date=until_date
                    )

                    duration_text = self._format_duration(duration)

                    keyboard = [
                        [
                            InlineKeyboardButton("🔊 Unmute", callback_data=f"unmute_{user_id}")
                        ]
                    ]
                    reply_markup = InlineKeyboardMarkup(keyboard)

                    savage_mute_messages = [
                        f"🤐 <b>SILENCE IS GOLDEN!</b>\n\n"
                        f"<b>Silenced Soul:</b> {user_mention}\n"
                        f"👮 <b>Silencer:</b> {update.effective_user.mention_html()}\n"
                        f"⏰ <b>Sentence:</b> {duration_text}",
                        
                        f"🔇 <b>MUTED IN 4K!</b>\n\n"
                        f"<b>Chat Menace:</b> {user_mention}\n"
                        f"👮 <b>Peace Keeper:</b> {update.effective_user.mention_html()}\n"
                        f"⏰ <b>Timeout:</b> {duration_text}",
                        
                        f"📢 <b>CAUGHT LACKING!</b>\n\n"
                        f"<b>Quiet Corner Kid:</b> {user_mention}\n"
                        f"👮 <b>Timeout Giver:</b> {update.effective_user.mention_html()}\n"
                        f"⏰ <b>Think About Life For:</b> {duration_text}"
                    ]

                    mute_message = random.choice(savage_mute_messages)
                    if duration > 0:
                        mute_message += f"\n<i>Freedom Returns: {datetime.fromtimestamp(until_date).strftime('%Y-%m-%d %H:%M:%S')}</i>"

                    await query.edit_message_text(
                        mute_message,
                        parse_mode=ParseMode.HTML,
                        reply_markup=reply_markup
                    )

                except TelegramError as e:
                    await query.edit_message_text(f"❌ Failed to mute user: {str(e)}")

            elif action == "unmute":
                chat_id = update.effective_chat.id
                user_id = int(data[1])
                
                try:
                    # Get user info for mention
                    user = await context.bot.get_chat_member(chat_id, user_id)
                    user_mention = user.user.mention_html()
                    admin_mention = update.effective_user.mention_html()

                    # Set permissions for unmute
                    permissions = ChatPermissions(
                        can_send_messages=True,
                        can_send_audios=True,
                        can_send_documents=True,
                        can_send_photos=True,
                        can_send_videos=True,
                        can_send_video_notes=True,
                        can_send_voice_notes=True,
                        can_send_polls=False,  # Polls disabled
                        can_send_other_messages=True,
                        can_add_web_page_previews=True,
                        can_invite_users=True,
                        can_pin_messages=False  # Pin disabled
                    )

                    # Unmute the user
                    await context.bot.restrict_chat_member(
                        chat_id,
                        user_id,
                        permissions
                    )

                    # Log the action
                    await self.db.log_admin_action(
                        chat_id=chat_id,
                        admin_id=update.effective_user.id,
                        action="unmute",
                        target_user_id=user_id,
                        reason="Button unmute"
                    )

                    unmute_messages = [
                        f"🔊 <b>AYOO, WHO LET THEM TALK AGAIN?!</b>\n\n"
                        f"<b>Back in the chat:</b> {user_mention}\n"
                        f"<b>Freedom Provider:</b> {admin_mention}\n\n"
                        f"<i>Welcome back to the land of the speaking! 😎</i>",
                        
                        f"🎉 <b>GUESS WHO'S BACK TO SPAM THE CHAT!</b>\n\n"
                        f"<b>Chat Menace:</b> {user_mention}\n"
                        f"<b>Responsible Admin:</b> {admin_mention}\n\n"
                        f"<i>Try using your words wisely this time! 😉</i>",
                        
                        f"📢 <b>BREAKING NEWS: THEY CAN TALK AGAIN!</b>\n\n"
                        f"<b>Reformed Citizen:</b> {user_mention}\n"
                        f"<b>Merciful Admin:</b> {admin_mention}\n\n"
                        f"<i>Don't make us regret this decision! 😤</i>"
                    ]

                    # Edit the original callback message
                    await query.edit_message_text(
                        text=random.choice(unmute_messages),
                        parse_mode=ParseMode.HTML
                    )

                    # Delete the message after 30 seconds
                    await asyncio.sleep(30)
                    try:
                        await query.message.delete()
                    except TelegramError:
                        pass

                except TelegramError as e:
                    error_message = f"❌ Failed to unmute user: {str(e)}"
                    try:
                        await query.edit_message_text(
                            text=error_message,
                            parse_mode=ParseMode.HTML
                        )
                    except TelegramError:
                        await context.bot.send_message(
                            chat_id=chat_id,
                            text=error_message,
                            parse_mode=ParseMode.HTML
                        )
                return

        # Handle /mute command
        if not update.message:
            return

        if not await self._check_admin_rights(update, context):
            await update.message.reply_text("❌ You don't have permission to mute users.")
            return

        try:
            user_id = await self._get_user_id(update)
            if not user_id:
                savage_help_messages = [
                    "🎯 <b>MISSION IMPOSSIBLE!</b>\n\n"
                    "Can't mute the invisible!\n"
                    "Try one of these instead:\n"
                    "• Reply to their message with /mute\n"
                    "• Use /mute @username\n"
                    "• Use /mute user_id\n\n"
                    "<i>Let's make this mute magical! ✨</i>",
                    
                    "🤔 <b>BRUH MOMENT!</b>\n\n"
                    "Who we muting today, chief?\n"
                    "Try these pro gamer moves:\n"
                    "• Reply to their message with /mute\n"
                    "• Use /mute @username\n"
                    "• Use /mute user_id\n\n"
                    "<i>Time to silence someone! 🤫</i>"
                ]
                await update.message.reply_text(random.choice(savage_help_messages), parse_mode=ParseMode.HTML)
                return

            chat_id = update.effective_chat.id
            user = await context.bot.get_chat_member(chat_id, user_id)
            user_mention = user.user.mention_html()

            keyboard = [
                [
                    InlineKeyboardButton("🤐 5 Minutes", callback_data=f"mute_{user_id}_300"),
                    InlineKeyboardButton("🤐 30 Minutes", callback_data=f"mute_{user_id}_1800")
                ],
                [
                    InlineKeyboardButton("🤐 2 Hours", callback_data=f"mute_{user_id}_7200"),
                    InlineKeyboardButton("🤐 24 Hours", callback_data=f"mute_{user_id}_86400")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                f"⏰ <b>MUTE DURATION SELECTOR</b>\n\n"
                f"Target: {user_mention}\n\n"
                f"<i>Choose how long they should think about their life choices:</i>",
                reply_markup=reply_markup,
                parse_mode=ParseMode.HTML
            )

        except Exception as e:
            await update.message.reply_text(f"❌ Error: {str(e)}")

    async def _get_user_id(self, update: Update) -> Optional[int]:
        """Helper method to get user ID from command."""
        if update.message.reply_to_message:
            return update.message.reply_to_message.from_user.id

        if not update.message.text:
            return None

        parts = update.message.text.split()
        if len(parts) < 2:
            return None

        user_ref = parts[1]

        if user_ref.isdigit():
            return int(user_ref)
        elif user_ref.startswith('@'):
            try:
                user = await update.message.bot.get_chat(user_ref)
                return user.id
            except TelegramError:
                return None

        return None

    async def unmute_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle both unmute command and unmute callbacks."""
        chat_id = update.effective_chat.id
        
        # Handle callback query (button press)
        if update.callback_query:
            query = update.callback_query
            if not await self._check_admin_rights(update, context):
                await query.answer("❌ Only admins can use these buttons!", show_alert=True)
                return

            await query.answer()
            data = query.data.split('_')
            
            if len(data) < 2:
                return
            
            user_id = int(data[1])
            
            try:
                # Get user info for mention
                user = await context.bot.get_chat_member(chat_id, user_id)
                user_mention = user.user.mention_html()
                admin_mention = update.effective_user.mention_html()

                # Set permissions for unmute
                permissions = ChatPermissions(
                    can_send_messages=True,
                    can_send_audios=True,
                    can_send_documents=True,
                    can_send_photos=True,
                    can_send_videos=True,
                    can_send_video_notes=True,
                    can_send_voice_notes=True,
                    can_send_polls=False,  # Polls disabled
                    can_send_other_messages=True,
                    can_add_web_page_previews=True,
                    can_invite_users=True,
                    can_pin_messages=False  # Pin disabled
                )

                # Unmute the user
                await context.bot.restrict_chat_member(
                    chat_id,
                    user_id,
                    permissions
                )

                # Log the action
                await self.db.log_admin_action(
                    chat_id=chat_id,
                    admin_id=update.effective_user.id,
                    action="unmute",
                    target_user_id=user_id,
                    reason="Button unmute"
                )

                unmute_messages = [
                    f"🔊 <b>AYOO, WHO LET THEM TALK AGAIN?!</b>\n\n"
                    f"<b>Back in the chat:</b> {user_mention}\n"
                    f"<b>Freedom Provider:</b> {admin_mention}\n\n"
                    f"<i>Welcome back to the land of the speaking! 😎</i>",
                    
                    f"🎉 <b>GUESS WHO'S BACK TO SPAM THE CHAT!</b>\n\n"
                    f"<b>Chat Menace:</b> {user_mention}\n"
                    f"<b>Responsible Admin:</b> {admin_mention}\n\n"
                    f"<i>Try using your words wisely this time! 😉</i>",
                    
                    f"📢 <b>BREAKING NEWS: THEY CAN TALK AGAIN!</b>\n\n"
                    f"<b>Reformed Citizen:</b> {user_mention}\n"
                    f"<b>Merciful Admin:</b> {admin_mention}\n\n"
                    f"<i>Don't make us regret this decision! 😤</i>"
                ]

                # Edit the original callback message
                await query.edit_message_text(
                    text=random.choice(unmute_messages),
                    parse_mode=ParseMode.HTML
                )

                # Delete the message after 30 seconds
                await asyncio.sleep(30)
                try:
                    await query.message.delete()
                except TelegramError:
                    pass

            except TelegramError as e:
                error_message = f"❌ Failed to unmute user: {str(e)}"
                try:
                    await query.edit_message_text(
                        text=error_message,
                        parse_mode=ParseMode.HTML
                    )
                except TelegramError:
                    await context.bot.send_message(
                        chat_id=chat_id,
                        text=error_message,
                        parse_mode=ParseMode.HTML
                    )
            return

        # Handle /unmute command
        if not update.message:
            return

        if not await self._check_admin_rights(update, context):
            await update.message.reply_text("❌ You don't have permission to unmute users.")
            return

        try:
            bot_member = await context.bot.get_chat_member(chat_id, context.bot.id)
            if not bot_member.can_restrict_members:
                try:
                    msg = await context.bot.send_message(
                        chat_id=chat_id,
                        text="😅 <b>Awkward...</b>\n\n"
                             "I don't have permission to unmute members!\n"
                             "Someone needs to give me more power! 💪\n\n"
                             "<i>This message will self-destruct in 5 seconds...</i>",
                        parse_mode=ParseMode.HTML
                    )
                    await self._delete_after_delay(msg, 5)
                except TelegramError as e:
                    logging.error(f"Failed to send permission error: {e}")
                return

            user_to_unmute = None
            if update.message.reply_to_message:
                user_to_unmute = update.message.reply_to_message.from_user
            elif context.args:
                target = context.args[0].replace('@', '')
                member = await self._find_user(context, chat_id, target)
                if member:
                    user_to_unmute = member.user
            if not user_to_unmute:
                keyboard = [
                    [
                        InlineKeyboardButton("🎯 Target Someone", switch_inline_query_current_chat="unmute "),
                        InlineKeyboardButton("📖 Unmute Guide", callback_data="unmute_help")
                    ]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                try:
                    msg = await context.bot.send_message(
                        chat_id=chat_id,
                        text="🔊 <b>Unmute Command Help</b>\n\n"
                             "How to use:\n"
                             "• Reply to message with /unmute\n"
                             "• Use /unmute @username\n"
                             "• Use /unmute user_id\n\n"
                             "<i>Time to let someone speak again! 🗣️</i>",
                        parse_mode=ParseMode.HTML,
                        reply_markup=reply_markup
                    )
                    await self._delete_after_delay(msg, 30)
                except TelegramError as e:
                    logging.error(f"Failed to send help message: {e}")
                return

            permissions = ChatPermissions(
                can_send_messages=True,
                can_send_audios=True,
                can_send_documents=True,
                can_send_photos=True,
                can_send_videos=True,
                can_send_video_notes=True,
                can_send_voice_notes=True,
                can_send_polls=False,  # Polls disabled
                can_send_other_messages=True,
                can_add_web_page_previews=True,
                can_invite_users=True,
                can_pin_messages=False  # Pin disabled
            )

            await context.bot.restrict_chat_member(
                chat_id,
                user_to_unmute.id,
                permissions
            )

            await self.db.log_admin_action(
                chat_id=chat_id,
                admin_id=update.effective_user.id,
                action="unmute",
                target_user_id=user_to_unmute.id,
                reason="Manual unmute"
            )

            user_mention = user_to_unmute.mention_html()
            admin_mention = update.effective_user.mention_html()

            unmute_messages = [
                f"🔊 <b>AYOO, WHO LET THEM TALK AGAIN?!</b>\n\n"
                f"<b>Back in the chat:</b> {user_mention}\n"
                f"<b>Freedom Provider:</b> {admin_mention}\n\n"
                f"<i>Welcome back to the land of the speaking! 😎</i>",
                f"🎉 <b>GUESS WHO'S BACK TO SPAM THE CHAT!</b>\n\n"
                f"<b>Chat Menace:</b> {user_mention}\n"
                f"<b>Responsible Admin:</b> {admin_mention}\n\n"
                f"<i>Try using your words wisely this time! 😉</i>",
                f"📢 <b>BREAKING NEWS: THEY CAN TALK AGAIN!</b>\n\n"
                f"<b>Reformed Citizen:</b> {user_mention}\n"
                f"<b>Merciful Admin:</b> {admin_mention}\n\n"
                f"<i>Don't make us regret this decision! 😤</i>"
            ]

            try:
                msg = await context.bot.send_message(
                    chat_id=chat_id,
                    text=random.choice(unmute_messages),
                    parse_mode=ParseMode.HTML
                )
                await self._delete_after_delay(msg, 30)
            except TelegramError as e:
                logging.error(f"Failed to send unmute message: {e}")

        except Exception as e:
            logging.error(f"Error in unmute_user: {str(e)}", exc_info=True)
            try:
                msg = await context.bot.send_message(
                    chat_id=chat_id,
                    text="❌ <b>An error occurred</b>\n\n"
                         "<i>This message will self-destruct in 5 seconds...</i>",
                    parse_mode=ParseMode.HTML
                )
                await self._delete_after_delay(msg, 5)
            except TelegramError as e:
                logging.error(f"Failed to send error message: {e}")

    async def ban_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Ban a user with optional duration."""
        if not await self._check_admin_rights(update, context):
            await update.message.reply_text(
                "❌ <b>PERMISSION DENIED!</b>\n\n"
                "<i>Nice try, but you're not worthy of the ban hammer! 🔨</i>",
                parse_mode=ParseMode.HTML
            )
            return

        message = update.message
        chat_id = update.effective_chat.id
        command = message.text.split()[0].lower()

        if message.reply_to_message:
            target_user = message.reply_to_message.from_user
            reason = " ".join(context.args) if context.args else "No reason provided"
        else:
            if not context.args:
                await message.reply_text(
                    "<b>🎯 How to Unleash the Ban Hammer:</b>\n\n"
                    "• Reply to a message with:\n"
                    "  <code>/ban [reason]</code> - Permanent ban\n"
                    "  <code>/tban [duration] [reason]</code> - Temporary ban\n"
                    "  <code>/sban [reason]</code> - Soft ban (kick)\n\n"
                    "• Or use directly:\n"
                    "  <code>/ban @username [reason]</code>\n"
                    "  <code>/tban @username 24h [reason]</code>\n"
                    "  <code>/sban user_id [reason]</code>\n\n"
                    "<i>Duration format: 1h, 2d, 30m</i>",
                    parse_mode=ParseMode.HTML
                )
                return

            target = context.args[0]
            reason = " ".join(context.args[1:]) if len(context.args) > 1 else "No reason provided"

            try:
                if target.startswith('@'):
                    username = target[1:]
                    try:
                        chat_member = await self._get_user_by_username(username, context, chat_id)
                        if not chat_member:
                            await message.reply_text(
                                "❌ <b>TARGET NOT FOUND!</b>\n\n"
                                "<i>This user is like my Boss - nowhere to be found! 👻</i>",
                                parse_mode=ParseMode.HTML
                            )
                            return
                        target_user = chat_member.user
                    except TelegramError as e:
                        await message.reply_text(
                            f"❌ <b>MISSION FAILED!</b>\n\n"
                            f"<code>{str(e)}</code>\n\n"
                            f"<i>Even Chuck Norris couldn't find this user! 🥋</i>",
                            parse_mode=ParseMode.HTML
                        )
                        return
                else:
                    try:
                        user_id = int(target)
                        try:
                            target_user = await context.bot.get_chat_member(chat_id, user_id)
                            target_user = target_user.user
                        except TelegramError:
                            target_user = User(id=user_id, first_name="Unknown", is_bot=False)
                    except ValueError:
                        await message.reply_text(
                            "❌ <b>INVALID TARGET FORMAT!</b>\n\n"
                            "<i>What are you trying to ban? Your imaginary friend? 🤪</i>\n"
                            "Use @username or numeric ID, Einstein!",
                            parse_mode=ParseMode.HTML
                        )
                        return
            except Exception as e:
                await message.reply_text(
                    f"❌ <b>TASK FAILED SUCCESSFULLY!</b>\n\n"
                    f"<code>{str(e)}</code>\n\n"
                    "<i>Even the ban hammer is confused! 🔨</i>",
                    parse_mode=ParseMode.HTML
                )
                return

        try:
            duration = None
            if command == "/tban":
                # For tban, we need to determine if we're using reply-to-message or direct target
                if message.reply_to_message:
                    # If replying to message, first arg (if any) is duration
                    if context.args:
                        try:
                            duration = int(self._parse_time(context.args[0]).total_seconds())
                            reason = " ".join(context.args[1:]) if len(context.args) > 1 else "No reason provided"
                        except ValueError:
                            await message.reply_text(
                                "❌ <b>TIME FORMAT ERROR!</b>\n\n"
                                "<i>What time unit is that? Banana? 🍌</i>\n"
                                "Use: 30m, 24h, or 7d",
                                parse_mode=ParseMode.HTML
                            )
                            return
                    else:
                        # Default duration if none provided
                        duration = int(timedelta(hours=24).total_seconds())
                        reason = "No reason provided"
                else:
                    # If not replying, we have target as first arg
                    # Duration should be second arg (if any)
                    if len(context.args) > 1:
                        try:
                            duration = int(self._parse_time(context.args[1]).total_seconds())
                            reason = " ".join(context.args[2:]) if len(context.args) > 2 else "No reason provided"
                        except ValueError:
                            await message.reply_text(
                                "❌ <b>TIME FORMAT ERROR!</b>\n\n"
                                "<i>What time unit is that? Banana? 🍌</i>\n"
                                "Use: 30m, 24h, or 7d",
                                parse_mode=ParseMode.HTML
                            )
                            return
                    else:
                        # Default duration if none provided
                        duration = int(timedelta(hours=24).total_seconds())
                        reason = "No reason provided"

            if command == "/tban":
                ban_type = f"temporary ({duration}s)" if duration else "temporary (24h default)"
                until_date = datetime.now() + timedelta(seconds=duration) if duration else datetime.now() + timedelta(hours=24)
                await context.bot.ban_chat_member(chat_id, target_user.id, until_date=until_date)
                
                if duration:
                    context.job_queue.run_once(
                        self._send_unban_link,
                        duration,
                        data={
                            'chat_id': chat_id,
                            'user_id': target_user.id,
                            'chat_title': update.effective_chat.title
                        }
                    )
            
            elif command == "/sban":
                ban_type = "kick"
                await context.bot.ban_chat_member(chat_id, target_user.id)
                await context.bot.unban_chat_member(chat_id, target_user.id)
                
                await self._send_unban_link(context, {
                    'chat_id': chat_id,
                    'user_id': target_user.id,
                    'chat_title': update.effective_chat.title
                })
            
            else:
                ban_type = "permanent"
                await context.bot.ban_chat_member(chat_id, target_user.id)

            if self.db:
                await self.db.add_ban(
                    chat_id=chat_id,
                    user_id=target_user.id,
                    admin_id=update.effective_user.id,
                    reason=reason,
                    duration=duration,
                    ban_type=ban_type
                )

            user_mention = target_user.mention_html()
            admin_mention = update.effective_user.mention_html()

            await self._send_ban_notification(
                context, chat_id, user_mention, admin_mention,
                ban_type, reason, command
            )

        except TelegramError as e:
            await message.reply_text(
                f"❌ <b>MISSION FAILED SUCCESSFULLY!</b>\n\n"
                f"<code>{str(e)}</code>\n\n"
                f"<i>Even the ban hammer needs a vacation sometimes! 🏖️</i>",
                parse_mode=ParseMode.HTML
            )

    async def _send_unban_link(self, context: ContextTypes.DEFAULT_TYPE, data: dict) -> None:
        """Send an invite link to the user after unban/kick."""
        chat_id = data['chat_id']
        user_id = data['user_id']
        chat_title = data['chat_title']

        try:
            # Create one-time use invite link that expires in 24 hours
            invite_link = await context.bot.create_chat_invite_link(
                chat_id=chat_id,
                expire_date=datetime.now() + timedelta(days=1),
                member_limit=1
            )

            # Savage comeback messages
            messages = [
                f"<b>🎯 REDEMPTION ARC UNLOCKED!</b>\n\n"
                f"Looks like someone's getting a second chance at {chat_title}!\n\n"
                f"<b>🎫 Your Comeback Pass:</b>\n"
                f"<a href='{invite_link.invite_link}'>Click to prove you're worthy!</a>\n\n"
                f"<i>⚡ Pro Tips:\n"
                f"• Link expires in 24h (tick tock!)\n"
                f"• One-time use (don't fumble it!)\n"
                f"• Try not to speedrun another ban! 🏃</i>",

                f"<b>🔄 RESPAWN TICKET ACTIVATED!</b>\n\n"
                f"Ready for round 2 in {chat_title}?\n\n"
                f"<b>🎮 Your Respawn Link:</b>\n"
                f"<a href='{invite_link.invite_link}'>Press Start to Continue!</a>\n\n"
                f"<i>🎯 Patch Notes:\n"
                f"• 24h expiry timer\n"
                f"• Single-use code\n"
                f"• New update: Follow the rules! 😎</i>",

                f"<b>🎪 CIRCUS READMISSION PASS!</b>\n\n"
                f"The {chat_title} show must go on!\n\n"
                f"<b>🎪 Your Backstage Pass:</b>\n"
                f"<a href='{invite_link.invite_link}'>Re-enter the circus!</a>\n\n"
                f"<i>🎭 Performance Notes:\n"
                f"• 24h until curtain call\n"
                f"• One performance only\n"
                f"• Try not to clown around this time! 🤡</i>"
            ]

            # Send DM with random savage message
            await context.bot.send_message(
                chat_id=user_id,
                text=random.choice(messages),
                parse_mode=ParseMode.HTML
            )
        except TelegramError as e:
            logging.error(f"Failed to send unban link: {e}")

    async def _send_ban_notification(self, context, chat_id, user_mention, admin_mention, ban_type, reason, command):
        """Send a savage ban/kick notification."""
        
        if command == "/sban":
            messages = [
                f"👋 <b>YEET CANNON ACTIVATED!</b>\n\n"
                f"<b>Yeeted:</b> {user_mention}\n"
                f"<b>Yeet Master:</b> {admin_mention}\n"
                f"<b>Yeet Type:</b> Kick\n"
                f"<b>Yeet Reason:</b> {reason}\n\n"
                f"<i>Door's still open if you behave! 🚪</i>",

                f"🥋 <b>KICK-FU MASTER STRIKES!</b>\n\n"
                f"<b>Student:</b> {user_mention}\n"
                f"<b>Sensei:</b> {admin_mention}\n"
                f"<b>Technique:</b> Flying Kick\n"
                f"<b>Lesson:</b> {reason}\n\n"
                f"<i>Come back when you've learned your lesson! 🎓</i>",

                f"🌪️ <b>TORNADO KICK DEPLOYED!</b>\n\n"
                f"<b>Victim:</b> {user_mention}\n"
                f"<b>Striker:</b> {admin_mention}\n"
                f"<b>Impact:</b> Temporary Removal\n"
                f"<b>Cause:</b> {reason}\n\n"
                f"<i>That's gonna leave a mark! 💫</i>"
            ]
        elif command == "/tban":
            messages = [
                f"⏳ <b>TIMEOUT DIMENSION OPENED!</b>\n\n"
                f"<b>Time Traveler:</b> {user_mention}\n"
                f"<b>Doctor Who:</b> {admin_mention}\n"
                f"<b>Temporal Shift:</b> {ban_type}\n"
                f"<b>Paradox Cause:</b> {reason}\n\n"
                f"<i>See you in another timeline! 🌀</i>",

                f"🔒 <b>VACATION TIME!</b>\n\n"
                f"<b>Tourist:</b> {user_mention}\n"
                f"<b>Travel Agent:</b> {admin_mention}\n"
                f"<b>Package:</b> {ban_type}\n"
                f"<b>Reason:</b> {reason}\n\n"
                f"<i>Enjoy your mandatory vacation! 🏖️</i>"
            ]
        else:
            messages = [
                f"💀 <b>OBLITERATION COMPLETE!</b>\n\n"
                f"<b>Deleted:</b> {user_mention}\n"
                f"<b>Executioner:</b> {admin_mention}\n"
                f"<b>Fatality Type:</b> Permanent\n"
                f"<b>Last Words:</b> {reason}\n\n"
                f"<i>Another one bites the dust! 👋</i>",

                f"🔨 <b>BAN HAMMER DROPPED!</b>\n\n"
                f"<b>Smashed:</b> {user_mention}\n"
                f"<b>Thor:</b> {admin_mention}\n"
                f"<b>Strike Type:</b> Critical\n"
                f"<b>Cause:</b> {reason}\n\n"
                f"<i>That's gonna leave a permanent mark! 💥</i>"
            ]

        msg = await context.bot.send_message(
            chat_id=chat_id,
            text=random.choice(messages),
            parse_mode=ParseMode.HTML
        )
        
        # 20% chance for savage followup
        if random.random() < 0.2:
            await asyncio.sleep(1)
            followups = [
                f"<i>{user_mention} just got a first-class ticket to Yeetville! ✈️</i>",
                f"<i>Another one joins the hall of shame! 🏆</i>",
                f"<i>Achievement Unlocked: Get Rekt! 🎮</i>",
                f"<i>Speedrunning that removal any% WR! 🏃</i>"
            ]
            followup_msg = await context.bot.send_message(
                chat_id=chat_id,
                text=random.choice(followups),
                parse_mode=ParseMode.HTML
            )
            await self._delete_after_delay(followup_msg, 30)
        
        await self._delete_after_delay(msg, 30)

    def _parse_time(self, time_str: str) -> timedelta:
        """Parse time string into timedelta."""
        unit = time_str[-1].lower()
        try:
            value = int(time_str[:-1])
            if unit == 'm':
                return timedelta(minutes=value)
            elif unit == 'h':
                return timedelta(hours=value)
            elif unit == 'd':
                return timedelta(days=value)
            else:
                raise ValueError("Invalid time unit")
        except ValueError:
            return timedelta(hours=24)  # Default to 24 hours



    async def unban_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Unban a previously banned user and send them an invite link."""
        if not await self._check_admin_rights(update, context):
            await update.message.reply_text(
                "❌ <b>ACCESS DENIED!</b>\n\n"
                "<i>You're about as qualified to unban as a potato is to fly! 🥔</i>",
                parse_mode=ParseMode.HTML
            )
            return

        if update.message.reply_to_message:
            user_to_unban = update.message.reply_to_message.from_user
            reason = " ".join(context.args) if context.args else "No reason provided"
        else:
            if not context.args:
                await update.message.reply_text(
                    "<b>🎯 How to Drop the Unban Hammer:</b>\n\n"
                    "• Reply to a message with:\n"
                    "  <code>/unban [reason]</code>\n\n"
                    "• Or use directly:\n"
                    "  <code>/unban @username [reason]</code>\n"
                    "  <code>/unban user_id [reason]</code>",
                    parse_mode=ParseMode.HTML
                )
                return

            target = context.args[0]
            reason = " ".join(context.args[1:]) if len(context.args) > 1 else "No reason provided"

            try:
                if target.startswith('@'):
                    username = target[1:]
                    try:
                        chat_member = await self._get_user_by_username(username, context, update.effective_chat.id)
                        if not chat_member:
                            await update.message.reply_text(
                                "❌ <b>USER NOT FOUND!</b>\n\n"
                                "<i>Are you trying to unban a ghost? 👻</i>",
                                parse_mode=ParseMode.HTML
                            )
                            return
                        user_to_unban = chat_member.user
                    except TelegramError as e:
                        await update.message.reply_text(
                            f"❌ <b>TASK FAILED SUCCESSFULLY!</b>\n\n"
                            f"<code>{str(e)}</code>\n\n"
                            f"<i>Even FBI couldn't find this user! 🕵️</i>",
                            parse_mode=ParseMode.HTML
                        )
                        return
                else:
                    try:
                        user_id = int(target)
                        try:
                            user_to_unban = await context.bot.get_chat_member(update.effective_chat.id, user_id)
                            user_to_unban = user_to_unban.user
                        except TelegramError:
                            user_to_unban = User(id=user_id, first_name="Unknown", is_bot=False)
                    except ValueError:
                        await update.message.reply_text(
                            "❌ <b>INVALID FORMAT!</b>\n\n"
                            "<i>What are you trying to unban? Your social life? 😅</i>\n"
                            "Use @username or numeric ID, genius!",
                            parse_mode=ParseMode.HTML
                        )
                        return
            except Exception as e:
                await update.message.reply_text(
                    f"❌ <b>OPERATION FAILED!</b>\n\n"
                    f"<code>{str(e)}</code>\n\n"
                    "<i>Even the unban hammer is facepalming! 🤦</i>",
                    parse_mode=ParseMode.HTML
                )
                return

        try:
            await context.bot.unban_chat_member(
                update.effective_chat.id,
                user_to_unban.id,
                only_if_banned=True
            )

            try:
                invite_link = await context.bot.create_chat_invite_link(
                    update.effective_chat.id,
                    expire_date=datetime.now() + timedelta(days=1),
                    member_limit=1
                )
                try:
                    await context.bot.send_message(
                        user_to_unban.id,
                        f"<b>🎉 JAILBREAK SUCCESSFUL!</b>\n\n"
                        f"Guess who just got pardoned from {update.effective_chat.title}! 🎊\n\n"
                        f"<b>🎫 Your VIP Comeback Tour:</b>\n"
                        f"<a href='{invite_link.invite_link}'>Click to slide back in!</a>\n\n"
                        f"<i>⚡ Quick Rules Refresh:\n"
                        f"• Link expires in 24h\n"
                        f"• One-time use only\n"
                        f"• Try not to speedrun another ban! 🏃</i>",
                        parse_mode=ParseMode.HTML
                    )
                    dm_status = "✅ Slid into their DMs with the VIP pass!"
                except TelegramError:
                    dm_status = "❌ Can't DM! Tell them to start @YourBot first!"
            except TelegramError:
                invite_link = None
                dm_status = "❌ Failed to forge the comeback pass!"

            if self.db:
                await self.db.log_admin_action(
                    chat_id=update.effective_chat.id,
                    admin_id=update.effective_user.id,
                    action="unban",
                    target_user_id=user_to_unban.id,
                    reason=reason
                )

            user_mention = user_to_unban.mention_html() if hasattr(user_to_unban, 'mention_html') else f"User {user_to_unban.id}"
            admin_mention = update.effective_user.mention_html()

            unban_messages = [
                f"🎉 <b>FREEDOM PROTOCOL ACTIVATED!</b>\n\n"
                f"<b>Jailbreaker:</b> {user_mention}\n"
                f"<b>Cool Admin:</b> {admin_mention}\n"
                f"<b>Pardon Reason:</b> {reason}\n"
                f"<b>DM Status:</b> {dm_status}\n\n"
                f"<i>Don't make us regret this jailbreak! 🚔</i>",

                f"⚡ <b>PRISON BREAK SUCCESS!</b>\n\n"
                f"<b>Lucky Escapee:</b> {user_mention}\n"
                f"<b>Guard on Payroll:</b> {admin_mention}\n"
                f"<b>Release Papers:</b> {reason}\n"
                f"<b>Message Status:</b> {dm_status}\n\n"
                f"<i>Try not to speedrun another ban! 🏃</i>",

                f"🔄 <b>CTRL+Z EXECUTED!</b>\n\n"
                f"<b>Restored User:</b> {user_mention}\n"
                f"<b>System Admin:</b> {admin_mention}\n"
                f"<b>Restore Point:</b> {reason}\n"
                f"<b>Notification:</b> {dm_status}\n\n"
                f"<i>Back from the Shadow Realm! 👻</i>"
            ]

            msg = await update.message.reply_text(
                random.choice(unban_messages),
                parse_mode=ParseMode.HTML
            )

            # 20% chance for savage followup
            if random.random() < 0.2:
                await asyncio.sleep(1)
                followups = [
                    f"<i>{user_mention} just got their 'Get Out of Jail Free' card! 🎮</i>",
                    f"<i>Breaking News: Local Admin Has Heart! ❤️</i>",
                    f"<i>Achievement Unlocked: Freedom! 🏆</i>",
                    f"<i>Plot twist: They're actually reformed! (maybe) 😇</i>"
                ]
                followup_msg = await context.bot.send_message(
                    chat_id=update.effective_chat.id,
                    text=random.choice(followups),
                    parse_mode=ParseMode.HTML
                )
                await self._delete_after_delay(followup_msg, 30)
            
            await self._delete_after_delay(msg, 30)

        except TelegramError as e:
            await update.message.reply_text(
                f"❌ <b>JAILBREAK FAILED!</b>\n\n"
                f"<code>{str(e)}</code>\n\n"
                f"<i>Even Houdini couldn't escape this one! 🔒</i>",
                parse_mode=ParseMode.HTML
            )

    async def deletion(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """
        /del - Delete a single message.
        """
        if not await self._check_admin_rights(update, context):
            return

        if not update.message.reply_to_message:
            help_msg = await update.message.reply_text(
                "<b>🗑️ DELETE COMMAND MANUAL</b>\n\n"
                "<b>Too difficult? Here's how:</b>\n"
                "1️⃣ Find the offensive message\n"
                "2️⃣ Reply with <code>/del</code>\n"
                "3️⃣ Add <code>-s</code> to be sneaky\n\n"
                "<i>Even a goldfish could follow these steps! 🐠</i>",
                parse_mode=ParseMode.HTML
            )
            await self._delete_after_delay(help_msg, 10)  # Delete help message after 10 seconds
            return

        silent_mode = '-s' in context.args

        processing_msg = None
        if not silent_mode:
            processing_msg = await update.message.reply_text(
                "<b>💀 OBLITERATION SEQUENCE INITIATED!</b>\n"
                "<i>Watch this message disappear faster than your ex!</i>",
                parse_mode=ParseMode.HTML
            )

        try:
            target_msg = update.message.reply_to_message
            start_time = time.time()
            await context.bot.delete_message(update.effective_chat.id, target_msg.message_id)
            elapsed = time.time() - start_time

            if processing_msg:
                await processing_msg.delete()

            # Delete command message after successful deletion
            try:
                await update.message.delete()
            except TelegramError:
                pass

            if not silent_mode:
                savage_messages = [
                    f"<b>🎯 TARGET ELIMINATED!</b>\n\n"
                    f"• Message ID: <code>{target_msg.message_id}</code>\n"
                    f"• Execution Time: <code>{elapsed:.2f}s</code>\n\n"
                    f"<i>Faster than your dating life! 💨</i>",

                    f"<b>⚡ MESSAGE SENT TO SHADOW REALM!</b>\n\n"
                    f"• Victim ID: <code>{target_msg.message_id}</code>\n"
                    f"• Time of Death: <code>{elapsed:.2f}s</code>\n\n"
                    f"<i>Another one bites the dust! 👻</i>",

                    f"<b>💥 DELETION COMPLETE!</b>\n\n"
                    f"• Destroyed: <code>{target_msg.message_id}</code>\n"
                    f"• Speed: <code>{elapsed:.2f}s</code>\n\n"
                    f"<i>Gone, reduced to atoms! ⚛️</i>"
                ]

                status_message = await update.effective_chat.send_message(
                    random.choice(savage_messages),
                    parse_mode=ParseMode.HTML
                )
                await self._delete_after_delay(status_message, 3)

        except TelegramError as e:
            if not silent_mode:
                error_msg = await update.message.reply_text(
                    f"<b>💩 TASK FAILED SUCCESSFULLY!</b>\n\n"
                    f"<code>{str(e)}</code>\n\n"
                    f"<i>Even my grandma deletes messages better than this! 👵</i>",
                    parse_mode=ParseMode.HTML
                )
                await self._delete_after_delay(error_msg, 5)

    async def purge(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Mass message deletion with style."""
        if not await self._check_admin_rights(update, context):
            return

        if not update.message.reply_to_message:
            help_msg = await update.message.reply_text(
                "<b>🧹 MASS DESTRUCTION GUIDE</b>\n\n"
                "<b>How to nuke chat history:</b>\n"
                "1️⃣ Reply to oldest message with <code>/purge</code>\n"
                "2️⃣ Optional limit: <code>/purge 50</code>\n"
                "3️⃣ Spicy options:\n"
                "  • <code>-s</code> - Stealth mode\n"
                "  • <code>-m</code> - Media massacre only\n"
                "  • <code>-u @user</code> - Target specific victim\n\n"
                "<i>Example: <code>/purge 100 -s -u @CaptainChaos</code></i>\n\n"
                "<i>Time to take out the trash! 🚮</i>",
                parse_mode=ParseMode.HTML
            )
            await self._delete_after_delay(help_msg, 10)
            return

        try:
            args = context.args
            limit = next((int(arg) for arg in args if arg.isdigit()), None)
            silent_mode = '-s' in args
            media_only = '-m' in args
            target_user = next((args[i + 1].replace('@', '') for i, arg in enumerate(args) if arg == '-u' and i + 1 < len(args)), None)

            processing_msg = None
            if not silent_mode:
                savage_starts = [
                    "<b>🌋 INITIATING MASS EXTINCTION!</b>\n<i>Dinosaurs had it easier...</i>",
                    "<b>⚡ CHARGING DELETION BEAM!</b>\n<i>Set phasers to obliterate!</i>",
                    "<b>🧹 SUMMONING CLEAN SWEEP!</b>\n<i>Watch messages disappear like my will to live!</i>"
                ]
                processing_msg = await update.message.reply_text(
                    random.choice(savage_starts),
                    parse_mode=ParseMode.HTML
                )

            # Calculate message range
            start_id = update.message.reply_to_message.message_id
            end_id = update.message.message_id
            
            if limit:
                start_id = max(start_id, end_id - limit)

            deleted_count = 0
            skipped_count = 0
            
            # Create message ID list
            message_ids = range(start_id, end_id + 1)
            
            # Process in smaller batches
            batch_size = 100
            for i in range(0, len(message_ids), batch_size):
                batch = list(message_ids[i:i + batch_size])
                delete_tasks = []

                for msg_id in batch:
                    try:
                        if media_only or target_user:
                            try:
                                msg = await context.bot.get_messages(
                                    chat_id=update.effective_chat.id,
                                    message_ids=msg_id
                                )
                                if not msg:
                                    skipped_count += 1
                                    continue
                                
                                if media_only and not any([
                                    msg.audio, msg.document, msg.photo,
                                    msg.video, msg.voice, msg.video_note,
                                    msg.sticker, msg.animation
                                ]):
                                    skipped_count += 1
                                    continue
                                
                                if target_user and msg.from_user and msg.from_user.username != target_user:
                                    skipped_count += 1
                                    continue
                                
                            except TelegramError:
                                skipped_count += 1
                                continue

                        delete_tasks.append(
                            context.bot.delete_message(
                                chat_id=update.effective_chat.id,
                                message_id=msg_id
                            )
                        )
                    except TelegramError:
                        skipped_count += 1
                        continue

                if delete_tasks:
                    results = await asyncio.gather(*delete_tasks, return_exceptions=True)
                    deleted_count += sum(1 for r in results if not isinstance(r, Exception))
                    skipped_count += sum(1 for r in results if isinstance(r, Exception))

                # Update progress message every 5 batches
                if processing_msg and i % (batch_size * 5) == 0:
                    try:
                        await processing_msg.edit_text(
                            f"<b>🚀 PURGE IN PROGRESS...</b>\n\n"
                            f"• Deleted: <code>{deleted_count}</code>\n"
                            f"• Skipped: <code>{skipped_count}</code>\n"
                            f"• Progress: <code>{i}/{len(message_ids)}</code>\n\n"
                            f"<i>Still going strong! 💪</i>",
                            parse_mode=ParseMode.HTML
                        )
                    except TelegramError:
                        pass

            # Clean up processing message
            if processing_msg:
                try:
                    await processing_msg.delete()
                except TelegramError:
                    pass

            # Delete command message
            try:
                await update.message.delete()
            except TelegramError:
                pass

            if not silent_mode:
                elapsed_time = time.time() - update.message.date.timestamp()
                savage_endings = [
                    f"<b>🎯 MASSACRE COMPLETE!</b>\n\n"
                    f"• Messages Destroyed: <code>{deleted_count}</code>\n"
                    f"• Survivors: <code>{skipped_count}</code>\n"
                    f"• Genocide Time: <code>{elapsed_time:.2f}s</code>\n\n"
                    f"<i>Cleaner than your browser history! 🧼</i>",

                    f"<b>💥 NUCLEAR CLEANUP FINISHED!</b>\n\n"
                    f"• Vaporized: <code>{deleted_count}</code>\n"
                    f"• Escaped: <code>{skipped_count}</code>\n"
                    f"• Fallout Time: <code>{elapsed_time:.2f}s</code>\n\n"
                    f"<i>Chat looking fresh as your excuses! ✨</i>",

                    f"<b>⚡ PURGE SUCCESSFUL!</b>\n\n"
                    f"• Messages Yeeted: <code>{deleted_count}</code>\n"
                    f"• Lucky Survivors: <code>{skipped_count}</code>\n"
                    f"• Speed Run: <code>{elapsed_time:.2f}s</code>\n\n"
                    f"<i>Faster than your relationships! 💨</i>"
                ]

                status_message = await update.effective_chat.send_message(
                    random.choice(savage_endings),
                    parse_mode=ParseMode.HTML
                )
                await self._delete_after_delay(status_message, 3)

        except TelegramError as e:
            if processing_msg:
                try:
                    await processing_msg.delete()
                except TelegramError:
                    pass
                    
            if not silent_mode:
                error_msg = await update.message.reply_text(
                    f"<b>🤡 TASK FAILED SUCCESSFULLY!</b>\n\n"
                    f"<code>{str(e)}</code>\n\n"
                    f"<i>Failed harder than my New Year's resolutions! 💀</i>",
                    parse_mode=ParseMode.HTML
                )
                await self._delete_after_delay(error_msg, 5)

    async def pin_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Pin a message in the chat with style."""
        if not await self._check_admin_rights(update, context):
            return

        if not update.message.reply_to_message:
            await update.message.reply_text(
                "<b>📌 PINNING FOR DUMMIES</b>\n\n"
                "🎯 <b>How to make it stick:</b>\n"
                "1️⃣ Find a message worth immortalizing\n"
                "2️⃣ Reply with <code>/pin</code>\n\n"
                "🎮 <b>Pro gamer moves:</b>\n"
                "• <code>-q</code> - Ninja mode (stealth pin)\n"
                "• <code>-u</code> - Out with the old, in with the new\n"
                "• <code>-s</code> - No flex mode\n\n"
                "<i>Example: <code>/pin -q -s</code> (for the humble)</i>",
                parse_mode=ParseMode.HTML
            )
            return

        try:
            args = context.args
            quiet_mode = '-q' in args
            unpin_first = '-u' in args
            silent_mode = '-s' in args

            start_time = time.time()

            if unpin_first:
                try:
                    await context.bot.unpin_chat_message(update.effective_chat.id)
                except TelegramError:
                    if not silent_mode:
                        await update.message.reply_text("🤡 Previous pin is being stubborn!")
                        return

            await context.bot.pin_chat_message(
                chat_id=update.effective_chat.id,
                message_id=update.message.reply_to_message.message_id,
                disable_notification=quiet_mode
            )

            elapsed = time.time() - start_time

            if silent_mode:
                try:
                    await update.message.delete()
                    return
                except TelegramError:
                    pass

            pinned_msg = update.message.reply_to_message
            preview = ""
            if pinned_msg.text:
                preview = html.escape(pinned_msg.text[:50]) + "..." if len(pinned_msg.text) > 50 else html.escape(pinned_msg.text)
            elif pinned_msg.caption:
                preview = html.escape(pinned_msg.caption[:50]) + "..." if len(pinned_msg.caption) > 50 else html.escape(pinned_msg.caption)
            elif pinned_msg.effective_attachment:
                preview = "[Some spicy media content]"

            savage_success = [
                f"<b>📌 PINNED HARDER THAN YOUR CRUSH'S REJECTION!</b>\n\n"
                f"<b>The goods:</b> <i>{preview}</i>\n"
                f"<b>Stealth:</b> {'🥷 Quiet AF' if quiet_mode else '🔔 Loud & Proud'}\n"
                f"<b>Old pin:</b> {'☠️ Murdered' if unpin_first else '🤝 Spared'}\n"
                f"<b>Speed:</b> <code>{elapsed:.2f}s</code> (faster than your comebacks)\n\n"
                f"<i>Slide up to witness greatness! ⬆️</i>",

                f"<b>📌 MESSAGE ASCENDED TO GODHOOD!</b>\n\n"
                f"<b>Chosen one:</b> <i>{preview}</i>\n"
                f"<b>Mode:</b> {'🤫 Sneaky beaky' if quiet_mode else '📢 Maximum chaos'}\n"
                f"<b>Casualty:</b> {'💀 Previous pin got yeeted' if unpin_first else '😎 Previous pin survived'}\n"
                f"<b>Time:</b> <code>{elapsed:.2f}s</code> (speed of light who?)\n\n"
                f"<i>It's all uphill from here! ⬆️</i>"
            ]

            status_message = await update.message.reply_text(
                random.choice(savage_success),
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(3)
            await status_message.delete()

        except TelegramError as e:
            await update.message.reply_text(
                f"<b>💀 TASK FAILED SUCCESSFULLY!</b>\n\n"
                f"<code>{str(e)}</code>\n\n"
                f"<i>Even my ex was less difficult than this pin attempt!</i>",
                parse_mode=ParseMode.HTML
            )

    async def unpin_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Unpin a message with attitude."""
        if not await self._check_admin_rights(update, context):
            return

        if not update.message.reply_to_message:
            await update.message.reply_text(
                "<b>📌 UNPINNING 101</b>\n\n"
                "🎯 <b>How to drop the pin:</b>\n"
                "1️⃣ Find that pinned message you regret\n"
                "2️⃣ Reply with <code>/unpin</code>\n\n"
                "🎮 <b>Secret technique:</b>\n"
                "• <code>-s</code> - Ghost mode activated\n\n"
                "<i>Example: <code>/unpin -s</code> (like your ex)</i>",
                parse_mode=ParseMode.HTML
            )
            return

        try:
            silent_mode = '-s' in context.args
            start_time = time.time()

            await context.bot.unpin_chat_message(
                chat_id=update.effective_chat.id,
                message_id=update.message.reply_to_message.message_id
            )

            if not silent_mode:
                savage_unpins = [
                    f"<b>🎯 PIN ELIMINATED!</b>\n\n"
                    f"<b>Time:</b> <code>{time.time() - start_time:.2f}s</code>\n"
                    f"<i>Faster than your DMs getting ignored! 💨</i>",

                    f"<b>💀 PIN SENT TO SHADOW REALM!</b>\n\n"
                    f"<b>Execution time:</b> <code>{time.time() - start_time:.2f}s</code>\n"
                    f"<i>Another one bites the dust! 👻</i>"
                ]
                status_message = await update.message.reply_text(
                    random.choice(savage_unpins),
                    parse_mode=ParseMode.HTML
                )
                await asyncio.sleep(3)
                await status_message.delete()

        except TelegramError as e:
            if not silent_mode:
                await update.message.reply_text(
                    f"<b>🤡 BRUH MOMENT!</b>\n\n"
                    f"<code>{str(e)}</code>\n\n"
                    f"<i>This pin is more stubborn than my ex! 💀</i>",
                    parse_mode=ParseMode.HTML
                )

    async def unpin_all_messages(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Nuclear option for pins."""
        if not await self._check_admin_rights(update, context):
            return

        silent_mode = '-s' in context.args
        processing_msg = None

        if not silent_mode:
            savage_starts = [
                "<b>☢️ INITIATING PIN GENOCIDE...</b>\n"
                "<i>Pins about to disappear like my Boss!</i>",
                
                "<b>🌋 LAUNCHING PIN APOCALYPSE...</b>\n"
                "<i>Pins gonna vanish faster than my savings!</i>"
            ]
            processing_msg = await update.message.reply_text(
                random.choice(savage_starts),
                parse_mode=ParseMode.HTML
            )

        try:
            start_time = time.time()
            await context.bot.unpin_all_chat_messages(update.effective_chat.id)
            elapsed = time.time() - start_time

            if processing_msg:
                await processing_msg.delete()

            if not silent_mode:
                savage_endings = [
                    f"<b>💥 MASS EXTINCTION COMPLETE!</b>\n\n"
                    f"• Execution Time: <code>{elapsed:.2f}s</code>\n"
                    f"<i>Pins got Thanos snapped! Perfectly balanced! ✨</i>",

                    f"<b>☠️ PINS WENT BYE BYE!</b>\n\n"
                    f"• Time to obliterate: <code>{elapsed:.2f}s</code>\n"
                    f"<i>Cleaned harder than my browser history! 🧹</i>"
                ]
                status_message = await update.effective_chat.send_message(
                    random.choice(savage_endings),
                    parse_mode=ParseMode.HTML
                )
                await asyncio.sleep(3)
                await status_message.delete()

        except TelegramError as e:
            if processing_msg:
                await processing_msg.delete()
            if not silent_mode:
                await update.message.reply_text(
                    f"<b>🤡 MISSION FAILED SUCCESSFULLY!</b>\n\n"
                    f"<code>{str(e)}</code>\n\n"
                    f"<i>Even my life is less messy than this attempt! 💀</i>",
                    parse_mode=ParseMode.HTML
                )

    async def _get_user_by_username(self, username: str, context: ContextTypes.DEFAULT_TYPE, chat_id: int) -> Optional[ChatMember]:
        """Enhanced helper method to get user by username or ID with multiple fallback strategies."""
        try:
            # Clean the username
            clean_username = username.lstrip('@').lower()
            
            # Strategy 1: Direct chat lookup
            try:
                user = await context.bot.get_chat(f"@{clean_username}")
                return await context.bot.get_chat_member(chat_id, user.id)
            except TelegramError:
                pass

            # Strategy 2: Numeric ID lookup
            try:
                if clean_username.isdigit():
                    return await context.bot.get_chat_member(chat_id, int(clean_username))
            except TelegramError:
                pass

            # Strategy 3: Chat administrators lookup
            try:
                admins = await context.bot.get_chat_administrators(chat_id)
                for member in admins:
                    if member.user.username and member.user.username.lower() == clean_username:
                        return member
            except TelegramError:
                pass

            return None
        except Exception as e:
            logging.error(f"Error in _get_user_by_username: {e}")
            return None

    async def warn_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Drop warnings like they're hot 🔥"""
        if not await self._check_admin_rights(update, context):
            return

        if not update.message.reply_to_message:
            await update.message.reply_text(
                "<b>🤦‍♂️ BRUH MOMENT!</b>\n\n"
                "Reply to someone's message first!\n"
                "Even my grandma knows how to use /warn better!\n\n"
                "<code>/warn [reason for the drama]</code>",
                parse_mode=ParseMode.HTML
            )
            return

        user_to_warn = update.message.reply_to_message.from_user
        reason = " ".join(context.args) if context.args else "Being too spicy for their own good"

        try:
            chat_member = await context.bot.get_chat_member(update.effective_chat.id, user_to_warn.id)
            if chat_member.status in ["administrator", "creator"]:
                savage_admin_responses = [
                    "❌ Nice try, but admins have diplomatic immunity! 🛡️",
                    "❌ Warning admins? That's like trying to outpizza the hut! 🍕",
                    "❌ Admins are like Nokia 3310 - unbreakable! 📱"
                ]
                await update.message.reply_text(
                    random.choice(savage_admin_responses),
                    parse_mode=ParseMode.HTML
                )
                return

            current_warns = await self.db.get_user_warn_count(update.effective_chat.id, user_to_warn.id)
            new_warns = current_warns + 1
            max_warns = 5

            await self.db.add_warning_sqlite(
                chat_id=update.effective_chat.id,
                user_id=user_to_warn.id,
                admin_id=update.effective_user.id,
                reason=reason
            )

            savage_warn_messages = [
                f"<b>⚠️ CAUGHT IN 4K!</b>\n\n"
                f"<b>Troublemaker:</b> {user_to_warn.mention_html()}\n"
                f"<b>Fun Police:</b> {update.effective_user.mention_html()}\n"
                f"<b>The Tea:</b> {reason}\n"
                f"<b>Strike Count:</b> {new_warns}/{max_warns}\n\n",

                f"<b>⚠️ VIOLATION DETECTED!</b>\n\n"
                f"<b>Main Character:</b> {user_to_warn.mention_html()}\n"
                f"<b>Plot Twist By:</b> {update.effective_user.mention_html()}\n"
                f"<b>Crime Scene:</b> {reason}\n"
                f"<b>Life Count:</b> {new_warns}/{max_warns}\n\n"
            ]

            warn_message = random.choice(savage_warn_messages)

            if new_warns >= max_warns:
                ban_messages = [
                    "🚫 <b>GAME OVER!</b> Speedrunning bans any%!\n\n",
                    "🚫 <b>FATALITY!</b> Flawless victory!\n\n",
                    "🚫 <b>ACHIEVEMENT UNLOCKED:</b> Got banned in style!\n\n"
                ]
                warn_message += random.choice(ban_messages)
                await context.bot.ban_chat_member(update.effective_chat.id, user_to_warn.id)
                await self.db.reset_warnings(update.effective_chat.id, user_to_warn.id)
                await self.db.log_admin_action(
                    chat_id=update.effective_chat.id,
                    admin_id=context.bot.id,
                    action="ban",
                    target_user_id=user_to_warn.id,
                    reason=f"Exceeded maximum warnings ({max_warns})"
                )
                warn_message += (
                    f"<i>User has been banned! To appeal, hit up @NexusAI_HelpingAI_bot "
                    f"and use the /appeal command!</i>"
                )
                try:
                    await context.bot.send_message(
                        user_to_warn.id,
                        f"<b>🚫 You've Been Banned!</b>\n\n"
                        f"You were banned from <b>{update.effective_chat.title}</b> for too many warnings.\n\n"
                        f"<b>Want another chance? Here's how to appeal:</b>\n\n"
                        f"1️⃣ Make sure you're chatting with @NexusAI_HelpingAI_bot\n"
                        f"2️⃣ Send this command:\n"
                        f"<code>/appeal {update.effective_chat.id}</code>\n"
                        f"3️⃣ Add your appeal message explaining:\n"
                        f"   • Why you should be unbanned\n"
                        f"   • What you learned\n"
                        f"   • How you'll do better\n\n"
                        f"<i>The admins will review your appeal and get back to you!</i> 🙏",
                        parse_mode=ParseMode.HTML
                    )
                except TelegramError:
                    pass
            else:
                remaining_warns = [
                    f"<i>⚠️ {max_warns - new_warns} more strikes until you're out! Choose your next moves carefully! 🎯</i>",
                    f"<i>⚠️ {max_warns - new_warns} warnings left before you get yeeted to the shadow realm! 👻</i>",
                    f"<i>⚠️ {max_warns - new_warns} more and you'll disappear faster than my Boss went for milk! 🥛</i>"
                ]
                warn_message += random.choice(remaining_warns)

            await update.message.reply_text(warn_message, parse_mode=ParseMode.HTML)

            try:
                await context.bot.send_message(
                    user_to_warn.id,
                    f"<b>⚠️ YO HEADS UP!</b>\n\n"
                    f"You just caught a warning in <b>{update.effective_chat.title}</b>!\n\n"
                    f"<b>Why:</b> {reason}\n"
                    f"<b>Warns:</b> {new_warns}/{max_warns}\n\n"
                    f"<i>Keep it cool fam, {max_warns - new_warns} more and you're out! 🙏</i>",
                    parse_mode=ParseMode.HTML
                )
            except TelegramError:
                pass

        except Exception as e:
            await update.message.reply_text(
                f"<b>💀 TASK FAILED SUCCESSFULLY!</b>\n\n"
                f"<code>{str(e)}</code>\n\n"
                f"<i>Even my code has trust issues!</i>",
                parse_mode=ParseMode.HTML
            )

    async def unwarn_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show mercy (but make it savage) 😈"""
        if not await self._check_admin_rights(update, context):
            savage_denied = [
                "🤡 <b>NICE TRY PEASANT!</b>\n\nYou have as much authority here as a potato has in a smartphone factory!",
                "😂 <b>CAUGHT IN 4K!</b>\n\nTrying to play admin? That's like a fish trying to teach birds how to fly!",
                "❌ <b>ACCESS DENIED!</b>\n\nCome back when you've earned your admin badge, young grasshopper!"
            ]
            await update.message.reply_text(random.choice(savage_denied), parse_mode=ParseMode.HTML)
            return

        if not context.args and not update.message.reply_to_message:
            savage_help = [
                "<b>🤦‍♂️ BRAIN.EXE HAS STOPPED WORKING!</b>\n\n"
                "Two braincell options:\n"
                "• Reply to message with /unwarn\n"
                "• Use /unwarn @their_username\n\n"
                "<i>Even my grandma's calculator could figure this out! 🧮</i>",

                "<b>💀 TASK FAILED SUCCESSFULLY!</b>\n\n"
                "Unwarn Guide for the Chronically Lost:\n"
                "• Find someone to forgive (harder than finding motivation on Monday)\n"
                "• /unwarn @username or reply to them\n\n"
                "<i>My pet rock learned this faster than you! 🪨</i>",

                "<b>🎯 MISSION IMPOSSIBLE!</b>\n\n"
                "How to unwarn:\n"
                "• Locate target (like finding Boss at the milk store)\n"
                "• /unwarn @username or reply\n\n"
                "<i>Need a map and compass too? 🧭</i>"
            ]
            await update.message.reply_text(random.choice(savage_help), parse_mode=ParseMode.HTML)
            return

        try:
            if update.message.reply_to_message:
                user = update.message.reply_to_message.from_user
                reason = " ".join(context.args) if context.args else "Feeling merciful today"
            else:
                username = context.args[0].lstrip('@')
                chat_member = await context.bot.get_chat_member(update.effective_chat.id, username)
                user = chat_member.user
                reason = " ".join(context.args[1:]) if len(context.args) > 1 else "Feeling merciful today"

            current_warns = await self.db.get_user_warn_count(update.effective_chat.id, user.id)
            if current_warns == 0:
                no_warns_responses = [
                    f"🤡 <b>BRUH MOMENT!</b>\n\n{user.mention_html()} is cleaner than my browser history!",
                    f"❌ <b>TASK FAILED SUCCESSFULLY!</b>\n\n{user.mention_html()} has no warnings! They're either a saint or just got here!",
                    f"😂 <b>SUFFERING FROM SUCCESS!</b>\n\n{user.mention_html()} is already warning-free! Want to give them a medal too?"
                ]
                await update.message.reply_text(random.choice(no_warns_responses), parse_mode=ParseMode.HTML)
                return

            success = await self.db.remove_warning(update.effective_chat.id, user.id)
            if success:
                await self.db.log_admin_action(
                    chat_id=update.effective_chat.id,
                    admin_id=update.effective_user.id,
                    action="unwarn",
                    target_user_id=user.id,
                    reason=reason
                )
                new_warns = await self.db.get_user_warn_count(update.effective_chat.id, user.id)
                
                savage_unwarn_messages = [
                    f"<b>🎉 PARDON GRANTED!</b>\n\n"
                    f"<b>Lucky Duck:</b> {user.mention_html()}\n"
                    f"<b>Merciful God:</b> {update.effective_user.mention_html()}\n"
                    f"<b>Why Though:</b> {reason}\n"
                    f"<b>Oopsies Left:</b> {new_warns}/5\n\n"
                    f"<i>Don't waste this second chance, or I'll make your ban speedrun look like a TikTok! 🏃‍♂️</i>",

                    f"<b>😇 SINS FORGIVEN!</b>\n\n"
                    f"<b>Reformed Sinner:</b> {user.mention_html()}\n"
                    f"<b>Guardian Angel:</b> {update.effective_user.mention_html()}\n"
                    f"<b>Divine Reason:</b> {reason}\n"
                    f"<b>Lives Left:</b> {new_warns}/5\n\n"
                    f"<i>Next time, I'm trading your warnings for Dogecoin! 🐕</i>",

                    f"<b>🎰 WARNING LOTTERY WINNER!</b>\n\n"
                    f"<b>Lucky Winner:</b> {user.mention_html()}\n"
                    f"<b>Generous Sponsor:</b> {update.effective_user.mention_html()}\n"
                    f"<b>Plot Twist:</b> {reason}\n"
                    f"<b>Warnings Left:</b> {new_warns}/5\n\n"
                    f"<i>Don't make me regret this more than my ex regrets me! 💔</i>"
                ]

                await update.message.reply_text(random.choice(savage_unwarn_messages), parse_mode=ParseMode.HTML)

                try:
                    dm_messages = [
                        f"<b>🎉 CONGRATULATIONS!</b>\n\n"
                        f"You've been unwarned in <b>{update.effective_chat.title}</b>!\n"
                        f"<b>Reason:</b> {reason}\n"
                        f"<b>Warnings:</b> {new_warns}/5\n\n"
                        f"<i>Don't mess up or get ready for ban speedrun any%! 🏃‍♂️</i>",

                        f"<b>🎯 SECOND CHANCE UNLOCKED!</b>\n\n"
                        f"Warning removed in <b>{update.effective_chat.title}</b>!\n"
                        f"<b>Reason:</b> {reason}\n"
                        f"<b>Warnings:</b> {new_warns}/5\n\n"
                        f"<i>Keep it clean or get ready for ban speedrun any%! 🏃‍♂️</i>"
                    ]
                    await context.bot.send_message(
                        user.id,
                        random.choice(dm_messages),
                        parse_mode=ParseMode.HTML
                    )
                except TelegramError:
                    pass

            else:
                await update.message.reply_text(
                    f"<b>💀 TASK FAILED SUCCESSFULLY!</b>\n\n"
                    f"Failed to unwarn {user.mention_html()}!\n"
                    f"<i>Even my code has trust issues!</i>",
                    parse_mode=ParseMode.HTML
                )

        except Exception as e:
            await update.message.reply_text(
                "<b>❌ Error!</b>\n\n"
                "Make sure:\n"
                "• The username is correct\n"
                "• The user is in the group\n"
                "• You're using the right format:\n"
                "<code>/unwarn @username [reason]</code>",
                parse_mode=ParseMode.HTML
            )

    async def _find_user(self, context: ContextTypes.DEFAULT_TYPE, chat_id: int, target: str) -> Optional[ChatMember]:
        """Enhanced user finding helper method."""
        clean_target = target.lstrip('@')
        try:
            if clean_target.isdigit():
                return await context.bot.get_chat_member(chat_id, int(clean_target))
            try:
                chat = await context.bot.get_chat(f"@{clean_target}")
                if chat and chat.id:
                    return await context.bot.get_chat_member(chat_id, chat.id)
            except TelegramError:
                pass
            return None
        except TelegramError:
            return None

    async def _check_admin_rights(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> bool:
        """Check if the user has admin rights."""
        try:
            user = await context.bot.get_chat_member(
                update.effective_chat.id,
                update.effective_user.id
            )
            return user.status in ["administrator", "creator"]
        except TelegramError:
            await update.message.reply_text("❌ Failed to verify admin rights.")
            return False

    def _format_duration(self, seconds: int) -> str:
        """Format duration in seconds to a human-readable string."""
        if seconds == 0:
            return "permanently"
        
        units = [
            (86400, "day"),
            (3600, "hour"),
            (60, "minute"),
            (1, "second")
        ]
        
        parts = []
        for unit_seconds, unit_name in units:
            if seconds >= unit_seconds:
                count = seconds // unit_seconds
                seconds %= unit_seconds
                parts.append(f"{count} {unit_name}{'s' if count > 1 else ''}")
            
        if len(parts) == 0:
            return "for a moment"
        
        if len(parts) == 1:
            return f"for {parts[0]}"
        
        return f"for {', '.join(parts[:-1])} and {parts[-1]}"

    async def promote_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Level up a peasant to admin status 👑"""
        if not update.effective_chat or not update.effective_message:
            return

        try:
            # Check if bot is admin
            bot_member = await context.bot.get_chat_member(update.effective_chat.id, context.bot.id)
            if not bot_member.status in [ChatMemberStatus.ADMINISTRATOR, ChatMemberStatus.OWNER]:
                savage_bot_responses = [
                    "🤖 <b>BRUH! I'M POWERLESS!</b>\n\n"
                    "<i>Make me admin first or I'm just a fancy calculator!</i>",
                    
                    "❌ <b>ERROR 404: ADMIN RIGHTS NOT FOUND!</b>\n\n"
                    "<i>Promote me first, then we'll talk business!</i>",
                    
                    "😤 <b>NO ADMIN? NO SERVICE!</b>\n\n"
                    "<i>Give me admin rights or watch me do absolutely nothing!</i>"
                ]
                await update.message.reply_text(
                    random.choice(savage_bot_responses),
                    parse_mode=ParseMode.HTML
                )
                return
            
            # Check if bot has promote rights
            if isinstance(bot_member, ChatMemberAdministrator) and not bot_member.can_promote_members:
                await update.message.reply_text(
                    "🤡 <b>MISSION FAILED SUCCESSFULLY!</b>\n\n"
                    "<i>I need 'Add new admins' permission!\n"
                    "What am I supposed to do? Promote them with positive vibes?</i>",
                    parse_mode=ParseMode.HTML
                )
                return

            # Check user rights
            user = await context.bot.get_chat_member(update.effective_chat.id, update.effective_user.id)
            if not (user.status == ChatMemberStatus.OWNER or 
                    (isinstance(user, ChatMemberAdministrator) and user.can_promote_members)):
                savage_denied = [
                    "🚫 <b>NICE TRY BESTIE!</b>\n\n"
                    "<i>But you're about as qualified to promote as I am to be an astronaut! 🚀</i>",
                    
                    "❌ <b>PROMOTION MACHINE BROKE!</b>\n\n"
                    "<i>Come back when you've got admin rights in your inventory!</i>",
                    
                    "😂 <b>CAUGHT IN 4K!</b>\n\n"
                    "<i>Trying to promote without rights? That's like trying to swim in a desert!</i>"
                ]
                await update.message.reply_text(
                    random.choice(savage_denied),
                    parse_mode=ParseMode.HTML
                )
                return

            # Get target user
            target_user = None
            custom_title = ""
            
            if update.message.reply_to_message:
                target_user = update.message.reply_to_message.from_user
                custom_title = " ".join(context.args)[:16] if context.args else ""
            elif context.args and context.args[0]:
                try:
                    # Try to get user by ID or username
                    user_id = context.args[0]
                    try:
                        # First try as a numeric ID
                        user_id = int(user_id)
                    except ValueError:
                        # If not a number, it might be a username without @
                        if not user_id.startswith('@'):
                            user_id = '@' + user_id
                    
                    try:
                        member = await context.bot.get_chat_member(update.effective_chat.id, user_id)
                        target_user = member.user
                        custom_title = " ".join(context.args[1:])[:16] if len(context.args) > 1 else ""
                    except TelegramError as e:
                        await update.message.reply_text(
                            f"❌ <b>USER NOT FOUND!</b>\n\n"
                            f"<i>Error: {str(e)}</i>\n\n"
                            f"<i>Make sure the user is in this chat.</i>",
                            parse_mode=ParseMode.HTML
                        )
                        return
                except (ValueError, TelegramError) as e:
                    await update.message.reply_text(
                        "🤦‍♂️ <b>TASK FAILED SUCCESSFULLY!</b>\n\n"
                        "<i>To promote someone, either:\n"
                        "1️⃣ Reply to their message with</i> <code>/promote [title]</code>\n"
                        "2️⃣ Use their ID or username:</i> <code>/promote [user_id/username] [title]</code>\n\n"
                        "<i>Example with ID:</i> <code>/promote 123456789 Cool Admin</code>\n"
                        f"<i>Error details: {str(e)}</i>",
                        parse_mode=ParseMode.HTML
                    )
                    return
            else:
                await update.message.reply_text(
                    "🤦‍♂️ <b>TASK FAILED SUCCESSFULLY!</b>\n\n"
                    "<i>To promote someone, either:\n"
                    "1️⃣ Reply to their message with</i> <code>/promote [title]</code>\n"
                    "2️⃣ Use their ID or username:</i> <code>/promote [user_id/username] [title]</code>\n\n"
                    "<i>Example with ID:</i> <code>/promote 123456789 Cool Admin</code>",
                    parse_mode=ParseMode.HTML
                )
                return
                
            # Check if target is already admin
            target_member = await context.bot.get_chat_member(update.effective_chat.id, target_user.id)
            if target_member.status in [ChatMemberStatus.ADMINISTRATOR, ChatMemberStatus.OWNER]:
                await update.message.reply_text(
                    "🤡 <b>BRUH MOMENT!</b>\n\n"
                    "<i>They're already an admin! What are you trying to do, promote them to super admin?</i>",
                    parse_mode=ParseMode.HTML
                )
                return

            # Promote the chosen one
            try:
                # Structure for the new API
                promote_kwargs = {
                    'chat_id': update.effective_chat.id,
                    'user_id': target_user.id,
                    'is_anonymous': False,  # Add this line for clarity
                }
                
                # Check if we can use the newer API
                if hasattr(bot_member, 'privileges'):
                    # New API style (PTB 20.0+)
                    promote_kwargs['privileges'] = ChatAdministratorRights(
                        can_change_info=True,
                        can_delete_messages=True,
                        can_invite_users=True,
                        can_restrict_members=True,
                        can_pin_messages=True,
                        can_manage_chat=True,
                        can_manage_video_chats=True,
                        # Be careful with these permissions
                        can_promote_members=False,  # Usually safer to keep this False
                        is_anonymous=False,
                    )
                else:
                    # Older API style
                    promote_kwargs.update({
                        'can_change_info': True,
                        'can_delete_messages': True,
                        'can_invite_users': True,
                        'can_restrict_members': True,
                        'can_pin_messages': True,
                        'can_manage_chat': True,
                        'can_manage_video_chats': True,
                        'can_promote_members': False,  # Usually safer to keep this False
                    })

                # Try to promote the user
                await context.bot.promote_chat_member(**promote_kwargs)

                # Set custom title if provided
                if custom_title:
                    try:
                        await context.bot.set_chat_administrator_custom_title(
                            chat_id=update.effective_chat.id,
                            user_id=target_user.id,
                            custom_title=custom_title
                        )
                    except TelegramError as e:
                        await update.message.reply_text(
                            f"✅ <b>PROMOTED BUT...</b>\n\n"
                            f"<i>User promoted, but couldn't set custom title: {str(e)}</i>",
                            parse_mode=ParseMode.HTML
                        )
                
                # Format success message
                title_part = f"\n<b>Special Title:</b> <code>{escape(custom_title)}</code>" if custom_title else ""
                
                promotion_messages = [
                    f"👑 <b>NEW ADMIN JUST DROPPED!</b>\n\n"
                    f"<b>Lucky One:</b> {target_user.mention_html()}\n"
                    f"<b>Promoted By:</b> {update.effective_user.mention_html()}"
                    f"{title_part}",

                    f"⚡ <b>UNLIMITED POWER UNLOCKED!</b>\n\n"
                    f"<b>Power Recipient:</b> {target_user.mention_html()}\n"
                    f"<b>Power Giver:</b> {update.effective_user.mention_html()}"
                    f"{title_part}",

                    f"🎮 <b>ADMIN MODE: ACTIVATED!</b>\n\n"
                    f"<b>Player 1:</b> {target_user.mention_html()}\n"
                    f"<b>Game Master:</b> {update.effective_user.mention_html()}"
                    f"{title_part}"
                ]

                msg = await update.message.reply_text(
                    random.choice(promotion_messages),
                    parse_mode=ParseMode.HTML
                )
                # Add auto-deletion after 30 seconds
                await self._delete_after_delay(msg, 30)

            except TelegramError as e:
                error_msg = str(e)
                if "right_forbidden" in error_msg.lower() or "not enough rights" in error_msg.lower():
                    error_msg = "I don't have enough permissions to promote users! Make sure I have 'Add new admins' permission!"
                elif "chat_admin_required" in error_msg.lower():
                    error_msg = "I need admin rights! Make me admin or I'll cry! 😭"
                elif "user_not_mutual_contact" in error_msg.lower() or "user_not_participant" in error_msg.lower():
                    error_msg = "This user needs to be in the chat first!"
                
                await update.message.reply_text(
                    f"💀 <b>TASK FAILED SUCCESSFULLY!</b>\n\n"
                    f"<i>{escape(error_msg)}</i>",
                    parse_mode=ParseMode.HTML
                )

        except Exception as e:
            logging.error(f"Error in promote_user: {str(e)}", exc_info=True)
            await update.message.reply_text(
                f"❌ <b>An unexpected error occurred</b>\n\n"
                f"<i>Error: {str(e)}</i>\n\n"
                f"<i>This message will self-destruct in 5 seconds...</i>",
                parse_mode=ParseMode.HTML
            )
            # Add auto-deletion for error message too
            await self._delete_after_delay(update.message.reply_text, 5)

    async def demote_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Send them back to the shadow realm 👻"""
        if not update.effective_chat or not update.effective_message:
            return

        try:
            # Bot admin check
            bot_member = await update.effective_chat.get_member(context.bot.id)
            if not bot_member.status == ChatMemberStatus.ADMINISTRATOR:
                await update.message.reply_text(
                    "😤 <b>I'M JUST A PEASANT!</b>\n\n"
                    "<i>Give me admin rights first, then we can play god!</i>",
                    parse_mode=ParseMode.HTML
                )
                return
            
            if isinstance(bot_member, ChatMemberAdministrator) and not bot_member.can_promote_members:
                await update.message.reply_text(
                    "🎭 <b>PLOT TWIST!</b>\n\n"
                    "<i>I need 'Add new admins' permission to remove them!\n"
                    "Life's full of ironies, isn't it?</i>",
                    parse_mode=ParseMode.HTML
                )
                return

            # User rights check
            user = await update.effective_chat.get_member(update.effective_user.id)
            if not (user.status == ChatMemberStatus.OWNER or 
                    (isinstance(user, ChatMemberAdministrator) and user.can_promote_members)):
                savage_denied = [
                    "🚫 <b>NICE DREAM BRO!</b>\n\n"
                    "<i>But you can't demote anyone without admin rights!\n"
                    "Stay in your lane! 🛵</i>",
                    
                    "❌ <b>ERROR 401: UNAUTHORIZED!</b>\n\n"
                    "<i>You're not that guy, pal. You're not that guy! 🎭</i>",
                    
                    "😂 <b>CAUGHT LACKING!</b>\n\n"
                    "<i>Nice try, but you need admin rights first!\n"
                    "Better luck next time! 🎲</i>"
                ]
                await update.message.reply_text(
                    random.choice(savage_denied),
                    parse_mode=ParseMode.HTML
                )
                return

            # Get target
            target_user = None
            
            if update.message.reply_to_message:
                target_user = update.message.reply_to_message.from_user
            elif context.args:
                query = context.args[0].replace('@', '')
                member = await self._find_user(context, update.effective_chat.id, query)
                if member:
                    target_user = member.user
                else:
                    await update.message.reply_text(
                        f"🕵️ <b>WHO DIS?</b>\n\n"
                        f"<i>Can't find @{escape(query)}!\n"
                        f"They're playing hide and seek or what? 🎮</i>",
                        parse_mode=ParseMode.HTML
                    )
                    return
            else:
                await update.message.reply_text(
                    "🤦‍♂️ <b>BRUH MOMENT!</b>\n\n"
                    "<code>/demote [username]</code>\n"
                    "<i>Or reply to their message with /demote\n"
                    "Even my pet rock knows how to do this! 🪨</i>",
                    parse_mode=ParseMode.HTML
                )
                return

            # Owner check
            chat_member = await context.bot.get_chat_member(update.effective_chat.id, target_user.id)
            if chat_member.status == ChatMemberStatus.OWNER:
                await update.message.reply_text(
                    "💀 <b>MISSION IMPOSSIBLE!</b>\n\n"
                    "<i>Trying to demote the owner?\n"
                    "That's like trying to delete System32! 🖥️</i>",
                    parse_mode=ParseMode.HTML
                )
                return

            if chat_member.status != ChatMemberStatus.ADMINISTRATOR:
                await update.message.reply_text(
                    "🤡 <b>TASK FAILED SUCCESSFULLY!</b>\n\n"
                    "<i>They're already as demoted as my social life!\n"
                    "Can't demote what's already at rock bottom! 📉</i>",
                    parse_mode=ParseMode.HTML
                )
                return

            # Execute order 66 - demote the user
            demote_kwargs = {
                'chat_id': update.effective_chat.id,
                'user_id': target_user.id,
                'is_anonymous': False,
            }

            if hasattr(bot_member, 'privileges'):
                # New API style
                demote_kwargs['privileges'] = ChatAdministratorRights(
                    can_change_info=False,
                    can_delete_messages=False,
                    can_invite_users=False,
                    can_restrict_members=False,
                    can_pin_messages=False,
                    can_manage_chat=False,
                    can_manage_video_chats=False,
                    can_post_messages=False,
                    can_edit_messages=False,
                    can_promote_members=False,
                    is_anonymous=False,
                )
            else:
                # Older API style
                demote_kwargs.update({
                    'can_change_info': False,
                    'can_delete_messages': False,
                    'can_invite_users': False,
                    'can_restrict_members': False,
                    'can_pin_messages': False,
                    'can_manage_chat': False,
                    'can_manage_video_chats': False,
                    'can_post_messages': False,
                    'can_edit_messages': False,
                    'can_promote_members': False,
                })

            await context.bot.promote_chat_member(**demote_kwargs)

            demotion_messages = [
                f"👻 <b>ADMIN CARD: REVOKED!</b>\n\n"
                f"<b>Former Admin:</b> {target_user.mention_html()}\n"
                f"<b>Executed By:</b> {update.effective_user.mention_html()}\n"
                f"<i>Back to peasant life! 🧑‍🌾</i>",

                f"📉 <b>ADMIN.EXE HAS STOPPED WORKING!</b>\n\n"
                f"<b>Demoted User:</b> {target_user.mention_html()}\n"
                f"<b>Responsible:</b> {update.effective_user.mention_html()}\n"
                f"<i>From hero to zero real quick! ⚡</i>",

                f"💀 <b>ADMIN PRIVILEGES GONE, REDUCED TO ATOMS!</b>\n\n"
                f"<b>Victim:</b> {target_user.mention_html()}\n"
                f"<b>Thanos:</b> {update.effective_user.mention_html()}\n"
                f"<i>Reality is often disappointing! 🤌</i>"
            ]

            msg = await update.message.reply_text(
                random.choice(demotion_messages),
                parse_mode=ParseMode.HTML
            )
            # Add auto-deletion after 30 seconds
            await self._delete_after_delay(msg, 30)

        except TelegramError as e:
            error_msg = str(e)
            if "chat_admin_required" in error_msg.lower():
                error_msg = "I need admin powers! Without them, I'm just a fancy calculator! 🧮"
            await update.message.reply_text(
                f"⚠️ <b>TASK FAILED SUCCESSFULLY!</b>\n\n"
                f"<i>{escape(error_msg)}</i>\n"
                f"<i>Even my errors have errors! 🤖</i>",
                parse_mode=ParseMode.HTML
            )

    async def dwarn_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Delete message and warn user with style 🗑️"""
        if not await self._check_admin_rights(update, context):
            return

        if not update.message.reply_to_message:
            await update.message.reply_text(
                "<b>🗑️ DELETE & WARN GUIDE</b>\n\n"
                "How to delete and warn:\n"
                "1️⃣ Find the offensive message\n"
                "2️⃣ Reply with <code>/dwarn [reason]</code>\n\n"
                "<i>Even my grandma knows how to delete and warn! 🧑‍🦳</i>",
                parse_mode=ParseMode.HTML
            )
            return

        try:
            user_to_warn = update.message.reply_to_message.from_user
            reason = " ".join(context.args) if context.args else "Being too spicy for their own good"

            # Check if target is admin
            chat_member = await context.bot.get_chat_member(update.effective_chat.id, user_to_warn.id)
            if chat_member.status in ["administrator", "creator"]:
                savage_admin_responses = [
                    "❌ Nice try, but admins have diplomatic immunity! 🛡️",
                    "❌ Warning admins? That's like trying to outpizza the hut! 🍕",
                    "❌ Admins are like Nokia 3310 - unbreakable! 📱"
                ]
                await update.message.reply_text(
                    random.choice(savage_admin_responses),
                    parse_mode=ParseMode.HTML
                )
                return

            # Delete the message first
            try:
                await context.bot.delete_message(
                    chat_id=update.effective_chat.id,
                    message_id=update.message.reply_to_message.message_id
                )
            except TelegramError:
                await update.message.reply_text(
                    "❌ <b>DELETE FAILED!</b>\n\n"
                    "<i>I need 'Delete messages' permission to delete this!</i>",
                    parse_mode=ParseMode.HTML
                )
                return

            # Add warning
            current_warns = await self.db.get_user_warn_count(update.effective_chat.id, user_to_warn.id)
            new_warns = current_warns + 1
            max_warns = 5

            await self.db.add_warning_sqlite(
                chat_id=update.effective_chat.id,
                user_id=user_to_warn.id,
                admin_id=update.effective_user.id,
                reason=reason
            )

            savage_dwarn_messages = [
                f"<b>🗑️ MESSAGE DELETED & USER WARNED!</b>\n\n"
                f"<b>Troublemaker:</b> {user_to_warn.mention_html()}\n"
                f"<b>Fun Police:</b> {update.effective_user.mention_html()}\n"
                f"<b>The Tea:</b> {reason}\n"
                f"<b>Strike Count:</b> {new_warns}/{max_warns}\n\n",

                f"<b>💥 DOUBLE TROUBLE!</b>\n\n"
                f"<b>Main Character:</b> {user_to_warn.mention_html()}\n"
                f"<b>Plot Twist By:</b> {update.effective_user.mention_html()}\n"
                f"<b>Crime Scene:</b> {reason}\n"
                f"<b>Life Count:</b> {new_warns}/{max_warns}\n\n"
            ]

            warn_message = random.choice(savage_dwarn_messages)

            if new_warns >= max_warns:
                ban_messages = [
                    "🚫 <b>GAME OVER!</b> Speedrunning bans any%!\n\n",
                    "🚫 <b>FATALITY!</b> Flawless victory!\n\n",
                    "🚫 <b>ACHIEVEMENT UNLOCKED:</b> Got banned in style!\n\n"
                ]
                warn_message += random.choice(ban_messages)
                await context.bot.ban_chat_member(update.effective_chat.id, user_to_warn.id)
                await self.db.reset_warnings(update.effective_chat.id, user_to_warn.id)
                await self.db.log_admin_action(
                    chat_id=update.effective_chat.id,
                    admin_id=context.bot.id,
                    action="ban",
                    target_user_id=user_to_warn.id,
                    reason=f"Exceeded maximum warnings ({max_warns})"
                )
                warn_message += (
                    f"<i>User has been banned! To appeal, hit up @NexusAI_HelpingAI_bot "
                    f"and use the /appeal command!</i>"
                )
                try:
                    await context.bot.send_message(
                        user_to_warn.id,
                        f"<b>🚫 You've Been Banned!</b>\n\n"
                        f"You were banned from <b>{update.effective_chat.title}</b> for too many warnings.\n\n"
                        f"<b>Want another chance? Here's how to appeal:</b>\n\n"
                        f"1️⃣ Make sure you're chatting with @NexusAI_HelpingAI_bot\n"
                        f"2️⃣ Send this command:\n"
                        f"<code>/appeal {update.effective_chat.id}</code>\n"
                        f"3️⃣ Add your appeal message explaining:\n"
                        f"   • Why you should be unbanned\n"
                        f"   • What you learned\n"
                        f"   • How you'll do better\n\n"
                        f"<i>The admins will review your appeal and get back to you!</i> 🙏",
                        parse_mode=ParseMode.HTML
                    )
                except TelegramError:
                    pass
            else:
                remaining_warns = [
                    f"<i>⚠️ {max_warns - new_warns} more strikes until you're out! Choose your next moves carefully! 🎯</i>",
                    f"<i>⚠️ {max_warns - new_warns} warnings left before you get yeeted to the shadow realm! 👻</i>",
                    f"<i>⚠️ {max_warns - new_warns} more and you'll disappear faster than my Boss went for milk! 🥛</i>"
                ]
                warn_message += random.choice(remaining_warns)

            await update.message.reply_text(warn_message, parse_mode=ParseMode.HTML)

            try:
                await context.bot.send_message(
                    user_to_warn.id,
                    f"<b>⚠️ YO HEADS UP!</b>\n\n"
                    f"You just caught a warning in <b>{update.effective_chat.title}</b>!\n\n"
                    f"<b>Why:</b> {reason}\n"
                    f"<b>Warns:</b> {new_warns}/{max_warns}\n\n"
                    f"<i>Keep it cool fam, {max_warns - new_warns} more and you're out! 🙏</i>",
                    parse_mode=ParseMode.HTML
                )
            except TelegramError:
                pass

        except Exception as e:
            await update.message.reply_text(
                f"<b>💀 TASK FAILED SUCCESSFULLY!</b>\n\n",
                parse_mode=ParseMode.HTML
            )

    async def swarn_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Silent warning (no DM) with style 🤫"""
        if not await self._check_admin_rights(update, context):
            return

        if not update.message.reply_to_message:
            await update.message.reply_text(
                "<b>🤫 SILENT WARNING GUIDE</b>\n\n"
                "How to silently warn:\n"
                "1️⃣ Find the target message\n"
                "2️⃣ Reply with <code>/swarn [reason]</code>\n\n"
                "<i>Like a ninja, but with warnings! 🥷</i>",
                parse_mode=ParseMode.HTML
            )
            return

        try:
            user_to_warn = update.message.reply_to_message.from_user
            reason = " ".join(context.args) if context.args else "Being too spicy for their own good"

            # Check if target is admin
            chat_member = await context.bot.get_chat_member(update.effective_chat.id, user_to_warn.id)
            if chat_member.status in ["administrator", "creator"]:
                savage_admin_responses = [
                    "❌ Nice try, but admins have diplomatic immunity! 🛡️",
                    "❌ Warning admins? That's like trying to outpizza the hut! 🍕",
                    "❌ Admins are like Nokia 3310 - unbreakable! 📱"
                ]
                await update.message.reply_text(
                    random.choice(savage_admin_responses),
                    parse_mode=ParseMode.HTML
                )
                return

            # Add warning
            current_warns = await self.db.get_user_warn_count(update.effective_chat.id, user_to_warn.id)
            new_warns = current_warns + 1
            max_warns = 5

            await self.db.add_warning_sqlite(
                chat_id=update.effective_chat.id,
                user_id=user_to_warn.id,
                admin_id=update.effective_user.id,
                reason=reason
            )

            savage_swarn_messages = [
                f"<b>🤫 SILENT WARNING DEPLOYED!</b>\n\n"
                f"<b>Target:</b> {user_to_warn.mention_html()}\n"
                f"<b>Stealth Admin:</b> {update.effective_user.mention_html()}\n"
                f"<b>Secret Reason:</b> {reason}\n"
                f"<b>Strike Count:</b> {new_warns}/{max_warns}\n\n",

                f"<b>🥷 NINJA WARNING STRIKE!</b>\n\n"
                f"<b>Victim:</b> {user_to_warn.mention_html()}\n"
                f"<b>Ninja:</b> {update.effective_user.mention_html()}\n"
                f"<b>Stealth Reason:</b> {reason}\n"
                f"<b>Life Count:</b> {new_warns}/{max_warns}\n\n"
            ]

            warn_message = random.choice(savage_swarn_messages)

            if new_warns >= max_warns:
                ban_messages = [
                    "🚫 <b>GAME OVER!</b> Speedrunning bans any%!\n\n",
                    "🚫 <b>FATALITY!</b> Flawless victory!\n\n",
                    "🚫 <b>ACHIEVEMENT UNLOCKED:</b> Got banned in style!\n\n"
                ]
                warn_message += random.choice(ban_messages)
                await context.bot.ban_chat_member(update.effective_chat.id, user_to_warn.id)
                await self.db.reset_warnings(update.effective_chat.id, user_to_warn.id)
                await self.db.log_admin_action(
                    chat_id=update.effective_chat.id,
                    admin_id=context.bot.id,
                    action="ban",
                    target_user_id=user_to_warn.id,
                    reason=f"Exceeded maximum warnings ({max_warns})"
                )
                warn_message += (
                    f"<i>User has been banned! To appeal, hit up @NexusAI_HelpingAI_bot "
                    f"and use the /appeal command!</i>"
                )
            else:
                remaining_warns = [
                    f"<i>⚠️ {max_warns - new_warns} more strikes until you're out! Choose your next moves carefully! 🎯</i>",
                    f"<i>⚠️ {max_warns - new_warns} warnings left before you get yeeted to the shadow realm! 👻</i>",
                    f"<i>⚠️ {max_warns - new_warns} more and you'll disappear faster than my Boss went for milk! 🥛</i>"
                ]
                warn_message += random.choice(remaining_warns)

            await update.message.reply_text(warn_message, parse_mode=ParseMode.HTML)

        except Exception as e:
            await update.message.reply_text(
                f"<b>💀 TASK FAILED SUCCESSFULLY!</b>\n\n",
                parse_mode=ParseMode.HTML
            )

    async def create_poll(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """
        Create polls with attitude! 📊
        
        Flags & Features:
        --multi : Let people be greedy and pick multiple options
        --anon  : For those who want to hide in the shadows
        --quiz  : Test their knowledge (add correct answer number after flag)
        --live  : Show live results as votes come in
        
        Format:
        /poll [flags] [minutes] question | option1 | option2 | ...
        
        Time to flex those polling muscles! 💪
        """
        if not update.effective_chat or not update.effective_message:
            return

        try:
            # Check if user is worthy (admin check)
            member = await context.bot.get_chat_member(
                update.effective_chat.id, 
                update.effective_user.id
            )
            
            if member.status not in [ChatMemberStatus.ADMINISTRATOR, ChatMemberStatus.OWNER]:
                await self._handle_non_admin_request(update)
                return

            args = context.args
            if not args:
                await self._show_poll_help(update)
                return

            # Parse poll parameters
            poll_params = self._parse_poll_parameters(args, update)
            
            if not poll_params:
                error_msg = await update.message.reply_text(
                    "❌ <b>INVALID POLL FORMAT!</b>\nCheck /poll for proper usage, genius! 🧠",
                    parse_mode=ParseMode.HTML
                )
                await self._delete_after_delay(error_msg, 10)
                await self._delete_after_delay(update.message, 10)
                return
                
            # Create and send the poll
            poll_msg = await context.bot.send_poll(**poll_params)
            
            # Send success message
            await self._send_poll_success_message(update, poll_params)

        except TelegramError as te:
            await self._handle_telegram_error(update, te)
        except Exception as e:
            logging.exception(f"Poll creation failed: {str(e)}")
            error_msg = await update.message.reply_text(
                "❌ <b>EPIC FAIL!</b>\n"
                f"<i>Error: {str(e)[:50]}...</i>\n"
                "<i>Even my code is laughing at this attempt! 🤖</i>",
                parse_mode=ParseMode.HTML
            )
            await self._delete_after_delay(error_msg, 10)
            await self._delete_after_delay(update.message, 10)

    async def _handle_non_admin_request(self, update: Update) -> None:
        """Handle requests from non-admin users with style."""
        peasant_responses = [
            "🚫 <b>Nice try, peasant!</b>\nOnly the chosen ones can create polls! 👑",
            "❌ <b>ERROR 404:</b> Admin privileges not found! 😂",
            "⛔ <b>Bruh!</b> You need admin powers for this flex! 💪",
            "🔒 <b>ACCESS DENIED!</b>\nCome back with admin badge or don't come back at all! 🛡️"
        ]
        error_msg = await update.message.reply_text(
            random.choice(peasant_responses),
            parse_mode=ParseMode.HTML
        )
        await self._delete_after_delay(error_msg, 10)
        await self._delete_after_delay(update.message, 10)

    async def _show_poll_help(self, update: Update) -> None:
        """Show comprehensive poll help with examples."""
        savage_help = (
            "🎯 <b>POLL CREATION 101 FOR DUMMIES!</b>\n\n"
            "<code>/poll [flags] [minutes] question | option1 | option2 | ...</code>\n"
            "<i>Duration is capped at 10 minutes due to Telegram limits.</i>\n\n"
            "<b>Flags:</b>\n"
            "• <code>--multi</code> - Multiple choice allowed\n"
            "• <code>--anon</code> - Anonymous voting\n"
            "• <code>--quiz N</code> - Quiz mode (N = correct option number)\n"
            "• <code>--live</code> - Show live results\n\n"
            "<b>Example Flexes:</b>\n"
            "• Basic Poll (10min):\n"
            "<code>/poll 10 Who's the coolest? | Me | Also Me | Definitely Me</code>\n\n"
            "• Multi-choice (5min):\n"
            "<code>/poll --multi 5 What's broken? | My life | My dreams | My code</code>\n\n"
            "• Anonymous Poll:\n"
            "<code>/poll --anon Who's the sus? | Red | Blue | Green</code>\n\n"
            "• Quiz Time (45min):\n"
            "<code>/poll --quiz 2 45 What's 1+1? | 11 | 2 | Window</code>\n\n"
            "• Live Results:\n"
            "<code>/poll --live --multi Best pizza toppings? | Cheese | Pepperoni | Pineapple</code>"
        )
        error_msg = await update.message.reply_text(savage_help, parse_mode=ParseMode.HTML)
        await self._delete_after_delay(error_msg, 20)  # Extended time to read the help
        await self._delete_after_delay(update.message, 20)

    def _parse_poll_parameters(self, args: List[str], update: Update) -> Optional[Dict]:
        """Parse poll parameters from command arguments."""
        # Initialize poll parameters
        multi_choice = False
        anonymous = False
        quiz_mode = False
        quiz_correct = None
        show_live_results = False
        new_args = []
        i = 0

        # Process flags
        while i < len(args):
            token = args[i]
            if token.startswith("--"):
                if token == "--multi":
                    multi_choice = True
                elif token == "--anon":
                    anonymous = True
                elif token == "--live":
                    show_live_results = True
                elif token == "--quiz":
                    quiz_mode = True
                    if i + 1 >= len(args) or not args[i + 1].isdigit():
                        return None  # Invalid quiz parameter
                    quiz_correct = int(args[i + 1])
                    i += 1
            else:
                new_args.append(token)
            i += 1

        # Parse duration and question
        duration = None
        if new_args and new_args[0].isdigit():
            duration = int(new_args[0])
            new_args = new_args[1:]

        poll_text = " ".join(new_args)
        parts = [x.strip() for x in poll_text.split("|")]
        
        if len(parts) < 2:
            return None  # Invalid poll format
        
        question = parts[0]
        options = parts[1:]

        if not (1 <= len(options) <= 10):
            return None  # Invalid number of options

        # Quiz validation
        if quiz_mode and not (1 <= quiz_correct <= len(options)):
            return None  # Invalid quiz correct answer

        # Create poll parameters
        poll_params = {
            "chat_id": update.effective_chat.id,
            "question": question,
            "options": options,
            "is_anonymous": anonymous,
            "allows_multiple_answers": multi_choice,
            "is_closed": False,
            "explanation": None,
            "explanation_parse_mode": None,
            "protect_content": False
        }
        
        # Add duration - using close_date instead of open_period for better compatibility
        if duration:
            # Telegram only allows automatic poll closing within 5-600 seconds
            # Clamp the duration to this limit and convert to seconds
            seconds = max(5, min(duration * 60, 600))
            poll_params["close_date"] = int((datetime.now(timezone.utc) + timedelta(seconds=seconds)).timestamp())

        # Add live results if requested (works with non-quiz polls)
        if show_live_results and not quiz_mode:
            poll_params["is_anonymous"] = False

        # Add quiz parameters if quiz mode
        if quiz_mode:
            poll_params["type"] = "quiz"
            poll_params["correct_option_id"] = quiz_correct - 1

        return poll_params

    async def _send_poll_success_message(self, update: Update, poll_params: Dict) -> None:
        """Send a stylish success message after poll creation."""
        success_messages = [
            "📊 <b>POLL DEPLOYED LIKE A BOSS!</b>",
            "🎯 <b>POLL DROPPED AND IT'S HOT!</b>",
            "💫 <b>BEHOLD, THE POLL OF DESTINY!</b>",
            "🚀 <b>POLL LAUNCHED INTO THE WILD!</b>"
        ]

        # Get poll details for the message
        close_date = poll_params.get("close_date", 0)
        if close_date:
            # Calculate remaining minutes from close_date
            remaining_minutes = int((close_date - datetime.now(timezone.utc).timestamp()) / 60)
            duration_text = f"\n⏰ <i>Closes in {remaining_minutes} minutes</i>"
        else:
            duration_text = "\n⏰ <i>Running indefinitely</i>"
        
        type_info = []
        
        if poll_params.get("type") == "quiz":
            type_info.append("🎮 <i>Quiz Mode</i>")
        
        if poll_params.get("allows_multiple_answers", False):
            type_info.append("👥 <i>Multi-Choice</i>")
        
        if poll_params.get("is_anonymous", False):
            type_info.append("🎭 <i>Anonymous</i>")
        else:
            type_info.append("👁️ <i>Visible Votes</i>")
        
        type_text = "\n" + " • ".join(type_info)

        success_msg = await update.message.reply_text(
            f"{random.choice(success_messages)}{duration_text}\n{type_text}",
            parse_mode=ParseMode.HTML
        )

        # Clean up after ourselves
        await self._delete_after_delay(success_msg, 15)
        await self._delete_after_delay(update.message, 15)

    async def _handle_telegram_error(self, update: Update, error: TelegramError) -> None:
        """Handle Telegram-specific errors with helpful messages."""
        error_message = "❌ <b>TELEGRAM SAID NO!</b>\n"
        
        if "not enough rights" in str(error).lower():
            error_message += "<i>I need more bot powers! Give me permission to send polls! 🦾</i>"
        elif "too many poll options" in str(error).lower():
            error_message += "<i>Too many options! Telegram can only handle 10! 🔢</i>"
        elif "poll question is too long" in str(error).lower():
            error_message += "<i>Your question is longer than my attention span! Keep it under 300 chars! 📏</i>"
        elif "poll option is too long" in str(error).lower():
            error_message += "<i>Options too long! Keep each under 100 chars! ✂️</i>"
        else:
            error_message += f"<i>{str(error)[:100]}</i>"
        
        error_msg = await update.message.reply_text(
            error_message,
            parse_mode=ParseMode.HTML
        )
        await self._delete_after_delay(error_msg, 15)

    # ==================== MISSING ADMIN COMMANDS ====================

    async def kick_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Kick users like they're footballs! ⚽"""
        if not await self._check_admin_rights(update, context):
            return

        chat_id = update.effective_chat.id
        target_user = await self._get_target_user(update, context)
        
        if not target_user:
            await update.message.reply_text(
                "🎯 <b>TARGET ACQUISITION FAILED!</b>\n\n"
                "Usage:\n"
                "• Reply to a message: <code>/kick [reason]</code>\n"
                "• Use username: <code>/kick @username [reason]</code>\n"
                "• Use ID: <code>/kick 123456789 [reason]</code>\n\n"
                "<i>Time to show someone the door! 🚪</i>",
                parse_mode=ParseMode.HTML
            )
            return

        # Check if trying to kick an admin
        try:
            member = await context.bot.get_chat_member(chat_id, target_user.id)
            if member.status in [ChatMemberStatus.ADMINISTRATOR, ChatMemberStatus.OWNER]:
                savage_admin_responses = [
                    "❌ Nice try, but admins have diplomatic immunity! 🛡️",
                    "❌ Kicking admins? That's like trying to delete System32! 💻",
                    "❌ Admins are like Nokia 3310 - unbreakable! 📱"
                ]
                await update.message.reply_text(
                    random.choice(savage_admin_responses),
                    parse_mode=ParseMode.HTML
                )
                return
        except Exception:
            pass

        # Get reason
        reason = " ".join(context.args[1:]) if len(context.args) > 1 else "No reason provided"
        
        try:
            # Kick the user (ban then unban)
            await context.bot.ban_chat_member(chat_id, target_user.id)
            await context.bot.unban_chat_member(chat_id, target_user.id)
            
            # Log the action
            await self.db.log_admin_action(
                chat_id=chat_id,
                admin_id=update.effective_user.id,
                action="kick",
                target_user_id=target_user.id,
                reason=reason
            )

            # Send savage kick notification
            kick_messages = [
                f"👢 <b>YEETED TO THE SHADOW REALM!</b>\n\n{target_user.mention_html()} got the boot!\n\n<b>Reason:</b> {reason}\n<b>Admin:</b> {update.effective_user.mention_html()}\n\n<i>They can come back, but their dignity stays here! 😂</i>",
                f"⚽ <b>GOAL!</b>\n\n{target_user.mention_html()} has been kicked out of the stadium!\n\n<b>Reason:</b> {reason}\n<b>Referee:</b> {update.effective_user.mention_html()}\n\n<i>Red card! But they can rejoin if they behave! 🟥</i>",
                f"🚪 <b>EXIT STAGE LEFT!</b>\n\n{target_user.mention_html()} found the door!\n\n<b>Reason:</b> {reason}\n<b>Bouncer:</b> {update.effective_user.mention_html()}\n\n<i>Don't let the door hit you on the way out! 🚪</i>"
            ]
            
            await update.message.reply_text(
                random.choice(kick_messages),
                parse_mode=ParseMode.HTML
            )

        except TelegramError as e:
            error_msg = str(e)
            if "not enough rights" in error_msg.lower():
                error_msg = "I don't have permission to kick users! Give me the power! 💪"
            
            await update.message.reply_text(
                f"❌ <b>KICK FAILED!</b>\n\n<i>{error_msg}</i>",
                parse_mode=ParseMode.HTML
            )

    async def tmute_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Temporary mute - silence them for a specific time! 🤐"""
        if not await self._check_admin_rights(update, context):
            return

        chat_id = update.effective_chat.id
        target_user = await self._get_target_user(update, context)
        
        if not target_user:
            await update.message.reply_text(
                "🎯 <b>TARGET ACQUISITION FAILED!</b>\n\n"
                "Usage:\n"
                "• <code>/tmute @username 5m [reason]</code>\n"
                "• <code>/tmute 123456789 1h spam</code>\n"
                "• Reply: <code>/tmute 30m being annoying</code>\n\n"
                "<b>Time formats:</b> 5m, 1h, 2d\n"
                "<i>Time to zip some lips! 🤐</i>",
                parse_mode=ParseMode.HTML
            )
            return

        # Parse time duration
        if not context.args or len(context.args) < 1:
            await update.message.reply_text(
                "⏰ <b>TIME MISSING!</b>\n\n"
                "Specify duration: <code>/tmute @user 5m reason</code>\n"
                "<b>Formats:</b> 5m, 1h, 2d",
                parse_mode=ParseMode.HTML
            )
            return

        time_arg = context.args[0] if not context.args[0].startswith('@') and not context.args[0].isdigit() else context.args[1]
        duration = self._parse_time_duration(time_arg)
        
        if not duration:
            await update.message.reply_text(
                "⏰ <b>INVALID TIME FORMAT!</b>\n\n"
                "Use: 5m, 1h, 2d, etc.\n"
                "<i>Learn to tell time, genius! 🕐</i>",
                parse_mode=ParseMode.HTML
            )
            return

        # Check if trying to mute an admin
        try:
            member = await context.bot.get_chat_member(chat_id, target_user.id)
            if member.status in [ChatMemberStatus.ADMINISTRATOR, ChatMemberStatus.OWNER]:
                savage_admin_responses = [
                    "❌ Muting admins? That's like trying to silence a Karen! 🗣️",
                    "❌ Admins have permanent speaking privileges! 🎤",
                    "❌ Nice try, but admins are unmutable! 🔊"
                ]
                await update.message.reply_text(
                    random.choice(savage_admin_responses),
                    parse_mode=ParseMode.HTML
                )
                return
        except Exception:
            pass

        # Get reason
        reason_start = 2 if context.args[0].startswith('@') or context.args[0].isdigit() else 1
        reason = " ".join(context.args[reason_start:]) if len(context.args) > reason_start else "No reason provided"
        
        try:
            # Calculate until_date
            until_date = datetime.now(timezone.utc) + timedelta(seconds=duration)
            
            # Mute the user
            await context.bot.restrict_chat_member(
                chat_id=chat_id,
                user_id=target_user.id,
                permissions=ChatPermissions(can_send_messages=False),
                until_date=until_date
            )
            
            # Log the action
            await self.db.log_admin_action(
                chat_id=chat_id,
                admin_id=update.effective_user.id,
                action="tmute",
                target_user_id=target_user.id,
                reason=f"{reason} (Duration: {self._format_duration(duration)})"
            )

            # Send savage mute notification
            mute_messages = [
                f"🤐 <b>SILENCE IS GOLDEN!</b>\n\n{target_user.mention_html()} has been temporarily silenced!\n\n<b>Duration:</b> {self._format_duration(duration)}\n<b>Reason:</b> {reason}\n<b>Admin:</b> {update.effective_user.mention_html()}\n\n<i>Enjoy the peace and quiet! 🧘</i>",
                f"🔇 <b>MUTE BUTTON ACTIVATED!</b>\n\n{target_user.mention_html()} is taking a forced break from talking!\n\n<b>Timeout:</b> {self._format_duration(duration)}\n<b>Reason:</b> {reason}\n<b>Moderator:</b> {update.effective_user.mention_html()}\n\n<i>Sometimes silence is the best medicine! 💊</i>",
                f"🚫 <b>VOCAL CORDS DISABLED!</b>\n\n{target_user.mention_html()} is temporarily speechless!\n\n<b>Duration:</b> {self._format_duration(duration)}\n<b>Reason:</b> {reason}\n<b>Silencer:</b> {update.effective_user.mention_html()}\n\n<i>Time to practice the art of listening! 👂</i>"
            ]
            
            await update.message.reply_text(
                random.choice(mute_messages),
                parse_mode=ParseMode.HTML
            )

        except TelegramError as e:
            error_msg = str(e)
            if "not enough rights" in error_msg.lower():
                error_msg = "I don't have permission to mute users! Give me the power! 💪"
            
            await update.message.reply_text(
                f"❌ <b>MUTE FAILED!</b>\n\n<i>{error_msg}</i>",
                parse_mode=ParseMode.HTML
            )

    async def warnings_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Check user warnings - see their rap sheet! 📋"""
        if not await self._check_admin_rights(update, context):
            return

        chat_id = update.effective_chat.id
        target_user = await self._get_target_user(update, context)
        
        if not target_user:
            await update.message.reply_text(
                "🎯 <b>TARGET ACQUISITION FAILED!</b>\n\n"
                "Usage:\n"
                "• Reply to a message: <code>/warnings</code>\n"
                "• Use username: <code>/warnings @username</code>\n"
                "• Use ID: <code>/warnings 123456789</code>\n\n"
                "<i>Time to check someone's criminal record! 📋</i>",
                parse_mode=ParseMode.HTML
            )
            return

        try:
            # Get user warnings
            warnings = await self.db.get_user_warnings(chat_id, target_user.id)
            warn_count = len(warnings)
            
            if warn_count == 0:
                clean_messages = [
                    f"✅ <b>CLEAN SLATE!</b>\n\n{target_user.mention_html()} has no warnings!\n\n<i>Either they're an angel or just got here! 😇</i>",
                    f"🏆 <b>MODEL CITIZEN!</b>\n\n{target_user.mention_html()} is warning-free!\n\n<i>Give them a medal! 🥇</i>",
                    f"😇 <b>INNOCENT AS A LAMB!</b>\n\n{target_user.mention_html()} has zero warnings!\n\n<i>Suspiciously clean record... 🤔</i>"
                ]
                await update.message.reply_text(
                    random.choice(clean_messages),
                    parse_mode=ParseMode.HTML
                )
                return

            # Build warnings list
            warnings_text = f"⚠️ <b>RAP SHEET FOR {target_user.mention_html()}</b>\n\n"
            warnings_text += f"<b>Total Warnings:</b> {warn_count}/5\n"
            warnings_text += f"<b>Status:</b> {'🔴 DANGER ZONE' if warn_count >= 4 else '🟡 WATCH LIST' if warn_count >= 2 else '🟢 MINOR OFFENSES'}\n\n"
            
            for i, warning in enumerate(warnings[-5:], 1):  # Show last 5 warnings
                timestamp = datetime.fromisoformat(warning['timestamp']).strftime("%Y-%m-%d %H:%M")
                admin_name = warning.get('admin_name', 'Unknown Admin')
                reason = warning.get('reason', 'No reason provided')
                
                warnings_text += f"<b>{i}.</b> {timestamp}\n"
                warnings_text += f"   <b>Admin:</b> {admin_name}\n"
                warnings_text += f"   <b>Reason:</b> {reason}\n\n"
            
            if warn_count > 5:
                warnings_text += f"<i>... and {warn_count - 5} more warnings</i>\n\n"
            
            warnings_text += f"<i>⚠️ {5 - warn_count} more warnings until ban!</i>"
            
            await update.message.reply_text(
                warnings_text,
                parse_mode=ParseMode.HTML
            )

        except Exception as e:
            await update.message.reply_text(
                f"❌ <b>DATABASE ERROR!</b>\n\n<i>Failed to fetch warnings: {str(e)}</i>",
                parse_mode=ParseMode.HTML
            )

    async def resetwarn_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Reset user warnings - clean slate time! 🧹"""
        if not await self._check_admin_rights(update, context):
            return

        chat_id = update.effective_chat.id
        target_user = await self._get_target_user(update, context)
        
        if not target_user:
            await update.message.reply_text(
                "🎯 <b>TARGET ACQUISITION FAILED!</b>\n\n"
                "Usage:\n"
                "• Reply to a message: <code>/resetwarn</code>\n"
                "• Use username: <code>/resetwarn @username</code>\n"
                "• Use ID: <code>/resetwarn 123456789</code>\n\n"
                "<i>Time to wipe someone's record clean! 🧹</i>",
                parse_mode=ParseMode.HTML
            )
            return

        try:
            # Get current warning count
            current_warns = await self.db.get_user_warn_count(chat_id, target_user.id)
            
            if current_warns == 0:
                clean_messages = [
                    f"🤡 <b>BRUH MOMENT!</b>\n\n{target_user.mention_html()} is cleaner than my browser history!",
                    f"❌ <b>TASK FAILED SUCCESSFULLY!</b>\n\n{target_user.mention_html()} has no warnings! They're either a saint or just got here!",
                    f"😂 <b>SUFFERING FROM SUCCESS!</b>\n\n{target_user.mention_html()} is already warning-free! Want to give them a medal too?"
                ]
                await update.message.reply_text(
                    random.choice(clean_messages),
                    parse_mode=ParseMode.HTML
                )
                return

            # Reset warnings
            await self.db.reset_warnings(chat_id, target_user.id)
            
            # Log the action
            await self.db.log_admin_action(
                chat_id=chat_id,
                admin_id=update.effective_user.id,
                action="resetwarn",
                target_user_id=target_user.id,
                reason=f"Cleared {current_warns} warnings"
            )

            # Send savage reset notification
            reset_messages = [
                f"🧹 <b>SLATE WIPED CLEAN!</b>\n\n{target_user.mention_html()}'s {current_warns} warnings have been erased!\n\n<b>Admin:</b> {update.effective_user.mention_html()}\n\n<i>Fresh start! Don't waste this second chance! ✨</i>",
                f"🔄 <b>FACTORY RESET COMPLETE!</b>\n\n{target_user.mention_html()} is back to zero warnings!\n\n<b>Merciful Admin:</b> {update.effective_user.mention_html()}\n\n<i>Their record is now cleaner than my code! 💻</i>",
                f"✨ <b>REDEMPTION ARC ACTIVATED!</b>\n\n{target_user.mention_html()}'s {current_warns} warnings vanished!\n\n<b>Forgiver:</b> {update.effective_user.mention_html()}\n\n<i>Everyone deserves a second chance! 🙏</i>"
            ]
            
            await update.message.reply_text(
                random.choice(reset_messages),
                parse_mode=ParseMode.HTML
            )

        except Exception as e:
            await update.message.reply_text(
                f"❌ <b>RESET FAILED!</b>\n\n<i>Error: {str(e)}</i>",
                parse_mode=ParseMode.HTML
            )

    async def adminlist_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show admin list with their powers! 👑"""
        if not update.effective_chat or update.effective_chat.type == "private":
            await update.message.reply_text(
                "❌ <b>WRONG PLACE!</b>\n\nThis command only works in groups!\n<i>Go find a group to flex in! 💪</i>",
                parse_mode=ParseMode.HTML
            )
            return

        try:
            chat_id = update.effective_chat.id
            admins = await context.bot.get_chat_administrators(chat_id)
            
            admin_list = "👑 <b>ADMIN HALL OF FAME</b>\n\n"
            admin_list += f"<b>Group:</b> {update.effective_chat.title}\n"
            admin_list += f"<b>Total Admins:</b> {len(admins)}\n\n"
            
            owner_count = 0
            admin_count = 0
            
            for admin in admins:
                user = admin.user
                if user.is_bot:
                    continue
                    
                if admin.status == ChatMemberStatus.OWNER:
                    admin_list += f"👑 <b>{user.mention_html()}</b> - <i>Supreme Leader</i>\n"
                    owner_count += 1
                else:
                    admin_list += f"⚡ <b>{user.mention_html()}</b> - <i>Admin</i>\n"
                    admin_count += 1
            
            # Add bot admins
            bot_admins = [admin for admin in admins if admin.user.is_bot]
            if bot_admins:
                admin_list += f"\n🤖 <b>BOT ADMINS ({len(bot_admins)}):</b>\n"
                for bot_admin in bot_admins:
                    admin_list += f"🤖 <b>{bot_admin.user.mention_html()}</b>\n"
            
            admin_list += f"\n<i>Bow down to your {owner_count + admin_count} human overlords! 🙇</i>"
            
            await update.message.reply_text(
                admin_list,
                parse_mode=ParseMode.HTML
            )

        except TelegramError as e:
            await update.message.reply_text(
                f"❌ <b>ADMIN FETCH FAILED!</b>\n\n<i>Error: {str(e)}</i>",
                parse_mode=ParseMode.HTML
            )

    async def admins_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """List group admins - same as adminlist but shorter! 👥"""
        await self.adminlist_command(update, context)

    def _parse_time_duration(self, time_str: str) -> int:
        """Parse time duration string to seconds."""
        if not time_str:
            return 0
            
        time_str = time_str.lower().strip()
        
        # Extract number and unit
        import re
        match = re.match(r'^(\d+)([smhd]?)$', time_str)
        if not match:
            return 0
            
        number = int(match.group(1))
        unit = match.group(2) or 's'
        
        multipliers = {
            's': 1,
            'm': 60,
            'h': 3600,
            'd': 86400
        }
        
        return number * multipliers.get(unit, 1)
 
    async def _get_target_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[User]:
        """
        Get target user from update and context.
        Supports reply-to-message, username, or user ID.
        """
        # If replying to a message, use that user
        if update.message.reply_to_message:
            return update.message.reply_to_message.from_user
            
        # If no arguments provided
        if not context.args:
            return None
            
        # Get the first argument as target
        target = context.args[0]
        
        # If it's a username
        if target.startswith('@'):
            try:
                chat_member = await self._get_user_by_username(target, context, update.effective_chat.id)
                return chat_member.user if chat_member else None
            except Exception:
                return None
                
        # If it's a user ID
        try:
            user_id = int(target)
            try:
                chat_member = await context.bot.get_chat_member(update.effective_chat.id, user_id)
                return chat_member.user
            except TelegramError:
                # Return a User object even if we can't get the chat member
                return User(id=user_id, first_name="Unknown", is_bot=False)
        except ValueError:
            # Not a valid user ID
            return None
            
        return None
