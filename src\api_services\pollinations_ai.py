from typing import Dict, List, Optional, AsyncGenerator, Union
import requests
import aiohttp
import asyncio
import logging
import json
import random
import time
from urllib.parse import quote, quote_plus

logger = logging.getLogger(__name__)

DEFAULT_HEADERS = {
    'Accept': '*/*',
    'Accept-Language': 'en-US,en;q=0.9',
    'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
}

STATIC_URL = "https://pollinations.ai"

class ModelNotFoundError(Exception):
    """Raised when a requested model is not found"""
    pass

class MissingAuthError(Exception):
    """Raised when authentication is required but missing"""
    pass

class PollinationsAI:
    label = "Pollinations AI"
    url = "https://pollinations.ai"
    working = True
    supports_system_message = True
    supports_message_history = True
    login_url = "https://pollinations.ai/login"

    # API endpoints
    text_api_endpoint = "https://text.pollinations.ai"
    openai_endpoint = "https://text.pollinations.ai/openai"
    image_api_endpoint = "https://image.pollinations.ai/"

    # Models configuration
    default_model = "openai"
    fallback_model = "deepseek"
    default_image_model = "flux"
    default_vision_model = default_model
    default_audio_model = "openai-audio"
    default_voice = "alloy"
    text_models = [default_model, "evil"]
    image_models = [default_image_model, "turbo", "kontext", "gptimage", "transparent"]
    audio_models = {default_audio_model: []}
    vision_models = [default_vision_model]
    _models_loaded = False
    model_aliases = {
        "gpt-4": "openai",
        "gpt-4o": "openai",
        "gpt-4.1-mini": "openai",
        "gpt-4o-mini": "openai",
        "gpt-4.1-nano": "openai-fast",
        "gpt-4.1": "openai-large",
        "o4-mini": "openai-reasoning",
        "qwen-2.5-coder-32b": "qwen-coder",
        "llama-3.3-70b": "llama",
        "llama-4-scout": "llamascout",
        "mistral-small-3.1-24b": "mistral",
        "phi-4": "phi",
        "deepseek-r1": "deepseek-reasoning",
        "deepseek-v3-0324": "deepseek",
        "deepseek-v3": "deepseek",
        "grok-3-mini": "grok",
        "grok-3-mini-high": "grok",
        "gpt-4o-mini-audio": "openai-audio",
        "gpt-4o-audio": "openai-audio",
        "sdxl-turbo": "turbo",
        "gpt-image": "gptimage",
        "flux-dev": "flux",
        "flux-schnell": "flux",
        "flux-pro": "flux",
        "flux": "flux",
    }
    swap_models = {value: key for key, value in model_aliases.items()}

    @classmethod
    def get_model(cls, model: str) -> str:
        """Get the internal model name from the user-provided model name."""
        if not model:
            return cls.default_model

        # Check if there's an alias for this model
        if model in cls.model_aliases:
            return cls.model_aliases[model]

        # Check if the model exists directly in our model lists
        if model in cls.text_models or model in cls.image_models or model in cls.audio_models:
            return model

        # If no match is found, raise an error
        raise ModelNotFoundError(f"PollinationsAI: Model {model} not found")

    @classmethod
    def get_models(cls, **kwargs):
        if not cls._models_loaded:
            try:
                # Update of image models
                image_response = requests.get("https://image.pollinations.ai/models")
                if image_response.ok:
                    new_image_models = image_response.json()
                else:
                    new_image_models = []

                # Combine image models without duplicates
                image_models = cls.image_models.copy()  # Start with default model
                
                # Add extra image models if not already in the list
                for model in new_image_models:
                    if model not in image_models:
                        image_models.append(model)
                
                cls.image_models = image_models

                text_response = requests.get("https://text.pollinations.ai/models")
                text_response.raise_for_status()
                models = text_response.json()

                # Purpose of audio models
                cls.audio_models = {
                    model.get("name"): model.get("voices")
                    for model in models
                    if "output_modalities" in model and "audio" in model["output_modalities"]
                }
                for alias, model in cls.model_aliases.items():
                    if model in cls.audio_models and alias not in cls.audio_models:
                        cls.audio_models.update({alias: {}})

                cls.vision_models.extend([
                    cls.swap_models.get(model.get("name"), model.get("name"))
                    for model in models
                    if model.get("vision") and model not in cls.vision_models
                ])
                for alias, model in cls.model_aliases.items():
                    if model in cls.vision_models and alias not in cls.vision_models:
                        cls.vision_models.append(alias)

                # Create a set of unique text models starting with default model
                text_models = cls.text_models.copy()

                # Add models from the API response
                for model in models:
                    model_name = model.get("name")
                    if model_name and "input_modalities" in model and "text" in model["input_modalities"]:
                        text_models.append(cls.swap_models.get(model_name, model_name))

                # Convert to list and update text_models
                cls.text_models = list(dict.fromkeys(text_models))

                cls._models_loaded = True

            except Exception as e:
                # Save default models in case of an error
                if not cls.text_models:
                    cls.text_models = [cls.default_model]
                if not cls.image_models:
                    cls.image_models = [cls.default_image_model]
                logger.error(f"Failed to fetch models: {e}")

        # Return unique models across all categories
        all_models = cls.text_models.copy()
        all_models.extend(cls.image_models)
        all_models.extend(cls.audio_models.keys())
        if cls.default_audio_model in cls.audio_models:
            all_models.extend(cls.audio_models[cls.default_audio_model])
        return list(dict.fromkeys(all_models))

    @classmethod
    def get_grouped_models(cls) -> dict[str, list[str]]:
        cls.get_models()
        return [
            {"group": "Text Generation", "models": cls.text_models},
            {"group": "Image Generation", "models": cls.image_models},
            {"group": "Audio Generation", "models": list(cls.audio_models.keys())},
            {"group": "Audio Voices", "models": cls.audio_models.get(cls.default_audio_model, [])},
        ]

    @classmethod
    def get_model_names(cls) -> List[str]:
        """Get list of available model names"""
        return cls.get_models()

    @classmethod
    def get_model_info(cls, model_name: str) -> Optional[Dict]:
        """Get information about a specific model"""
        try:
            internal_model = cls.get_model(model_name)
            return {
                "name": model_name,
                "internal_name": internal_model,
                "type": "image" if internal_model in cls.image_models else "text",
                "audio": internal_model in cls.audio_models,
                "vision": internal_model in cls.vision_models
            }
        except ModelNotFoundError:
            return None

    @classmethod
    def generate_text(
        cls,
        model: str,
        messages: List[Dict[str, str]],
        temperature: float = None,
        presence_penalty: float = None,
        top_p: float = 1,
        frequency_penalty: float = None,
        response_format: Optional[dict] = None,
        seed: Optional[int] = None,
        cache: bool = False,
    ) -> str:
        """
        Generate text using the Pollinations AI API (synchronous version)
        
        Args:
            model: Model to use for generation
            messages: List of message dictionaries with 'role' and 'content'
            temperature: Temperature for generation
            presence_penalty: Presence penalty
            top_p: Top p sampling
            frequency_penalty: Frequency penalty
            response_format: Response format specification
            seed: Random seed
            cache: Whether to use cache
            
        Returns:
            Generated text response
        """
        try:
            model = cls.get_model(model) if model else cls.default_model
        except ModelNotFoundError:
            model = cls.default_model

        if not cache and seed is None:
            seed = random.randint(0, 2**32)

        jsonMode = False
        if response_format is not None and "type" in response_format:
            if response_format["type"] == "json_object":
                jsonMode = True

        data = {
            "messages": messages,
            "model": model,
            "temperature": temperature,
            "presence_penalty": presence_penalty,
            "top_p": top_p,
            "frequency_penalty": frequency_penalty,
            "jsonMode": jsonMode,
            "stream": False,
            "seed": None if model == "grok" else seed,
            "cache": cache
        }

        # Remove None values
        data = {k: v for k, v in data.items() if v is not None}

        try:
            response = requests.post(
                cls.openai_endpoint,
                headers=DEFAULT_HEADERS,
                json=data,
                timeout=60
            )
            response.raise_for_status()
            
            result = response.json()
            return result['choices'][0]['message']['content']
        except requests.exceptions.RequestException as e:
            logger.error(f"Error in generate_text: {str(e)}")
            raise

    @classmethod
    async def generate_text_async(
        cls,
        model: str,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        presence_penalty: float = 0.1,
        top_p: float = 0.95,
        frequency_penalty: float = 0.2,
        response_format: Optional[dict] = None,
        seed: Optional[int] = None,
        cache: bool = False,
        timeout: int = 60,
        max_tokens: Optional[int] = None,
    ) -> str:
        """
        Generate text using the Pollinations AI API (asynchronous version)
        
        Args:
            model: Model to use for generation
            messages: List of message dictionaries with 'role' and 'content'
            temperature: Temperature for generation
            presence_penalty: Presence penalty
            top_p: Top p sampling
            frequency_penalty: Frequency penalty
            response_format: Response format specification
            seed: Random seed
            cache: Whether to use cache
            timeout: Request timeout in seconds
            max_tokens: Maximum tokens in response
            
        Returns:
            Generated text response
        """
        try:
            model = cls.get_model(model) if model else cls.default_model
        except ModelNotFoundError:
            model = cls.default_model

        if not cache and seed is None:
            seed = random.randint(0, 2**32)

        jsonMode = False
        if response_format is not None and "type" in response_format:
            if response_format["type"] == "json_object":
                jsonMode = True

        data = {
            "messages": messages,
            "model": model,
            "temperature": temperature,
            "presence_penalty": presence_penalty,
            "top_p": top_p,
            "frequency_penalty": frequency_penalty,
            "jsonMode": jsonMode,
            "stream": False,
            "seed": None if model == "grok" else seed,
            "cache": cache,
        }

        # Add max_tokens if specified
        if max_tokens is not None:
            data["max_tokens"] = max_tokens

        # Remove None values
        data = {k: v for k, v in data.items() if v is not None}

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    cls.openai_endpoint,
                    headers=DEFAULT_HEADERS,
                    json=data,
                    timeout=aiohttp.ClientTimeout(total=timeout)
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"Error from API: {response.status}, {error_text}")
                        response.raise_for_status()
                    
                    result = await response.json()
                    return result['choices'][0]['message']['content']
        except aiohttp.ClientError as e:
            logger.error(f"Error in generate_text_async: {str(e)}")
            raise
        except asyncio.TimeoutError:
            logger.error(f"Request to Pollinations AI timed out after {timeout}s")
            raise

    @classmethod
    async def generate_image(
        cls,
        prompt: str,
        model: str = None,
        width: int = None,
        height: int = None,
        aspect_ratio: str = None,
        seed: Optional[int] = None,
        nologo: bool = True,
        private: bool = False,
        enhance: bool = None,
        safe: bool = False,
        transparent: bool = False,
        timeout: int = 120
    ) -> str:
        """
        Generate image using Pollinations AI
        
        Args:
            prompt: Text prompt for image generation
            model: Model to use (defaults to flux)
            width: Image width
            height: Image height
            aspect_ratio: Aspect ratio (e.g., "1:1", "16:9")
            seed: Random seed
            nologo: Remove Pollinations logo
            private: Private generation
            enhance: Enhance prompt
            safe: Safe mode
            transparent: Transparent background
            timeout: Request timeout
            
        Returns:
            URL of the generated image
        """
        if model is None:
            model = cls.default_image_model
            
        try:
            model = cls.get_model(model) if model else cls.default_image_model
        except ModelNotFoundError:
            model = cls.default_image_model

        if enhance is None:
            enhance = True if model == "flux" else False
            
        params = {
            "model": model,
            "nologo": str(nologo).lower(),
            "private": str(private).lower(),
            "enhance": str(enhance).lower(),
            "safe": str(safe).lower(),
            "referrer": STATIC_URL
        }
        
        if transparent:
            params["transparent"] = "true"
            
        if seed is not None:
            params["seed"] = str(seed)
            
        if width and height:
            params["width"] = str(width)
            params["height"] = str(height)
        elif aspect_ratio:
            # Convert aspect ratio to width/height
            if aspect_ratio == "1:1":
                params["width"] = "1024"
                params["height"] = "1024"
            elif aspect_ratio == "16:9":
                params["width"] = "1920"
                params["height"] = "1080"
            elif aspect_ratio == "9:16":
                params["width"] = "1080"
                params["height"] = "1920"
            else:
                params["width"] = "1024"
                params["height"] = "1024"
        
        query = "&".join(f"{k}={quote(str(v))}" for k, v in params.items() if v is not None)
        encoded_prompt = quote_plus(prompt.strip(". \n"))[:4096-len(cls.image_api_endpoint)-len(query)-8].rstrip("%")
        url = f"{cls.image_api_endpoint}prompt/{encoded_prompt}?{query}"
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    url,
                    headers=DEFAULT_HEADERS,
                    timeout=aiohttp.ClientTimeout(total=timeout)
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"Error from image API: {response.status}, {error_text}")
                        response.raise_for_status()
                    
                    return str(response.url)
        except aiohttp.ClientError as e:
            logger.error(f"Error in generate_image: {str(e)}")
            raise
        except asyncio.TimeoutError:
            logger.error(f"Image generation timed out after {timeout}s")
            raise

if __name__ == "__main__":
    print(PollinationsAI.generate_text(
        model="grok",
        messages=[{"role": "user", "content": "how many r in strawberry"}]
    ))
