"""
Collection of public API services that don't require authentication.
"""
import aiohttp
import asyncio
import json
from typing import Dict, Optional, Any

class PublicAPIs:
    def __init__(self):
        self.session = None

    async def ensure_session(self):
        if self.session is None:
            self.session = aiohttp.ClientSession()

    async def close_session(self):
        if self.session:
            await self.session.close()
            self.session = None

    # Weather API using Open-Meteo
    async def get_weather(self, location: str) -> dict:
        """Get detailed weather information using multiple sources"""
        await self.ensure_session()
        
        # First, get coordinates for the location using Nominatim
        geocoding_url = f"https://nominatim.openstreetmap.org/search?q={location}&format=json&limit=1"
        headers = {
            'User-Agent': 'NexusAI Telegram Bot/1.0',
            'Accept-Language': 'en-US,en;q=0.9'
        }
        
        try:
            # Get location data
            async with self.session.get(geocoding_url, headers=headers) as response:
                location_data = await response.json()
                
                if not location_data:
                    raise Exception("Location not found! Try being more specific?")
                
                lat = float(location_data[0]['lat'])
                lon = float(location_data[0]['lon'])
                display_name = location_data[0]['display_name']
                city = display_name.split(',')[0]
                country = location_data[0].get('address', {}).get('country', display_name.split(',')[-1].strip())
                
            # Get weather data from Open-Meteo
            weather_url = (
                f"https://api.open-meteo.com/v1/forecast"
                f"?latitude={lat}&longitude={lon}"
                f"&current=temperature_2m,relative_humidity_2m,wind_speed_10m,wind_direction_10m,"
                f"apparent_temperature,precipitation,cloud_cover,surface_pressure,visibility,is_day,"
                f"wind_gusts_10m,uv_index"
                f"&daily=temperature_2m_max,temperature_2m_min,precipitation_sum,wind_speed_10m_max,"
                f"wind_gusts_10m_max,precipitation_probability_max,uv_index_max,sunrise,sunset,"
                f"precipitation_hours,rain_sum"
                f"&timezone=auto"
                f"&forecast_days=3"
            )
            
            async with self.session.get(weather_url) as response:
                weather_data = await response.json()
                
                if 'error' in weather_data:
                    raise Exception(weather_data['reason'])
                
                current = weather_data['current']
                daily = weather_data['daily']

            # Get air quality data
            air_url = (
                f"https://air-quality-api.open-meteo.com/v1/air-quality"
                f"?latitude={lat}&longitude={lon}"
                f"&current=us_aqi,pm10,pm2_5,carbon_monoxide,nitrogen_dioxide,ozone"
            )
            
            async with self.session.get(air_url) as response:
                air_data = await response.json()
                air_current = air_data.get('current', {})

            # Convert wind direction to cardinal
            def degrees_to_cardinal(degrees):
                directions = ["N", "NNE", "NE", "ENE", "E", "ESE", "SE", "SSE",
                            "S", "SSW", "SW", "WSW", "W", "WNW", "NW", "NNW"]
                index = round(degrees / (360 / len(directions))) % len(directions)
                return directions[index]

            # Get AQI description
            def get_aqi_desc(aqi):
                if aqi <= 50: return "Good 😊"
                elif aqi <= 100: return "Moderate 😐"
                elif aqi <= 150: return "Unhealthy for Sensitive Groups 😷"
                elif aqi <= 200: return "Unhealthy 🤢"
                elif aqi <= 300: return "Very Unhealthy ⚠️"
                else: return "Hazardous ☠️"

            # Get UV description
            def get_uv_desc(uv):
                if uv <= 2: return "Low 😎"
                elif uv <= 5: return "Moderate 🕶️"
                elif uv <= 7: return "High 🌞"
                elif uv <= 10: return "Very High ⚠️"
                else: return "Extreme 🔥"

            # Format visibility
            def get_visibility_desc(visibility):
                if visibility >= 10000: return "Excellent 👁️"
                elif visibility >= 5000: return "Good 👀"
                elif visibility >= 2000: return "Moderate 🌫️"
                else: return "Poor ⚠️"

            # Get weather alerts if any
            alerts = []
            if current['wind_gusts_10m'] > 50:
                alerts.append("🌪️ Strong wind gusts detected!")
            if current['precipitation'] > 10:
                alerts.append("🌧️ Heavy precipitation warning!")
            if air_current.get('us_aqi', 0) > 150:
                alerts.append("😷 Poor air quality alert!")
            if current.get('uv_index', 0) > 8:
                alerts.append("🔥 Extreme UV warning!")

            return {
                "location": f"{city}, {country}",
                "lat": lat,
                "lon": lon,
                "temp_c": round(current['temperature_2m'], 1),
                "temp_f": round(current['temperature_2m'] * 9/5 + 32, 1),
                "feels_like_c": round(current['apparent_temperature'], 1),
                "feels_like_f": round(current['apparent_temperature'] * 9/5 + 32, 1),
                "humidity": current['relative_humidity_2m'],
                "cloud_cover": current['cloud_cover'],
                "visibility": get_visibility_desc(current['visibility']),
                "pressure": round(current['surface_pressure']),
                "wind_speed": round(current['wind_speed_10m']),
                "wind_dir": degrees_to_cardinal(current['wind_direction_10m']),
                "wind_gusts": round(current['wind_gusts_10m']),
                "precipitation": round(current['precipitation'], 1),
                "uv_index": f"{round(current['uv_index'], 1)} - {get_uv_desc(current['uv_index'])}",
                "is_day": bool(current['is_day']),
                
                # Daily data
                "max_temp": round(daily['temperature_2m_max'][0], 1),
                "min_temp": round(daily['temperature_2m_min'][0], 1),
                "max_wind": round(daily['wind_speed_10m_max'][0]),
                "rain_chance": daily['precipitation_probability_max'][0],
                "rain_amount": round(daily['rain_sum'][0], 1),
                "rain_hours": daily['precipitation_hours'][0],
                "sunrise": daily['sunrise'][0].split('T')[1],
                "sunset": daily['sunset'][0].split('T')[1],
                
                # Air Quality
                "aqi": f"{air_current.get('us_aqi', 'N/A')} - {get_aqi_desc(air_current.get('us_aqi', 0))}",
                "pm25": round(air_current.get('pm2_5', 0), 1),
                "pm10": round(air_current.get('pm10', 0), 1),
                "co": round(air_current.get('carbon_monoxide', 0), 1),
                "no2": round(air_current.get('nitrogen_dioxide', 0), 1),
                "o3": round(air_current.get('ozone', 0), 1),
                
                # Forecast (next 2 days)
                "forecast": [
                    {
                        "date": "Tomorrow",
                        "max_temp": round(daily['temperature_2m_max'][1], 1),
                        "min_temp": round(daily['temperature_2m_min'][1], 1),
                        "rain_chance": daily['precipitation_probability_max'][1],
                        "rain_amount": round(daily['rain_sum'][1], 1),
                    },
                    {
                        "date": "Day After",
                        "max_temp": round(daily['temperature_2m_max'][2], 1),
                        "min_temp": round(daily['temperature_2m_min'][2], 1),
                        "rain_chance": daily['precipitation_probability_max'][2],
                        "rain_amount": round(daily['rain_sum'][2], 1),
                    }
                ],
                
                # Alerts
                "alerts": alerts
            }
                
        except Exception as e:
            raise Exception(f"Failed to get weather info: {str(e)}")

    # Random Quote API
    async def get_quote(self) -> Dict[str, str]:
        """Get a random quote from ZenQuotes API"""
        await self.ensure_session()
        url = "https://zenquotes.io/api/random"
        async with self.session.get(url) as response:
            data = await response.json()
            return {
                "quote": data[0]["q"],
                "author": data[0]["a"]
            }

    # Random Joke API
    async def get_joke(self) -> str:
        """Get a random joke from Official Joke API"""
        await self.ensure_session()
        url = "https://official-joke-api.appspot.com/random_joke"
        async with self.session.get(url) as response:
            data = await response.json()
            return f"{data['setup']}\n\n{data['punchline']}"

    # Random Facts API
    async def get_fact(self) -> str:
        """Get a random fact from uselessfacts API"""
        await self.ensure_session()
        url = "https://uselessfacts.jsph.pl/random.json?language=en"
        async with self.session.get(url) as response:
            data = await response.json()
            return data["text"]

    # Random Dog Image
    async def get_dog_image(self) -> str:
        """Get a random dog image from Dog API"""
        await self.ensure_session()
        url = "https://dog.ceo/api/breeds/image/random"
        async with self.session.get(url) as response:
            data = await response.json()
            return data["message"]

    # Random Cat Fact
    async def get_cat_fact(self) -> str:
        """Get a random cat fact"""
        await self.ensure_session()
        url = "https://catfact.ninja/fact"
        async with self.session.get(url) as response:
            data = await response.json()
            return data["fact"]

    # Currency Exchange Rates
    async def get_exchange_rate(self, base_currency: str = "USD", target_currency: str = None, amount: float = 1.0) -> Dict[str, Any]:
        """Get current exchange rates and convert currencies"""
        await self.ensure_session()
        base_currency = base_currency.upper()
        
        url = f"https://open.er-api.com/v6/latest/{base_currency}"
        async with self.session.get(url) as response:
            data = await response.json()
            
            if data.get("result") == "error":
                raise Exception(f"Currency API Error: {data.get('error-type')}")
            
            result = {
                "base_currency": base_currency,
                "date": data["time_last_update_utc"],
                "rates": data["rates"]
            }
            
            # If target currency is specified, calculate the conversion
            if target_currency:
                target_currency = target_currency.upper()
                if target_currency not in data["rates"]:
                    raise Exception(f"Invalid target currency: {target_currency}")
                    
                rate = data["rates"][target_currency]
                converted_amount = round(amount * rate, 2)
                
                result["conversion"] = {
                    "amount": amount,
                    "from": base_currency,
                    "to": target_currency,
                    "rate": rate,
                    "result": converted_amount,
                    "formula": f"{amount} {base_currency} × {rate} = {converted_amount} {target_currency}"
                }
            
            return result

    # Random Number Facts
    async def get_number_fact(self, number: int) -> str:
        """Get an interesting fact about a number"""
        await self.ensure_session()
        url = f"http://numbersapi.com/{number}"
        async with self.session.get(url) as response:
            return await response.text()

    # IP Geolocation
    async def get_ip_info(self, ip: str) -> Dict[str, Any]:
        """Get IP information from ipapi.co"""
        await self.ensure_session()
        url = f"https://ipapi.co/{ip}/json/"
        async with self.session.get(url) as response:
            data = await response.json()
            return {
                "ip": data.get("ip"),
                "city": data.get("city"),
                "region": data.get("region"),
                "country": data.get("country_name"),
                "timezone": data.get("timezone"),
                "isp": data.get("org"),
                "location": {
                    "latitude": data.get("latitude"),
                    "longitude": data.get("longitude")
                }
            }

    # Random User Generator
    async def get_random_user(self) -> Dict[str, Any]:
        """Get random user data with additional details"""
        await self.ensure_session()
        url = "https://randomuser.me/api/?inc=name,location,email,phone,picture,dob,nat&noinfo"
        async with self.session.get(url) as response:
            data = await response.json()
            user = data["results"][0]
            return {
                "name": f"{user['name']['first']} {user['name']['last']}",
                "email": user['email'],
                "phone": user['phone'],
                "age": user['dob']['age'],
                "country": user['location']['country'],
                "city": user['location']['city'],
                "picture": user['picture']['large'],
                "nationality": user['nat']
            }
