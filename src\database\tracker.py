"""
SQLite Database tracker for monitoring server and user activity.
"""
import logging
import sqlite3
from typing import Optional, Dict, Any, List
from datetime import datetime
import os

logger = logging.getLogger(__name__)

class SQLiteTracker:
    """Handles tracking of server and user activity in SQLite."""
    
    def __init__(self, db_path: str = "data/bot_database.db"):
        """Initialize the SQLite tracker."""
        self.db_path = db_path
        # Create data directory if it doesn't exist
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        self._create_tables()
        logger.info("SQLite tracker initialized successfully")
    
    def _create_tables(self):
        """Create necessary tables for tracking"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Users table
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS tracker_users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    telegram_id INTEGER UNIQUE NOT NULL,
                    username TEXT,
                    first_name TEXT,
                    last_name TEX<PERSON>,
                    last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """)
                
                # Servers table
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS tracker_servers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    telegram_id INTEGER UNIQUE NOT NULL,
                    title TEXT,
                    description TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """)
                
                # Server members table
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS tracker_server_members (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    server_telegram_id INTEGER NOT NULL,
                    user_telegram_id INTEGER NOT NULL,
                    is_admin BOOLEAN DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    message_count INTEGER DEFAULT 0,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(server_telegram_id, user_telegram_id)
                )
                """)
                
                # Messages table
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS tracker_messages (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_telegram_id INTEGER NOT NULL,
                    chat_id INTEGER NOT NULL,
                    message_text TEXT,
                    message_type TEXT DEFAULT 'text',
                    has_media BOOLEAN DEFAULT 0,
                    media_type TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """)
                
                # Command usage table
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS tracker_command_usage (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_telegram_id INTEGER NOT NULL,
                    server_telegram_id INTEGER,
                    command TEXT NOT NULL,
                    arguments TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    success BOOLEAN DEFAULT 1
                )
                """)
                
                # Server settings table
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS tracker_server_settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    server_telegram_id INTEGER UNIQUE NOT NULL,
                    protection_enabled BOOLEAN DEFAULT 1,
                    link_filter_enabled BOOLEAN DEFAULT 0,
                    media_filter_enabled BOOLEAN DEFAULT 0,
                    nsfw_filter_enabled BOOLEAN DEFAULT 0,
                    restrict_new_users BOOLEAN DEFAULT 0,
                    welcome_enabled BOOLEAN DEFAULT 1,
                    goodbye_enabled BOOLEAN DEFAULT 1,
                    antiflood_enabled BOOLEAN DEFAULT 1,
                    antiflood_limit INTEGER DEFAULT 5,
                    antiflood_time INTEGER DEFAULT 3,
                    logging_enabled BOOLEAN DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """)
                
                conn.commit()
                logger.info("SQLite tracker tables created successfully")
        except Exception as e:
            logger.error(f"Error creating tracker tables: {e}")
    
    def close(self):
        """Close the database connection."""
        # SQLite connections are closed automatically
    
    def track_user(self, user_id: int, username: str = None, first_name: str = None, 
                  last_name: str = None) -> bool:
        """
        Track a user in the database.
        
        Args:
            user_id: The Telegram user ID
            username: The user's username
            first_name: The user's first name
            last_name: The user's last name
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                INSERT OR REPLACE INTO tracker_users 
                (telegram_id, username, first_name, last_name, last_active, is_active, updated_at)
                VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP)
                """, (user_id, username, first_name, last_name))
                conn.commit()
                return True
            
        except Exception as e:
            logger.error(f"Error tracking user {user_id}: {e}")
            return False
    
    def track_server(self, server_id: int, title: str = None, 
                    description: str = None) -> bool:
        """
        Track a server in the database.
        
        Args:
            server_id: The Telegram chat ID
            title: The server title
            description: The server description
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                INSERT OR REPLACE INTO tracker_servers 
                (telegram_id, title, description, is_active, updated_at)
                VALUES (?, ?, ?, 1, CURRENT_TIMESTAMP)
                """, (server_id, title, description))
                conn.commit()
                return True
            
        except Exception as e:
            logger.error(f"Error tracking server {server_id}: {e}")
            return False
    
    def track_server_member(self, server_id: int, user_id: int, 
                           is_admin: bool = False) -> bool:
        """
        Track a server member in the database.
        
        Args:
            server_id: The server ID
            user_id: The user ID
            is_admin: Whether the user is an admin
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                INSERT OR REPLACE INTO tracker_server_members 
                (server_telegram_id, user_telegram_id, is_admin, is_active, last_active, updated_at)
                VALUES (?, ?, ?, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                """, (server_id, user_id, is_admin))
                conn.commit()
                return True
            
        except Exception as e:
            logger.error(f"Error tracking server member {user_id} in {server_id}: {e}")
            return False
    
    def track_message(self, chat_id: int, user_id: int, message_text: str = None,
                     message_type: str = "text", has_media: bool = False,
                     media_type: str = None) -> bool:
        """
        Track a message in the database.
        
        Args:
            chat_id: The chat ID
            user_id: The user ID
            message_text: The message text
            message_type: The message type
            has_media: Whether the message has media
            media_type: The media type
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Insert message
                cursor.execute("""
                INSERT INTO tracker_messages 
                (user_telegram_id, chat_id, message_text, message_type, has_media, media_type, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                """, (user_id, chat_id, message_text, message_type, has_media, media_type))
                
                # Update server member message count
                cursor.execute("""
                UPDATE tracker_server_members 
                SET message_count = message_count + 1, last_active = CURRENT_TIMESTAMP
                WHERE server_telegram_id = ? AND user_telegram_id = ?
                """, (chat_id, user_id))
                
                conn.commit()
                return True
            
        except Exception as e:
            logger.error(f"Error tracking message from {user_id} in {chat_id}: {e}")
            return False
    
    def track_command(self, user_id: int, command: str, server_id: int = None,
                     arguments: str = None, success: bool = True) -> bool:
        """
        Track a command usage in the database.
        
        Args:
            user_id: The user ID
            command: The command name
            server_id: The server ID (optional for private chats)
            arguments: The command arguments
            success: Whether the command was successful
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                INSERT INTO tracker_command_usage 
                (user_telegram_id, server_telegram_id, command, arguments, timestamp, success)
                VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, ?)
                """, (user_id, server_id, command, arguments, success))
                conn.commit()
                return True
            
        except Exception as e:
            logger.error(f"Error tracking command {command} from {user_id}: {e}")
            return False
    
    def get_server_settings(self, server_id: int) -> Optional[Dict]:
        """
        Get settings for a server.
        
        Args:
            server_id: The server ID (Telegram chat ID)
            
        Returns:
            The server settings dict or None if not found
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM tracker_server_settings WHERE server_telegram_id = ?", (server_id,))
                result = cursor.fetchone()
                
                if not result:
                    # Create default settings
                    cursor.execute("""
                    INSERT INTO tracker_server_settings 
                    (server_telegram_id, protection_enabled, link_filter_enabled, media_filter_enabled,
                     nsfw_filter_enabled, restrict_new_users, welcome_enabled, goodbye_enabled,
                     antiflood_enabled, antiflood_limit, antiflood_time, logging_enabled)
                    VALUES (?, 1, 0, 0, 0, 0, 1, 1, 1, 5, 3, 0)
                    """, (server_id,))
                    conn.commit()
                    
                    # Get the inserted record
                    cursor.execute("SELECT * FROM tracker_server_settings WHERE server_telegram_id = ?", (server_id,))
                    result = cursor.fetchone()
                
                if result:
                    columns = [description[0] for description in cursor.description]
                    return dict(zip(columns, result))
                return None
            
        except Exception as e:
            logger.error(f"Error getting server settings for {server_id}: {e}")
            return None
    
    def update_server_settings(self, server_id: int, **kwargs) -> bool:
        """
        Update settings for a server.
        
        Args:
            server_id: The server ID (Telegram chat ID)
            **kwargs: Settings to update
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Build update query dynamically
                if kwargs:
                    set_clauses = []
                    values = []
                    for key, value in kwargs.items():
                        if key != 'server_telegram_id':  # Don't update the ID
                            set_clauses.append(f"{key} = ?")
                            values.append(value)
                    
                    set_clauses.append("updated_at = CURRENT_TIMESTAMP")
                    values.append(server_id)
                    
                    query = f"UPDATE tracker_server_settings SET {', '.join(set_clauses)} WHERE server_telegram_id = ?"
                    cursor.execute(query, values)
                    conn.commit()
                    
                    return cursor.rowcount > 0
                return False
            
        except Exception as e:
            logger.error(f"Error updating server settings for {server_id}: {e}")
            return False
    
    def get_server_stats(self, server_id: int) -> Dict[str, Any]:
        """
        Get statistics for a server.
        
        Args:
            server_id: The server ID
            
        Returns:
            A dictionary of server statistics
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get server info
                cursor.execute("SELECT title FROM tracker_servers WHERE telegram_id = ?", (server_id,))
                server_result = cursor.fetchone()
                if not server_result:
                    return {}
                
                # Get member count
                cursor.execute("SELECT COUNT(*) FROM tracker_server_members WHERE server_telegram_id = ? AND is_active = 1", (server_id,))
                member_count = cursor.fetchone()[0]
                
                # Get message count
                cursor.execute("SELECT COUNT(*) FROM tracker_messages WHERE chat_id = ?", (server_id,))
                message_count = cursor.fetchone()[0]
                
                # Get command count
                cursor.execute("SELECT COUNT(*) FROM tracker_command_usage WHERE server_telegram_id = ?", (server_id,))
                command_count = cursor.fetchone()[0]
                
                # Get top users by message count
                cursor.execute("""
                SELECT tsm.user_telegram_id, tu.username, tsm.message_count
                FROM tracker_server_members tsm
                LEFT JOIN tracker_users tu ON tsm.user_telegram_id = tu.telegram_id
                WHERE tsm.server_telegram_id = ?
                ORDER BY tsm.message_count DESC
                LIMIT 10
                """, (server_id,))
                top_users_data = cursor.fetchall()
                
                formatted_top_users = []
                for user_data in top_users_data:
                    formatted_top_users.append({
                        "user_id": user_data[0],
                        "username": user_data[1],
                        "message_count": user_data[2] or 0
                    })
                
                # Get settings
                settings = self.get_server_settings(server_id)
                settings_dict = {}
                if settings:
                    settings_dict = {
                        "protection_enabled": bool(settings.get("protection_enabled", 1)),
                        "link_filter_enabled": bool(settings.get("link_filter_enabled", 0)),
                        "media_filter_enabled": bool(settings.get("media_filter_enabled", 0)),
                        "nsfw_filter_enabled": bool(settings.get("nsfw_filter_enabled", 0)),
                        "restrict_new_users": bool(settings.get("restrict_new_users", 0)),
                        "welcome_enabled": bool(settings.get("welcome_enabled", 1)),
                        "goodbye_enabled": bool(settings.get("goodbye_enabled", 1)),
                        "antiflood_enabled": bool(settings.get("antiflood_enabled", 1)),
                        "antiflood_limit": settings.get("antiflood_limit", 5),
                        "antiflood_time": settings.get("antiflood_time", 3),
                        "logging_enabled": bool(settings.get("logging_enabled", 0))
                    }
                
                return {
                    "server_id": server_id,
                    "title": server_result[0],
                    "member_count": member_count,
                    "message_count": message_count,
                    "command_count": command_count,
                    "settings": settings_dict,
                    "top_users": formatted_top_users
                }
            
        except Exception as e:
            logger.error(f"Error getting server stats for {server_id}: {e}")
            return {}

# Create a singleton instance that mimics the old interface
class DatabaseTracker:
    """Compatibility wrapper for the SQLite-based tracker"""
    
    def __init__(self):
        """Initialize the SQLite tracker."""
        try:
            self.sqlite_tracker = SQLiteTracker()
            logger.info("Database tracker initialized with SQLite")
        except Exception as e:
            logger.error(f"Error initializing database tracker: {e}")
            # Create a dummy tracker that doesn't fail
            self.sqlite_tracker = None
    
    def close(self):
        """Close the database connection."""
        if self.sqlite_tracker:
            self.sqlite_tracker.close()
    
    def track_user(self, user_id: int, username: str = None, first_name: str = None, 
                  last_name: str = None) -> Optional[Any]:
        """Track a user in the database."""
        if self.sqlite_tracker:
            success = self.sqlite_tracker.track_user(user_id, username, first_name, last_name)
            return {"id": user_id} if success else None
        return None
    
    def track_server(self, server_id: int, title: str = None, 
                    description: str = None) -> Optional[Any]:
        """Track a server in the database."""
        if self.sqlite_tracker:
            success = self.sqlite_tracker.track_server(server_id, title, description)
            return {"id": server_id} if success else None
        return None
    
    def track_server_member(self, server_id: int, user_id: int, 
                           is_admin: bool = False) -> Optional[Any]:
        """Track a server member in the database."""
        if self.sqlite_tracker:
            success = self.sqlite_tracker.track_server_member(server_id, user_id, is_admin)
            return {"server_id": server_id, "user_id": user_id} if success else None
        return None
    
    def track_message(self, chat_id: int, user_id: int, message_text: str = None,
                     message_type: str = "text", has_media: bool = False,
                     media_type: str = None) -> Optional[Any]:
        """Track a message in the database."""
        if self.sqlite_tracker:
            success = self.sqlite_tracker.track_message(chat_id, user_id, message_text, message_type, has_media, media_type)
            return {"chat_id": chat_id, "user_id": user_id} if success else None
        return None
    
    def track_command(self, user_id: int, command: str, server_id: int = None,
                     arguments: str = None, success: bool = True) -> Optional[Any]:
        """Track a command usage in the database."""
        if self.sqlite_tracker:
            success = self.sqlite_tracker.track_command(user_id, command, server_id, arguments, success)
            return {"user_id": user_id, "command": command} if success else None
        return None
    
    def get_server_settings(self, server_id: int) -> Optional[Any]:
        """Get settings for a server."""
        if self.sqlite_tracker:
            return self.sqlite_tracker.get_server_settings(server_id)
        return None
    
    def update_server_settings(self, server_id: int, **kwargs) -> bool:
        """Update settings for a server."""
        if self.sqlite_tracker:
            return self.sqlite_tracker.update_server_settings(server_id, **kwargs)
        return False
    
    def get_server_stats(self, server_id: int) -> Dict[str, Any]:
        """Get statistics for a server."""
        if self.sqlite_tracker:
            return self.sqlite_tracker.get_server_stats(server_id)
        return {}

# Create a singleton instance
db_tracker = DatabaseTracker()