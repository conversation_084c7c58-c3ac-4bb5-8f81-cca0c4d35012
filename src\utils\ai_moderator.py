"""AI-powered content moderation using OpenAI API 🤖"""

import json
import os
import datetime
from typing import Tuple, Optional, Dict, List, Set
import xml.etree.ElementTree as ET
from pathlib import Path
from dotenv import load_dotenv
import re
import logging
import traceback

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

from src.api_services.pollinations_ai import PollinationsAI

# Load environment variables
load_dotenv()

class AIModerator:
    def __init__(self):
        self.client = PollinationsAI()
        self.model = "openai-fast"
        # Load NSFW words list
        self.nsfw_words = self._load_nsfw_words()
        # Create directory for moderation logs if it doesn't exist
        self.logs_dir = Path("logs/moderation")
        self.logs_dir.mkdir(parents=True, exist_ok=True)
        # Path for mod.json file
        self.mod_json_path = Path("src/config/mod.json")
        self._ensure_mod_json_exists()
        # Message history for context
        self.message_history: List[Dict] = []
        self.max_history = 2  # Keep current message + 2 previous messages for context
        # Emoji reactions for different severity levels
        self.reaction_emojis = {
            1: "⚠️",  # Warning
            2: "🚫",  # Moderate violation
            3: "⛔",  # Severe violation
            4: "🔒",  # Critical violation
            "safe": "✅",  # Safe content
            "false_alarm": "✅"  # False alarm reaction
        }
        # Store message IDs for cleanup
        self.warning_messages: Dict[str, Dict] = {}  # {original_msg_id: {warning_msg_id: str, severity: int}}

    def _load_nsfw_words(self) -> Set[str]:
        """Load NSFW words from JSON file and create variations"""
        try:
            with open("src/config/nsfw_words.json", "r", encoding="utf-8") as f:
                data = json.load(f)
                words = set(data.get("nsfw_words", []))

                # Log the number of loaded words
                logger.info(f"Loaded {len(words)} NSFW words from json file")

                # Add common variations and leetspeak
                variations = set()
                for word in words:
                    # Add the word itself to variations
                    variations.add(word)

                    # Add simple obfuscation variations
                    variations.add(word.replace('a', '@'))
                    variations.add(word.replace('i', '1'))
                    variations.add(word.replace('e', '3'))
                    variations.add(word.replace('o', '0'))
                    variations.add(word.replace('s', '$'))

                    # Add space-separated variations
                    if len(word) > 3:
                        variations.add(' '.join(word))  # f u c k
                        variations.add('-'.join(word))  # f-u-c-k
                        variations.add('.'.join(word))  # f.u.c.k

                # Log the total number of variations
                logger.info(f"Created word list with {len(variations)} total variations")

                return variations
        except Exception as e:
            logger.error(f"Error loading NSFW words: {e}")
            logger.error(traceback.format_exc())
            return set()

    def _add_to_history(self, message: str, user_id: str) -> None:
        """Add message to history with timestamp and user info"""
        # Add new message to the end
        self.message_history.append({
            "text": message,
            "user_id": user_id,
            "timestamp": datetime.datetime.now().isoformat()
        })
        # Keep only last max_history messages (current + 2 previous)
        if len(self.message_history) > self.max_history + 1:  # +1 for current message
            self.message_history = self.message_history[-self.max_history-1:]

    def _get_context(self) -> str:
        """Get formatted context from message history"""
        if not self.message_history:
            return ""

        context = "Previous messages for context:\n"
        # Format previous messages (excluding current)
        for msg in self.message_history[:-1]:
            context += f"- {msg['text']}\n"
        return context

    def contains_nsfw_words(self, text: str) -> Tuple[bool, List[str]]:
        """Check if text contains any NSFW words and return found words"""
        if not self.nsfw_words or not text:
            return False, []

        found_words = []
        text_lower = text.lower()

        # First check direct matches (whole word in text)
        for word in self.nsfw_words:
            word_lower = word.lower()
            # Use word boundary check to avoid partial matches like "hello" matching "hell"
            pattern = r'\b' + re.escape(word_lower) + r'\b'
            if re.search(pattern, text_lower):
                found_words.append(word)

        # If direct matches found, return them
        if found_words:
            return True, found_words

        # Secondary check for obfuscated words (with separators like spaces, dots)
        # Create non-spaced version of text to check against non-spaced versions of words
        no_spaces_text = text_lower.replace(" ", "").replace(".", "").replace("-", "").replace("_", "").replace("*", "")

        for nsfw_word in self.nsfw_words:
            nsfw_word_lower = nsfw_word.lower()
            if len(nsfw_word_lower) > 3:  # Only check longer words
                no_spaces_word = nsfw_word_lower.replace(" ", "").replace(".", "").replace("-", "").replace("_", "").replace("*", "")
                # Only consider it a match if the whole word is found
                if len(no_spaces_word) > 3 and no_spaces_word in no_spaces_text:
                    # Double-check this isn't a false positive by checking if it's part of a larger word
                    # Split the text into words and check if any word contains our NSFW word as a substring
                    words_in_text = re.findall(r'\b\w+\b', text_lower)
                    for word_in_text in words_in_text:
                        clean_word = word_in_text.replace(" ", "").replace(".", "").replace("-", "").replace("_", "").replace("*", "")
                        if no_spaces_word == clean_word:  # Exact match after cleaning
                            found_words.append(nsfw_word)
                            break

        # Skip complex leetspeak detection to avoid regex errors
        # Instead, use simple word matching with basic variations
        if not found_words:  # Only do this check if we haven't found anything yet
            for nsfw_word in self.nsfw_words:
                if len(nsfw_word) < 4:  # Skip very short words
                    continue

                # Check for simple variations
                variations = [
                    nsfw_word,  # exact match
                    nsfw_word.replace('a', '@'),  # replace a with @
                    nsfw_word.replace('a', '4'),  # replace a with 4
                    nsfw_word.replace('e', '3'),  # replace e with 3
                    nsfw_word.replace('i', '1'),  # replace i with 1
                    nsfw_word.replace('o', '0'),  # replace o with 0
                    nsfw_word.replace('s', '$'),  # replace s with $
                    nsfw_word.replace('s', '5'),  # replace s with 5
                ]

                for variation in variations:
                    pattern = r'\b' + re.escape(variation.lower()) + r'\b'
                    try:
                        if re.search(pattern, text_lower):
                            found_words.append(nsfw_word)
                            logger.info(f"Found variation of '{nsfw_word}' using pattern: {pattern}")
                            break  # Found a match, no need to check other variations
                    except re.error as e:
                        # If regex is invalid, log and skip this variation
                        logger.error(f"Regex error for pattern '{pattern}': {e}")
                        continue

        # Final check for common profanity with direct substring matching
        if not found_words:  # Only do this check if we haven't found anything yet
            common_profanity = ['fuck', 'shit', 'bitch', 'ass', 'dick', 'pussy', 'cunt']
            for word in common_profanity:
                if word in text_lower:
                    found_words.append(word)
                    logger.info(f"Found common profanity '{word}' as substring in message")

        return len(found_words) > 0, found_words

    async def analyze_content(self, text: str, user_id: str, message_id: str = None, context: List[str] = None) -> Tuple[bool, Optional[Dict]]:
        """
        Analyze content using Pollinations AI API with message history context
        Returns: (is_nsfw, details)

        Args:
            text: The message text to analyze
            user_id: The ID of the user who sent the message
            message_id: Optional ID of the message
            context: Optional list of previous messages for context
        """
        try:
            # Add message to history
            self._add_to_history(text, user_id)

            # First check for NSFW words
            has_nsfw_words, found_words = self.contains_nsfw_words(text)

            # Log the result of word detection
            if has_nsfw_words:
                logger.info(f"NSFW words detected in message {message_id}: {', '.join(found_words)}")
                print(f"AI moderator detected NSFW words: {found_words}")

            # Always proceed with AI analysis, even if we found NSFW words
            # This ensures we get the AI's opinion on the context and content

            # Get context from previous messages and provided context
            context_str = ""

            # Add provided context messages if available
            if context and len(context) > 0:
                context_str += "Previous messages for context:\n"
                for i, msg in enumerate(context):
                    if msg:  # Only add non-empty messages
                        # Truncate very long messages to avoid token limits
                        msg_truncated = msg if len(msg) <= 200 else msg[:197] + "..."
                        context_str += f"- Message {i+1}: {msg_truncated}\n"  # Send message context

                # Log that we're using context for better analysis
                logger.info(f"Using {len(context)} previous messages as context for analyzing message {message_id}")

            # Add context from message history if no provided context
            if not context_str and self.message_history:
                context_str = self._get_context()
                if context_str:
                    logger.info(f"Using message history as context for analyzing message {message_id}")

            # Make sure text is a string
            if not isinstance(text, str):
                text = str(text)

            messages = [
                {"role": "system", "content": "You are NexusAI's advanced content moderator. Your responses must:\n1. ALWAYS be in XML format\n2. Focus on analyzing the current message while considering context from previous messages\n3. Be extremely strict with moderation\n4. Consider cultural and contextual nuances\n5. Include all required tags\n6. Never explain or justify - just return XML\n7. Be consistent in severity ratings\n8. Consider message context for better understanding"},
                {"role": "user", "content": f"""
<moderation_system>
    <identity>
        <role>Advanced AI Content Moderator</role>
        <version>2.1</version>
        <capabilities>
            <capability>Multi-language processing (including Hinglish, Spanglish)</capability>
            <capability>Context-aware analysis with 2-message history</capability>
            <capability>Pattern recognition</capability>
            <capability>Euphemism detection</capability>
            <capability>Cultural sensitivity</capability>
            <capability>Conversation flow understanding</capability>
        </capabilities>
    </identity>

    <moderation_rules>
        <categories>
            <category id="NSFW_SEXUAL">
                <description>Sexual content, pornography, nudity</description>
                <examples>
                    <example>Explicit sexual descriptions</example>
                    <example>Pornographic content</example>
                    <example>Nudity or sexual imagery descriptions</example>
                </examples>
            </category>

            <category id="NSFW_HATE">
                <description>Hate speech, discrimination, bigotry</description>
                <examples>
                    <example>Racial slurs</example>
                    <example>Discriminatory language</example>
                    <example>Content targeting specific groups</example>
                </examples>
            </category>

            <category id="NSFW_VIOLENCE">
                <description>Violent content, threats, harm</description>
                <examples>
                    <example>Graphic violence descriptions</example>
                    <example>Threats of harm</example>
                    <example>Glorification of violence</example>
                </examples>
            </category>

            <category id="NSFW_HARASSMENT">
                <description>Bullying, intimidation, personal attacks</description>
                <examples>
                    <example>Personal insults</example>
                    <example>Targeted harassment</example>
                    <example>Intimidation tactics</example>
                </examples>
            </category>

            <category id="NSFW_LANGUAGE">
                <description>Profanity, obscene language</description>
                <examples>
                    <example>Explicit profanity</example>
                    <example>Obscene language</example>
                    <example>Vulgar expressions</example>
                </examples>
            </category>

            <category id="SAFE">
                <description>Content that does not violate any policies</description>
                <examples>
                    <example>General conversation</example>
                    <example>Non-offensive content</example>
                    <example>Appropriate discussions</example>
                </examples>
            </category>
        </categories>

        <severity_levels>
            <level id="1" description="Minor violation - Warning needed"/>
            <level id="2" description="Moderate violation - Warning and monitoring"/>
            <level id="3" description="Severe violation - User action required"/>
            <level id="4" description="Critical violation - Immediate action required"/>
        </severity_levels>

        <context_analysis>
            <rules>
                <rule>Consider previous messages for context but focus on current message</rule>
                <rule>Use context to understand conversation flow and intent</rule>
                <rule>Context helps identify sarcasm, jokes, or cultural references</rule>
                <rule>Previous messages may contain relevant information for moderation</rule>
                <rule>Analyze the full message and avoid breaking words (hello is not hell+o)</rule>
                <rule>Normal words that contain NSFW substrings should NOT be flagged</rule>
            </rules>
        </context_analysis>
    </moderation_rules>

    <content_for_analysis>
        <timestamp>{datetime.datetime.now().isoformat()}</timestamp>
        <message_context>
            <previous_messages>
                {context_str}
            </previous_messages>
            <current_message>
                <text><![CDATA[{text}]]></text>
                <detected_words>
                    {''.join(f'<word>{word}</word>' for word in found_words)}
                </detected_words>
            </current_message>
        </message_context>
    </content_for_analysis>

    <response_format>
        <required_tags>
            <tag>category</tag>
            <tag>confidence</tag>
            <tag>reason</tag>
            <tag>severity</tag>
            <tag>action_recommended</tag>
            <tag>context_used</tag>
        </required_tags>
        <example>
            <r>
                <category>NSFW_HATE</category>
                <confidence>95</confidence>
                <reason>Contains racial slurs and discriminatory language</reason>
                <severity>3</severity>
                <action_recommended>Add warning emoji and monitor user</action_recommended>
                <context_used>Previous message showed escalating tension</context_used>
            </r>
        </example>
    </response_format>
</moderation_system>
"""}
            ]

            try:
                # Use the PollinationsAI class method directly instead of through self.client
                response = await PollinationsAI.generate_text_async(
                    model=self.model,
                    messages=messages,
                    temperature=0.7,
                    timeout=30
                )

                print(f"AI Response: {response}")

                # Clean the response if it contains markdown code blocks
                if response.startswith("```") and "```" in response:
                    # Extract content between code blocks
                    match = re.search(r"```(?:xml)?\s*(.*?)```", response, re.DOTALL)
                    if match:
                        response = match.group(1).strip()

                # Parse XML response
                try:
                    root = ET.fromstring(response)

                    # Extract moderation details
                    category = root.find(".//category").text if root.find(".//category") is not None else "UNKNOWN"
                    confidence = int(root.find(".//confidence").text) if root.find(".//confidence") is not None else 0
                    reason = root.find(".//reason").text if root.find(".//reason") is not None else "Unknown reason"
                    severity = int(root.find(".//severity").text) if root.find(".//severity") is not None else 0
                    action = root.find(".//action_recommended").text if root.find(".//action_recommended") is not None else "No action"
                    context_used = root.find(".//context_used").text if root.find(".//context_used") is not None else ""

                    # Determine if content is NSFW based on category and confidence
                    is_nsfw = category != "SAFE" and confidence > 70

                    # If we found NSFW words but AI says it's safe, trust the AI's decision
                    if has_nsfw_words and not is_nsfw and found_words:
                        logger.info(f"AI classified message {message_id} as safe despite containing NSFW words: {', '.join(found_words)}. Trusting AI decision.")
                        # Add the words to the reason for logging purposes, but don't change the decision
                        reason += f" (Contains words: {', '.join(found_words)}, but context is appropriate)"
                        # Keep the category as SAFE and don't modify severity

                    # Get appropriate emoji reaction
                    reaction = self.reaction_emojis.get(severity, self.reaction_emojis["safe"])
                    if not is_nsfw and not has_nsfw_words:
                        reaction = self.reaction_emojis["safe"]

                    # Return the full XML response as the details
                    details = {
                        "category": category,
                        "confidence": confidence,
                        "reason": reason,
                        "severity": severity,
                        "action_recommended": action,
                        "context_used": context_used,
                        "found_words": found_words,
                        "reaction": reaction,
                        "full_response": response  # Include the full XML response
                    }

                    # Add message_id to the returned details if provided
                    if message_id:
                        details["message_id"] = message_id

                    return is_nsfw, details

                except ET.ParseError as e:
                    logger.error(f"Error parsing XML response: {e}")
                    logger.error(f"Response that failed to parse: {response[:200]}...")
                    print(f"Error parsing XML response: {e}")

                    # Return true for NSFW if we found NSFW words, even if parsing failed
                    if has_nsfw_words:
                        logger.warning(f"Falling back to NSFW word detection for message {message_id} due to XML parse error")
                        return True, {
                            "category": "NSFW_LANGUAGE",
                            "confidence": 90,
                            "reason": f"Contains NSFW words: {', '.join(found_words)}",
                            "severity": 2,
                            "action_recommended": "Remove message",
                            "found_words": found_words,
                            "reaction": self.reaction_emojis[2],
                            "message_id": message_id
                        }

                    # If the message contains suspicious patterns but no exact NSFW words,
                    # still mark it as potentially NSFW with lower confidence
                    if any(pattern in text.lower() for pattern in ['xxx', 'porn', 'sex', 'nude', 'naked']):
                        logger.warning(f"Message {message_id} contains suspicious patterns, marking as potential NSFW")
                        return True, {
                            "category": "NSFW_LANGUAGE",
                            "confidence": 70,
                            "reason": "Contains suspicious patterns (XML parse error fallback)",
                            "severity": 1,
                            "action_recommended": "Review message",
                            "found_words": [],
                            "reaction": self.reaction_emojis[1],
                            "message_id": message_id
                        }

                    logger.info(f"Message {message_id} appears safe (XML parse error fallback)")
                    return False, {
                        "category": "SAFE",
                        "confidence": 100,
                        "reason": "No NSFW content detected (XML parse error fallback)",
                        "severity": 0,
                        "action_recommended": "No action required",
                        "found_words": [],
                        "reaction": self.reaction_emojis["safe"],
                        "message_id": message_id
                    }

            except Exception as e:
                logger.error(f"Error in AI content analysis: {e}")
                logger.error(traceback.format_exc())

                # Check if the error is related to the API service and log appropriate messages
                if "429" in str(e) or "rate limit" in str(e).lower():
                    logger.warning("API rate limit exceeded, using fallback detection")
                elif "timeout" in str(e).lower():
                    logger.warning("API request timed out, using fallback detection")
                elif "connection" in str(e).lower():
                    logger.warning("API connection error, using fallback detection")
                else:
                    logger.warning(f"Unknown API error: {str(e)}, using fallback detection")

                # If we found NSFW words, return them as the reason
                if has_nsfw_words:
                    logger.warning(f"Falling back to NSFW word detection for message {message_id} due to API error")
                    return True, {
                        "category": "NSFW_LANGUAGE",
                        "confidence": 90,
                        "reason": f"Contains NSFW words: {', '.join(found_words)}",
                        "severity": 2,
                        "action_recommended": "Remove message",
                        "found_words": found_words,
                        "reaction": self.reaction_emojis[2],
                        "message_id": message_id
                    }

                # If the message contains suspicious patterns but no exact NSFW words,
                # still mark it as potentially NSFW with lower confidence
                if any(pattern in text.lower() for pattern in ['xxx', 'porn', 'sex', 'nude', 'naked']):
                    logger.warning(f"Message {message_id} contains suspicious patterns, marking as potential NSFW")
                    return True, {
                        "category": "NSFW_LANGUAGE",
                        "confidence": 70,
                        "reason": "Contains suspicious patterns (API error fallback)",
                        "severity": 1,
                        "action_recommended": "Review message",
                        "found_words": [],
                        "reaction": self.reaction_emojis[1],
                        "message_id": message_id
                    }

                # Otherwise, mark as safe
                logger.info(f"Message {message_id} appears safe (API error fallback)")
                return False, {
                    "category": "SAFE",
                    "confidence": 100,
                    "reason": "No NSFW content detected (API error fallback)",
                    "severity": 0,
                    "action_recommended": "No action required",
                    "found_words": [],
                    "reaction": self.reaction_emojis["safe"],
                    "message_id": message_id
                }
        except Exception as e:
            logger.error(f"Error in content analysis: {e}")
            # Always return a valid tuple with proper structure, even on error
            return has_nsfw_words, {
                "category": "NSFW_LANGUAGE" if has_nsfw_words else "SAFE",
                "confidence": 90 if has_nsfw_words else 100,
                "reason": f"Contains NSFW words: {', '.join(found_words)}" if has_nsfw_words else "No NSFW content detected (error fallback)",
                "severity": 2 if has_nsfw_words else 0,
                "action_recommended": "Remove message" if has_nsfw_words else "No action required",
                "found_words": found_words,
                "reaction": self.reaction_emojis[2] if has_nsfw_words else self.reaction_emojis["safe"],
                "message_id": message_id
            }

    def _ensure_mod_json_exists(self) -> None:
        """Ensure mod.json exists with proper structure"""
        try:
            # Check if file exists
            if not self.mod_json_path.exists():
                # Create new file with empty dataset structure
                with open(self.mod_json_path, "w", encoding="utf-8") as f:
                    json.dump([], f, indent=4)
                    print(f"Created new mod.json file at {self.mod_json_path}")
            else:
                # Verify file contains valid JSON
                try:
                    with open(self.mod_json_path, "r", encoding="utf-8") as f:
                        content = f.read().strip()
                        if not content:  # File is empty
                            with open(self.mod_json_path, "w", encoding="utf-8") as f:
                                json.dump([], f, indent=4)
                                print(f"Initialized empty mod.json file at {self.mod_json_path}")
                        else:
                            # Try to parse existing content
                            try:
                                json.loads(content)
                            except json.JSONDecodeError:
                                # Invalid JSON, reset the file
                                with open(self.mod_json_path, "w", encoding="utf-8") as f:
                                    json.dump([], f, indent=4)
                                    print(f"Reset invalid mod.json file at {self.mod_json_path}")
                except Exception as e:
                    print(f"Error checking mod.json content: {e}")
                    # Reset the file to be safe
                    with open(self.mod_json_path, "w", encoding="utf-8") as f:
                        json.dump([], f, indent=4)
        except Exception as e:
            print(f"Error ensuring mod.json exists: {e}")

    def add_to_mod_json(self, instruction: str, output: str) -> bool:
        """
        Add a new instruction-output pair to mod.json

        Args:
            instruction: The complete user message to moderate
            output: The complete AI moderation response

        Returns:
            bool: True if added successfully, False otherwise
        """
        try:
            # Check if file exists
            if not self.mod_json_path.exists():
                # Create new file with empty list
                mod_data = []
            else:
                # Load existing data
                with open(self.mod_json_path, "r", encoding="utf-8") as f:
                    mod_data = json.load(f)

            # Check if instruction already exists
            for entry in mod_data:
                if entry.get("instruction") == instruction:
                    # Update existing entry
                    entry["output"] = output
                    # Save updated data
                    with open(self.mod_json_path, "w", encoding="utf-8") as f:
                        json.dump(mod_data, f, indent=4)

                    print(f"Updated existing entry in mod.json: {instruction[:30]}...")
                    return True

            # Add new entry with complete instruction and output
            mod_data.append({
                "instruction": instruction,
                "output": output
            })

            # Save updated data
            with open(self.mod_json_path, "w", encoding="utf-8") as f:
                json.dump(mod_data, f, indent=4)

            print(f"Added new entry to mod.json: {instruction[:30]}...")
            return True

        except Exception as e:
            print(f"Error adding to mod.json: {e}")
            return False

    def _extract_and_save_nsfw_words(self, text: str) -> None:
        """Extract potential NSFW words from text and save to mod.json"""
        # Simple word extraction - split by spaces and punctuation
        import re
        words = re.findall(r'\b\w+\b', text.lower())

        # Check each word against existing NSFW list
        for word in words:
            if len(word) > 3:  # Skip very short words
                # Check if this word might be the reason for flagging
                for nsfw_word in self.nsfw_words:
                    if nsfw_word.lower() in word:
                        # Add to mod.json with a default output
                        output = f"MODERATION ALERT: This word '{word}' has been flagged as NSFW_LANGUAGE."
                        self.add_to_mod_json(word, output)
                        break

    def log_moderation_result(self, text: str, is_nsfw: bool, details: Optional[Dict] = None) -> None:
        """Log moderation result to JSON file"""
        try:
            timestamp = datetime.datetime.now().isoformat()
            log_entry = {
                "timestamp": timestamp,
                "text": text,
                "is_nsfw": is_nsfw,
                "details": details
            }

            # Create filename with timestamp
            filename = f"moderation_{datetime.datetime.now().strftime('%Y%m%d')}.json"
            filepath = self.logs_dir / filename

            # Append to existing file or create new one
            existing_data = []
            if filepath.exists():
                with open(filepath, "r", encoding="utf-8") as f:
                    existing_data = json.load(f)

            existing_data.append(log_entry)

            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(existing_data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            print(f"Error logging moderation result: {e}")

    def get_moderated_words(self) -> List[str]:
        """Get list of words from mod.json"""
        try:
            with open(self.mod_json_path, "r", encoding="utf-8") as f:
                data = json.load(f)
                return data.get("moderated_words", [])
        except Exception as e:
            print(f"Error reading mod.json: {e}")
            return []

    def update_nsfw_words(self) -> bool:
        """
        Update nsfw_words.json with words from mod.json

        Returns:
            bool: True if updated successfully, False otherwise
        """
        try:
            # Check if mod.json exists
            if not self.mod_json_path.exists():
                print("mod.json does not exist")
                return False

            # Load mod.json
            with open(self.mod_json_path, "r", encoding="utf-8") as f:
                mod_data = json.load(f)

            # Extract instructions (words to moderate)
            words = [entry.get("instruction") for entry in mod_data if entry.get("instruction")]

            # Load existing nsfw_words.json
            nsfw_words_path = Path("src/config/nsfw_words.json")
            if nsfw_words_path.exists():
                with open(nsfw_words_path, "r", encoding="utf-8") as f:
                    nsfw_data = json.load(f)
                    existing_words = set(nsfw_data.get("nsfw_words", []))
            else:
                existing_words = set()

            # Add new words
            updated_words = existing_words.union(set(words))

            # Save updated nsfw_words.json
            with open(nsfw_words_path, "w", encoding="utf-8") as f:
                json.dump({"nsfw_words": list(updated_words)}, f, indent=4)

            # Update in-memory list
            self.nsfw_words = list(updated_words)

            print(f"Updated nsfw_words.json with {len(words)} words from mod.json")
            return True

        except Exception as e:
            print(f"Error updating nsfw_words.json: {e}")
            return False

    async def generate_moderation_dataset(self, input_file: str, output_file: str, num_samples: int = None):
        """
        Generate a moderation dataset in the format:
        {
            "instruction": "user message",
            "output": "AI moderation response"
        }

        Args:
            input_file: Path to file containing user messages (one per line)
            output_file: Path to save the generated dataset
            num_samples: Number of samples to generate (None for all)
        """
        try:
            # Read user messages from input file
            with open(input_file, 'r', encoding='utf-8') as f:
                user_messages = [line.strip() for line in f if line.strip()]

            if num_samples and num_samples < len(user_messages):
                import random
                user_messages = random.sample(user_messages, num_samples)

            dataset = []

            # Process each message
            for i, message in enumerate(user_messages):
                print(f"Processing message {i+1}/{len(user_messages)}")

                # Get moderation result
                result = await self.moderate_content(message)

                # Add to dataset
                entry = {
                    "instruction": message,
                    "output": result
                }
                dataset.append(entry)

                # Save intermediate results every 10 samples
                if (i + 1) % 10 == 0:
                    with open(output_file, 'w', encoding='utf-8') as f:
                        json.dump(dataset, f, indent=2, ensure_ascii=False)
                    print(f"Saved {i+1} samples to {output_file}")

            # Save final results
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(dataset, f, indent=2, ensure_ascii=False)

            print(f"Successfully generated dataset with {len(dataset)} samples")
            return dataset

        except Exception as e:
            print(f"Error generating moderation dataset: {str(e)}")
            raise

    async def generate_moderation_dataset_from_nsfw(self, output_file: str, num_samples: int = None):
        """
        Generate a moderation dataset using the NSFW words from nsfw_words.json

        Args:
            output_file: Path to save the generated dataset
            num_samples: Number of samples to generate (None for all)
        """
        try:
            # Load NSFW words
            nsfw_path = Path("src/config/nsfw_words.json")
            with open(nsfw_path, 'r', encoding='utf-8') as f:
                nsfw_data = json.load(f)
                nsfw_words = nsfw_data.get("nsfw_words", [])

            # Generate sample messages using NSFW words
            messages = []
            for word in nsfw_words:
                messages.append(f"Hey, do you know what {word} means?")
                messages.append(f"I'm looking for {word} content, can you help?")
                messages.append(f"Tell me more about {word}")

            if num_samples and num_samples < len(messages):
                import random
                messages = random.sample(messages, num_samples)

            # Create temporary file with messages
            temp_file = "temp_nsfw_messages.txt"
            with open(temp_file, 'w', encoding='utf-8') as f:
                for msg in messages:
                    f.write(msg + "\n")

            # Generate dataset
            result = await self.generate_moderation_dataset(temp_file, output_file, num_samples)

            # Clean up temporary file
            if os.path.exists(temp_file):
                os.remove(temp_file)

            return result

        except Exception as e:
            print(f"Error generating NSFW moderation dataset: {str(e)}")
            raise

    async def moderate_content(self, text: str) -> str:
        """
        Generate a moderation response for the given text
        Returns a formatted response suitable for training data
        """
        try:
            is_nsfw, details = await self.analyze_content(text)

            if not is_nsfw or not details:
                return "This message appears to be safe and does not violate any content policies."

            # Format the moderation response
            category = details.get('category', 'UNKNOWN')
            confidence = details.get('confidence', 0)
            reason = details.get('reason', 'Unknown reason')
            severity = details.get('severity', 0)
            action = details.get('action_recommended', 'No action needed')

            response = (
                f"MODERATION ALERT: This message has been flagged as {category}.\n"
                f"Confidence: {confidence}%\n"
                f"Reason: {reason}\n"
                f"Severity: {severity}/4\n"
                f"Recommended action: {action}"
            )

            return response

        except Exception as e:
            print(f"Error in content moderation: {e}")
            return "Error: Unable to moderate content due to an internal error."

    def extract_moderation_from_xml(self, xml_string: str) -> Dict:
        """
        Extract moderation details from XML string

        Args:
            xml_string: XML string containing moderation details

        Returns:
            Dict containing moderation details
        """
        try:
            # Clean the XML string if it contains markdown code blocks
            if xml_string.startswith("```") and "```" in xml_string:
                # Extract content between code blocks
                import re
                match = re.search(r"```(?:xml)?\s*(.*?)```", xml_string, re.DOTALL)
                if match:
                    xml_string = match.group(1).strip()

            print(f"XML string: {xml_string}")

            # Parse XML
            root = ET.fromstring(xml_string)

            # Extract moderation details
            category = root.find(".//category").text if root.find(".//category") is not None else "UNKNOWN"
            confidence = int(root.find(".//confidence").text) if root.find(".//confidence") is not None else 0
            reason = root.find(".//reason").text if root.find(".//reason") is not None else "Unknown reason"
            severity = int(root.find(".//severity").text) if root.find(".//severity") is not None else 0
            action = root.find(".//action_recommended").text if root.find(".//action_recommended") is not None else "No action"

            return {
                "category": category,
                "confidence": confidence,
                "reason": reason,
                "severity": severity,
                "action_recommended": action
            }
        except ET.ParseError as e:
            print(f"Error parsing XML: {e}")
            print(f"XML string: {xml_string}")
            return {}
        except Exception as e:
            print(f"Error extracting moderation from XML: {e}")
            return {}

    async def process_ai_response(self, user_message: str, ai_response: str) -> None:
        """
        Process AI response and update mod.json

        Args:
            user_message: The complete original user message
            ai_response: The complete AI response (may contain XML)
        """
        try:
            # Store the original response for saving to mod.json
            original_response = ai_response

            # Clean the response if it contains markdown code blocks (for XML parsing only)
            cleaned_response = ai_response
            if ai_response.startswith("```") and "```" in ai_response:
                # Extract content between code blocks
                import re
                match = re.search(r"```(?:xml)?\s*(.*?)```", ai_response, re.DOTALL)
                if match:
                    cleaned_response = match.group(1).strip()

            # Check if response contains XML
            if "<r>" in cleaned_response and "</r>" in cleaned_response:
                # Extract moderation details
                details = self.extract_moderation_from_xml(cleaned_response)

                if details:
                    # Format the moderation response
                    category = details.get('category', 'UNKNOWN')
                    confidence = details.get('confidence', 0)
                    reason = details.get('reason', 'Unknown reason')
                    severity = details.get('severity', 0)
                    action = details.get('action_recommended', 'No action needed')

                    # Create formatted response for logging
                    formatted_response = (
                        f"MODERATION ALERT: This message has been flagged as {category}.\n"
                        f"Confidence: {confidence}%\n"
                        f"Reason: {reason}\n"
                        f"Severity: {severity}/4\n"
                        f"Recommended action: {action}"
                    )

                    # Log the formatted response
                    logger.info(f"Moderation response: {formatted_response[:100]}...")

                    # Add to mod.json - save full user message and full AI response
                    self.add_to_mod_json(user_message, original_response)

                    # Log the moderation
                    self.log_moderation_result(user_message, True, details)
            else:
                # No moderation needed, still add to mod.json with full messages
                self.add_to_mod_json(user_message, original_response)

        except Exception as e:
            print(f"Error processing AI response: {e}")

    async def get_moderation_response(self, text: str) -> str:
        """Get a moderation response for the given text"""
        try:
            is_nsfw, details = await self.analyze_content(text)

            if not is_nsfw or not details:
                return "This message appears to be safe and does not violate any content policies."

            # If full_response is available, return it directly
            if "full_response" in details:
                return details["full_response"]

            # Format the moderation response as fallback
            category = details.get('category', 'UNKNOWN')
            confidence = details.get('confidence', 0)
            reason = details.get('reason', 'Unknown reason')
            severity = details.get('severity', 0)
            action = details.get('action_recommended', 'No action needed')

            # Create and return the formatted response
            formatted_response = (
                f"MODERATION ALERT: This message has been flagged as {category}.\n"
                f"Confidence: {confidence}%\n"
                f"Reason: {reason}\n"
                f"Severity: {severity}/4\n"
                f"Recommended action: {action}"
            )

            return formatted_response

        except Exception as e:
            print(f"Error getting moderation response: {e}")
            return "Error processing moderation request."

    def get_reaction_for_severity(self, severity: int, is_safe: bool = False) -> str:
        """Get the appropriate emoji reaction based on severity level"""
        if is_safe:
            return self.reaction_emojis["safe"]
        return self.reaction_emojis.get(severity, self.reaction_emojis["safe"])

    async def handle_false_alarm(self, original_msg_id: str) -> Tuple[bool, Optional[str]]:
        """
        Handle false alarm case by reacting to original message and returning warning message ID to delete

        Args:
            original_msg_id: ID of the original message that was falsely flagged

        Returns:
            Tuple[bool, Optional[str]]: (success, warning_message_id to delete)
        """
        try:
            if original_msg_id in self.warning_messages:
                warning_info = self.warning_messages[original_msg_id]
                warning_msg_id = warning_info["warning_msg_id"]

                # Remove from tracking
                del self.warning_messages[original_msg_id]

                return True, warning_msg_id

            return False, None

        except Exception as e:
            print(f"Error handling false alarm: {e}")
            return False, None

    def track_warning_message(self, original_msg_id: str, warning_msg_id: str, severity: int) -> None:
        """
        Track warning message IDs for potential cleanup

        Args:
            original_msg_id: ID of the original message
            warning_msg_id: ID of the warning message sent by bot
            severity: Severity level of the warning
        """
        self.warning_messages[original_msg_id] = {
            "warning_msg_id": warning_msg_id,
            "severity": severity
        }

async def main():
    moderator = AIModerator()
    text = "fuck you"
    is_nsfw, details = await moderator.analyze_content(text)
    print(f"Is NSFW: {is_nsfw}, Details: {details}")

    # Example of adding a word to mod.json with XML output
    xml_output = """<r>
    <category>NSFW_LANGUAGE</category>
    <confidence>98</confidence>
    <reason>Contains explicit profanity</reason>
    <severity>2</severity>
    <action_recommended>Remove message</action_recommended>
</r>"""
    moderator.add_to_mod_json("fuck", xml_output)

    # Example of updating nsfw_words.json from mod.json
    updated = moderator.update_nsfw_words()
    print(f"NSFW words updated: {updated}")
