import React from 'react';
import { Card, CardContent, Typography, Box } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledCard = styled(Card)(({ theme }) => ({
  height: '100%',
  borderRadius: 16,
  border: '1px solid #e2e8f0',
  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  },
}));

const IconContainer = styled(Box)(({ theme, gradient }) => ({
  width: 48,
  height: 48,
  borderRadius: 12,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  background: gradient || 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
  color: 'white',
  marginBottom: theme.spacing(2),
}));

const ModernStatsCard = ({ 
  icon, 
  title, 
  value, 
  subtitle, 
  gradient,
  trend,
  trendColor = 'success.main'
}) => {
  return (
    <StyledCard>
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 2 }}>
          <IconContainer gradient={gradient}>
            {icon}
          </IconContainer>
          {trend && (
            <Typography
              variant="body2"
              sx={{
                color: trendColor,
                fontWeight: 600,
                fontSize: '0.875rem',
              }}
            >
              {trend}
            </Typography>
          )}
        </Box>
        
        <Typography
          variant="h3"
          sx={{
            fontWeight: 700,
            mb: 1,
            color: 'text.primary',
          }}
        >
          {value}
        </Typography>
        
        <Typography
          variant="h6"
          sx={{
            fontWeight: 600,
            mb: 1,
            color: 'text.primary',
          }}
        >
          {title}
        </Typography>
        
        {subtitle && (
          <Typography
            variant="body2"
            sx={{
              color: 'text.secondary',
              lineHeight: 1.5,
            }}
          >
            {subtitle}
          </Typography>
        )}
      </CardContent>
    </StyledCard>
  );
};

export default ModernStatsCard;
