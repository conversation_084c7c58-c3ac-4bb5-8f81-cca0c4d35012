"""TypeGPT API service for content moderation and OCR 🤖"""

import asyncio
import aiohttp
import json
import logging
from typing import Dict, Optional, Union, List
import base64
from pathlib import Path

logger = logging.getLogger(__name__)

class TypeGPTAPI:
    """TypeGPT API client for content moderation and OCR services"""
    
    def __init__(self):
        self.base_url = "https://mono.typegpt.net/v1"
        self.headers = {
            'accept': 'application/json',
            'Content-Type': 'application/json'
        }
        self.session = None
        
    async def _get_session(self):
        """Get or create aiohttp session"""
        if self.session is None or self.session.closed:
            connector = aiohttp.TCPConnector(limit=10, limit_per_host=5)
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(
                connector=connector, 
                timeout=timeout,
                headers=self.headers
            )
        return self.session
    
    async def moderate_text(self, text: str, model: str = "text-moderation-007") -> Optional[Dict]:
        """
        Moderate text content using TypeGPT moderation API
        
        Args:
            text: Text content to moderate
            model: Moderation model to use (default: text-moderation-007)
            
        Returns:
            Dict containing moderation results or None if failed
        """
        try:
            session = await self._get_session()
            
            payload = {
                "input": text,
                "model": model
            }
            
            logger.info(f"🔍 TypeGPT: Moderating text with model {model}")
            logger.info(f"📝 Text snippet: {text[:100]}{'...' if len(text) > 100 else ''}")
            
            async with session.post(f"{self.base_url}/moderations", json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"✅ TypeGPT moderation successful")
                    
                    # Log results for debugging
                    if 'results' in result and len(result['results']) > 0:
                        moderation_result = result['results'][0]
                        flagged = moderation_result.get('flagged', False)
                        categories = moderation_result.get('categories', {})
                        active_categories = [cat for cat, active in categories.items() if active]
                        
                        logger.info(f"🚨 Flagged: {flagged}")
                        if active_categories:
                            logger.info(f"📊 Active categories: {active_categories}")
                        
                    return result
                else:
                    error_text = await response.text()
                    logger.error(f"❌ TypeGPT moderation failed: HTTP {response.status}")
                    logger.error(f"🔍 Error details: {error_text}")
                    return None
                    
        except asyncio.TimeoutError:
            logger.error("⏱️ TypeGPT moderation timeout")
            return None
        except Exception as e:
            logger.error(f"💥 TypeGPT moderation error: {e}")
            return None
    
    async def extract_text_from_image(self, image_url: str = None, image_b64: str = None) -> Optional[Dict]:
        """
        Extract text from image using TypeGPT OCR API
        
        Args:
            image_url: URL of the image to process
            image_b64: Base64 encoded image data
            
        Returns:
            Dict containing OCR results or None if failed
        """
        try:
            if not image_url and not image_b64:
                logger.error("❌ Either image_url or image_b64 must be provided")
                return None
                
            session = await self._get_session()
            
            payload = {}
            if image_url:
                payload["image_url"] = image_url
                logger.info(f"🖼️ TypeGPT OCR: Processing image from URL")
            elif image_b64:
                payload["image_b64"] = image_b64
                logger.info(f"🖼️ TypeGPT OCR: Processing base64 image")
            
            async with session.post(f"{self.base_url}/ocr", json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"✅ TypeGPT OCR successful")
                    
                    # Log OCR results
                    ocr_text = result.get('ocr_text', '')
                    if ocr_text:
                        # Parse the OCR text if it's JSON-like
                        try:
                            # Handle the format {'': 'actual text'}
                            if ocr_text.startswith('{') and ocr_text.endswith('}'):
                                parsed = json.loads(ocr_text)
                                if isinstance(parsed, dict):
                                    # Get the first value from the dict
                                    actual_text = list(parsed.values())[0] if parsed else ''
                                    result['parsed_text'] = actual_text
                                    logger.info(f"📝 Extracted text: {actual_text[:100]}{'...' if len(actual_text) > 100 else ''}")
                                else:
                                    result['parsed_text'] = ocr_text
                            else:
                                result['parsed_text'] = ocr_text
                        except json.JSONDecodeError:
                            result['parsed_text'] = ocr_text
                            
                        logger.info(f"📝 Raw OCR result: {ocr_text[:200]}{'...' if len(ocr_text) > 200 else ''}")
                    else:
                        logger.info("📝 No text detected in image")
                        result['parsed_text'] = ''
                    
                    return result
                else:
                    error_text = await response.text()
                    logger.error(f"❌ TypeGPT OCR failed: HTTP {response.status}")
                    logger.error(f"🔍 Error details: {error_text}")
                    return None
                    
        except asyncio.TimeoutError:
            logger.error("⏱️ TypeGPT OCR timeout")
            return None
        except Exception as e:
            logger.error(f"💥 TypeGPT OCR error: {e}")
            return None
    
    async def image_file_to_base64(self, image_path: str) -> Optional[str]:
        """
        Convert image file to base64 string
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Base64 encoded image string or None if failed
        """
        try:
            image_path = Path(image_path)
            if not image_path.exists():
                logger.error(f"❌ Image file not found: {image_path}")
                return None
                
            with open(image_path, 'rb') as image_file:
                image_data = image_file.read()
                base64_string = base64.b64encode(image_data).decode('utf-8')
                logger.info(f"✅ Image converted to base64 ({len(base64_string)} chars)")
                return base64_string
                
        except Exception as e:
            logger.error(f"💥 Error converting image to base64: {e}")
            return None
    
    async def analyze_flagged_content(self, moderation_result: Dict) -> Dict:
        """
        Analyze flagged content from moderation result and provide enhanced details
        
        Args:
            moderation_result: Result from moderate_text()
            
        Returns:
            Enhanced analysis dict with severity, categories, etc.
        """
        try:
            if not moderation_result or 'results' not in moderation_result:
                return {
                    'flagged': False,
                    'severity': 0,
                    'categories': [],
                    'reason': 'No moderation result available'
                }
            
            result = moderation_result['results'][0]
            flagged = result.get('flagged', False)
            categories = result.get('categories', {})
            category_scores = result.get('category_scores', {})
            reason = result.get('reason', 'No reason provided')
            
            # Calculate severity based on categories and scores
            severity = 0
            active_categories = []
            
            severity_weights = {
                'violence': 3,
                'violence/graphic': 4,
                'sexual': 3,
                'sexual/minors': 4,
                'hate': 2,
                'hate/threatening': 4,
                'harassment': 2,
                'self-harm': 3
            }
            
            for category, is_flagged in categories.items():
                if is_flagged:
                    active_categories.append(category)
                    category_severity = severity_weights.get(category, 1)
                    category_score = category_scores.get(category, 0)
                    # Weight by both category type and confidence score
                    weighted_severity = category_severity * category_score
                    severity = max(severity, weighted_severity)
            
            # Normalize severity to 1-4 scale
            if severity > 0:
                severity = min(4, max(1, int(severity * 4)))
            
            analysis = {
                'flagged': flagged,
                'severity': severity,
                'categories': active_categories,
                'category_scores': category_scores,
                'reason': reason,
                'confidence': max(category_scores.values()) * 100 if category_scores else 0,
                'api_source': 'TypeGPT',
                'model': moderation_result.get('model', 'text-moderation-007')
            }
            
            logger.info(f"📊 TypeGPT Analysis - Flagged: {flagged}, Severity: {severity}, Categories: {active_categories}")
            
            return analysis
            
        except Exception as e:
            logger.error(f"💥 Error analyzing flagged content: {e}")
            return {
                'flagged': False,
                'severity': 0,
                'categories': [],
                'reason': f'Analysis error: {e}'
            }
    
    async def close(self):
        """Close the aiohttp session"""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None
            logger.info("✅ TypeGPT API session closed")
    
    async def __aenter__(self):
        """Async context manager entry"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()


# Global instance for reuse
_typegpt_instance = None

async def get_typegpt_client() -> TypeGPTAPI:
    """Get global TypeGPT API client instance"""
    global _typegpt_instance
    if _typegpt_instance is None:
        _typegpt_instance = TypeGPTAPI()
    return _typegpt_instance

async def cleanup_typegpt_client():
    """Cleanup global TypeGPT client"""
    global _typegpt_instance
    if _typegpt_instance:
        await _typegpt_instance.close()
        _typegpt_instance = None