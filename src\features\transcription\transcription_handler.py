import logging
import tempfile
from pathlib import Path
import asyncio
from concurrent.futures import ThreadPoolExecutor
from functools import partial
from typing import List, Optional, Tuple, Any
import aiohttp
import aiofiles
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, Message, Voice, Audio
from telegram.ext import <PERSON><PERSON><PERSON><PERSON><PERSON>, CommandHandler, CallbackQueryHandler
from telegram.constants import ParseMode
from .transcription_service import AssemblyAITranscriber, TranscriptionError, TranscriptionResult

logger = logging.getLogger(__name__)

class TranscriptionHandler:
    """Handles speech-to-text transcription commands."""
    
    # Define Telegram's message length limit
    MAX_MESSAGE_LENGTH = 4096
    
    def __init__(self):
        """Initialize with thread pool for CPU-bound tasks"""
        self.executor = None  # Initialize as None
        self.session = None  # aiohttp session for async HTTP requests
        self.download_semaphore = asyncio.Semaphore(3)  # Limit concurrent downloads
        self.transcribe_semaphore = asyncio.Semaphore(2)  # Limit concurrent transcriptions
        self.temp_files = set()  # Track temporary files for cleanup
        self.transcriber = AssemblyAITranscriber()
        self._ensure_executor()  # Ensure executor is created when needed
        
    def _ensure_executor(self):
        """Ensure the executor is created and running"""
        if self.executor is None or self.executor._shutdown:
            self.executor = ThreadPoolExecutor(max_workers=4)
            logger.info("Created new ThreadPoolExecutor for transcription tasks")
    
    async def send_long_message(self, message: Message, text: str, **kwargs) -> List[Message]:
        """
        Send a message that might exceed Telegram's message length limit.
        Splits the message into multiple parts if necessary.
        
        Args:
            message: The original message to edit or reply to
            text: The text to send
            **kwargs: Additional arguments for send_message or edit_message_text
            
        Returns:
            List of sent messages
        """
        sent_messages = []
        
        # If text is within limits, just send/edit it
        if len(text) <= self.MAX_MESSAGE_LENGTH:
            if hasattr(message, 'edit_text'):
                sent_message = await message.edit_text(text, **kwargs)
            else:
                sent_message = await message.reply_text(text, **kwargs)
            return [sent_message]
        
        # Split text into chunks
        parts = []
        for i in range(0, len(text), self.MAX_MESSAGE_LENGTH):
            part = text[i:i + self.MAX_MESSAGE_LENGTH]
            parts.append(part)
        
        # Send each part
        for i, part in enumerate(parts):
            if i == 0 and hasattr(message, 'edit_text'):
                # Edit the original message with the first part
                sent_message = await message.edit_text(
                    f"{part}\n\n(Part {i+1}/{len(parts)})",
                    **kwargs
                )
            else:
                # Reply with subsequent parts
                sent_message = await message.reply_text(
                    f"{part}\n\n(Part {i+1}/{len(parts)})",
                    **kwargs
                )
            sent_messages.append(sent_message)
            
        return sent_messages

    async def get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession()
        return self.session

    async def get_file_from_message(self, message: Any) -> Tuple[Optional[Any], str]:
        """Get file object and type from message asynchronously"""
        if message.voice:
            return message.voice, "voice message"
        elif message.audio:
            return message.audio, "audio file"
        elif message.document:
            mime_type = message.document.mime_type or ""
            if mime_type.startswith("audio/"):
                return message.document, "audio document"
        return None, ""

    async def download_audio(self, audio_obj: Any) -> Optional[str]:
        """Download audio file asynchronously with rate limiting"""
        async with self.download_semaphore:
            try:
                file = await audio_obj.get_file()
                
                # Create temporary file with a unique name
                temp_file = tempfile.NamedTemporaryFile(
                    suffix=f"_{audio_obj.file_unique_id}.ogg",
                    delete=False
                )
                temp_path = temp_file.name
                temp_file.close()
                self.temp_files.add(temp_path)

                # Download file in chunks
                async with (await self.get_session()).get(file.file_path) as response:
                    if response.status == 200:
                        async with aiofiles.open(temp_path, 'wb') as f:
                            async for chunk in response.content.iter_chunked(8192):
                                await f.write(chunk)
                        return temp_path
                    else:
                        logger.error(f"Download failed with status {response.status}")
                        return None
            except Exception as e:
                logger.error(f"Error downloading audio: {e}")
                return None

    async def transcribe_audio(self, audio_path: str) -> dict:
        """Transcribe audio file asynchronously"""
        self._ensure_executor()  # Ensure executor is available
        
        async with self.transcribe_semaphore:
            try:
                # Run CPU-intensive transcription in thread pool
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(
                    self.executor,
                    partial(self.transcriber.transcribe_file, audio_path)
                )
                # Wait for the result if it's a coroutine
                if asyncio.iscoroutine(result):
                    result = await result
                return result
            except Exception as e:
                logger.error(f"Transcription error in transcribe_audio: {e}")
                raise

    def format_transcription_result(self, result: dict) -> str:
        """Format the transcription result into a readable message."""
        try:
            text = result.get('text', '')
            if not text:
                return "❌ No transcription text available."

            formatted = f"📝 <b>Transcription:</b>\n\n{text}"
            
            # Add metadata if available
            if 'confidence' in result:
                confidence = result['confidence'] * 100
                formatted += f"\n\n<i>Confidence: {confidence:.1f}%</i>"
                
            return formatted
            
        except Exception as e:
            logger.error(f"Error formatting transcription result: {e}")
            return "❌ Error formatting transcription result."

    async def handle_stt(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /stt command - alias for transcribe."""
        await self.handle_transcription(update, context)

    async def handle_transcription(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle transcription command"""
        if not update.effective_message or not update.effective_message.reply_to_message:
            await update.effective_message.reply_text(
                "❌ Please reply to a voice message or audio file.",
                parse_mode=ParseMode.HTML
            )
            return

        # Get the audio message that was replied to
        audio_obj, file_type = await self.get_file_from_message(
            update.effective_message.reply_to_message
        )
        
        if not audio_obj:
            await update.effective_message.reply_text(
                "❌ Please reply to a valid audio file.",
                parse_mode=ParseMode.HTML
            )
            return

        processing_msg = await update.effective_message.reply_text(
            f"🔄 Processing {file_type}...",
            parse_mode=ParseMode.HTML
        )

        temp_path = None
        try:
            temp_path = await self.download_audio(audio_obj)
            if not temp_path:
                raise Exception("Failed to download audio")

            result = await self.transcribe_audio(temp_path)
            response = self.format_transcription_result(result)
            
            # Use the new send_long_message method instead of direct edit
            await self.send_long_message(
                processing_msg,
                response,
                parse_mode=ParseMode.HTML
            )

        except Exception as e:
            logger.error(f"Transcription error: {e}")
            await processing_msg.edit_text(
                "❌ <b>An error occurred during transcription</b>\n"
                "Please try again later.",
                parse_mode=ParseMode.HTML
            )
        finally:
            if temp_path and temp_path in self.temp_files:
                try:
                    Path(temp_path).unlink(missing_ok=True)
                    self.temp_files.remove(temp_path)
                except Exception as e:
                    logger.error(f"Error cleaning up temp file: {e}")

    async def send_usage_instructions(self, message: Message) -> None:
        """Send usage instructions for transcription."""
        instructions = (
            "📝 <b>Speech to Text Transcription</b>\n\n"
            "To transcribe audio:\n"
            "1. Send or forward a voice message/audio file\n"
            "2. Reply to it with /stt or /transcribe\n\n"
            "<b>Supported formats:</b>\n"
            "• Voice messages\n"
            "• Audio files (OGG, MP3, WAV, M4A, OPUS)\n\n"
            "<i>Note: Maximum duration: 10 minutes</i>"
        )
        await message.reply_text(instructions, parse_mode=ParseMode.HTML)

    async def handle_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle button callbacks."""
        query = update.callback_query
        if not query or not query.data:
            return

        await query.answer()
        
        if query.data == "new":
            await self.send_usage_instructions(query.message)
        elif query.data.startswith("copy_"):
            text = query.data[5:]  # Remove 'copy_' prefix
            await query.message.reply_text(
                text,
                parse_mode=ParseMode.HTML
            )

    def get_handlers(self) -> list:
        """Return all handlers for this feature."""
        return [
            CommandHandler(['stt', 'transcribe'], self.handle_transcription),
            CallbackQueryHandler(self.handle_callback, pattern='^(copy_|new)')
        ]

    async def cleanup_resources(self):
        """Cleanup resources"""
        # Close aiohttp session
        if self.session and not self.session.closed:
            await self.session.close()
        
        # Cleanup any remaining temp files
        for temp_file in self.temp_files.copy():
            try:
                Path(temp_file).unlink(missing_ok=True)
                self.temp_files.remove(temp_file)
            except Exception as e:
                logger.error(f"Error cleaning up temp file during shutdown: {e}")
        
        # Shutdown thread pool if it exists
        if self.executor and not self.executor._shutdown:
            self.executor.shutdown(wait=False)
            self.executor = None
            logger.info("Shut down ThreadPoolExecutor for transcription tasks")
