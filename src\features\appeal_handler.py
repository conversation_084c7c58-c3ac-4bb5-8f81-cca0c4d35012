"""
🙏 Appeal Handler Module
--------------------
Let banned users request for another chance fr fr!
"""

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.constants import ParseMode
from telegram.error import TelegramError
import re
from datetime import datetime, timedelta

class AppealHandler:
    def __init__(self, db):
        self.db = db
        self.default_group = -1001846470840
        
    async def handle_appeal(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle appeal requests from banned users"""
        try:
            user = update.effective_user
            message = update.message
            
            # Check if the command is used in a private chat
            if message.chat.type != "private":
                await message.reply_text(
                    "❌ The /appeal command can only be used in private chat with the bot.\n"
                    "Please DM me to submit your appeal.",
                    parse_mode=ParseMode.HTML
                )
                return
                
            if not message.text or len(message.text.split()) < 2:
                await message.reply_text(
                    "<b>❌ YO HOLD UP!</b>\n\n"
                    "<b>How to Appeal Your Ban:</b>\n\n"
                    "1️⃣ Get the group ID or link\n"
                    "2️⃣ Write your appeal message\n"
                    "3️⃣ Send it like this:\n"
                    "<code>/appeal [group ID/link] [your message]</code>\n\n"
                    "<b>Your appeal should include:</b>\n"
                    "• Why you should be unbanned\n"
                    "• What you learned\n"
                    "• How you'll do better\n\n"
                    "<i>Example:</i>\n"
                    "<code>/appeal -1001846470840 I'm sorry for spamming. I understand now why it's wrong and promise to follow the rules. Please give me another chance!</code>",
                    parse_mode=ParseMode.HTML
                )
                return
                
            # Get group ID from link or first argument
            group_id = self.default_group
            appeal_text = " ".join(context.args)
            
            # Check if first argument is a group link
            link_match = re.search(r't\.me/(?:\+)?([a-zA-Z0-9_-]+)', context.args[0])
            if link_match:
                try:
                    chat = await context.bot.get_chat(f"@{link_match.group(1)}")
                    group_id = chat.id
                    appeal_text = " ".join(context.args[1:])
                except TelegramError:
                    pass
            elif context.args[0].startswith('-100'):
                try:
                    group_id = int(context.args[0])
                    appeal_text = " ".join(context.args[1:])
                except ValueError:
                    pass
            
            if not appeal_text:
                await message.reply_text(
                    "❌ Yo you gotta tell us why we should unban you!\n"
                    "Add your message after the group link/ID.",
                    parse_mode=ParseMode.HTML
                )
                return
                
            # Forward appeal to group
            try:
                # Add appeal to database
                appeal_id = await self.db.add_appeal(user.id, group_id, appeal_text)
                
                if not appeal_id:
                    await message.reply_text("❌ Something went wrong saving your appeal. Try again later!")
                    return
                
                # Create appeal buttons with appeal ID
                keyboard = InlineKeyboardMarkup([
                    [
                        InlineKeyboardButton("✅ Unban", callback_data=f"appeal_accept_{appeal_id}"),
                        InlineKeyboardButton("❌ Reject", callback_data=f"appeal_reject_{appeal_id}")
                    ]
                ])
                
                await context.bot.send_message(
                    group_id,
                    f"<b>🙏 NEW BAN APPEAL!</b>\n\n"
                    f"<b>User:</b> {user.mention_html()} [<code>{user.id}</code>]\n"
                    f"<b>Appeal:</b>\n<i>{appeal_text}</i>\n\n"
                    f"Admins, what's the move? 🤔",
                    reply_markup=keyboard,
                    parse_mode=ParseMode.HTML
                )
                
                await message.reply_text(
                    "✅ Ayy your appeal has been sent to the admins!\n"
                    "Keep it cool while they review it fr fr! 🙏\n\n"
                    "<i>We'll let you know their decision!</i>",
                    parse_mode=ParseMode.HTML
                )
                
            except TelegramError as e:
                await message.reply_text(
                    f"❌ Couldn't send your appeal: {str(e)}\n"
                    "Make sure the group ID/link is correct!",
                    parse_mode=ParseMode.HTML
                )
                
        except Exception as e:
            await message.reply_text(f"❌ Error: {str(e)}")
            
    async def handle_appeal_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle admin decisions on appeals"""
        query = update.callback_query
        
        try:
            # Parse callback data
            action, appeal_id = query.data.split("_")[1:]
            appeal_id = int(appeal_id)
            
            # Get appeal details
            appeal = await self.db.get_appeal(appeal_id)
            if not appeal:
                await query.answer("❌ Appeal not found!", show_alert=True)
                return
            
            if appeal['status'] != 'pending':
                await query.answer("❌ This appeal has already been handled!", show_alert=True)
                return
            
            # Check if user is admin in the target chat
            chat_id = appeal['chat_id']
            user_id = query.from_user.id
            
            try:
                chat_member = await context.bot.get_chat_member(chat_id, user_id)
                if chat_member.status not in ["administrator", "creator"]:
                    await query.answer("❌ Only admins can handle appeals!", show_alert=True)
                    return
            except Exception as e:
                await query.answer(f"❌ Error checking admin status: {str(e)}", show_alert=True)
                return
            
            if action == "accept":
                try:
                    # Unban user
                    await context.bot.unban_chat_member(appeal['chat_id'], appeal['user_id'])
                    
                    # Generate invite link
                    invite_link = await context.bot.create_chat_invite_link(
                        appeal['chat_id'],
                        expire_date=datetime.now() + timedelta(days=1),
                        member_limit=1
                    )
                    
                    # Update appeal status
                    await self.db.update_appeal_status(
                        appeal_id,
                        'accepted',
                        query.from_user.id,
                        'unban',
                        'Appeal accepted'
                    )
                    
                    # Notify user
                    await context.bot.send_message(
                        appeal['user_id'],
                        f"<b>AYYYY GOOD NEWS! 🎉</b>\n\n"
                        f"Your appeal was accepted! Welcome back to the squad! 💯\n\n"
                        f"<b>Here's your VIP ticket back in:</b>\n"
                        f"<a href='{invite_link.invite_link}'>{invite_link.invite_link}</a>\n\n"
                        f"<i>⚡ Quick heads up:\n"
                        f"• Link expires in 24h\n"
                        f"• One-time use only\n"
                        f"• Keep it real this time!</i>",
                        parse_mode=ParseMode.HTML
                    )
                    
                    await query.edit_message_text(
                        f"✅ Appeal accepted by {query.from_user.mention_html()}\n"
                        f"User has been unbanned and notified with an invite link.",
                        parse_mode=ParseMode.HTML
                    )
                    
                except Exception as e:
                    await query.answer(f"❌ Error: {str(e)}", show_alert=True)
                
            elif action == "reject":
                try:
                    # Update appeal status
                    await self.db.update_appeal_status(
                        appeal_id,
                        'rejected',
                        query.from_user.id,
                        'reject',
                        'Appeal rejected'
                    )
                    
                    # Notify user
                    await context.bot.send_message(
                        appeal['user_id'],
                        f"<b>Appeal Update ❌</b>\n\n"
                        f"Sorry fam, your appeal was rejected.\n"
                        f"Try again later and show them you've changed fr fr! 🙏",
                        parse_mode=ParseMode.HTML
                    )
                    
                    await query.edit_message_text(
                        f"❌ Appeal rejected by {query.from_user.mention_html()}",
                        parse_mode=ParseMode.HTML
                    )
                    
                except Exception as e:
                    await query.answer(f"❌ Error: {str(e)}", show_alert=True)
                
        except Exception as e:
            await query.answer(f"❌ Error: {str(e)}", show_alert=True)
