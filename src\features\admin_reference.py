"""
👑 Admin Command Reference
---------------------------
Complete Rose-bot style admin command guide.
"""

import asyncio
import html
import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.constants import ParseMode
from telegram.ext import ContextTypes, CommandHandler, CallbackQueryHandler

logger = logging.getLogger(__name__)

class AdminCommandReference:
    def __init__(self, db=None):
        self.db = db

    async def _delete_message_later(self, context: ContextTypes.DEFAULT_TYPE, chat_id: int, message_id: int, delay: int = 30) -> None:
        """Delete a message after a specified delay in seconds."""
        await asyncio.sleep(delay)
        try:
            await context.bot.delete_message(chat_id=chat_id, message_id=message_id)
        except Exception as e:
            logger.debug(f"Failed to delete message: {e}")

    async def _send_group_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE, text: str, parse_mode: str = ParseMode.HTML, reply_markup = None) -> None:
        """Helper function to send messages in groups with auto-delete"""
        if not update.effective_message:
            return

        is_private = update.effective_chat.type == "private"
        
        # Send message
        message = await update.effective_message.reply_text(
            text,
            parse_mode=parse_mode,
            reply_markup=reply_markup,
            disable_web_page_preview=True
        )
        
        # Auto delete in groups after 30 seconds
        if not is_private:
            asyncio.create_task(self._delete_message_later(
                context, 
                message.chat_id, 
                message.message_id,
                delay=30
            ))
        
        # Delete the command message in groups
        if not is_private and update.effective_message.text and update.effective_message.text.startswith('/'):
            asyncio.create_task(self._delete_message_later(
                context, 
                update.effective_message.chat_id,
                update.effective_message.message_id,
                delay=30
            ))
        
        return message

    async def admin_help(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Complete Rose-bot style admin command reference"""
        if not update.effective_message:
            return

        keyboard = [
            [
                InlineKeyboardButton("👥 User Management", callback_data="admin_ref_users"),
                InlineKeyboardButton("⚠️ Warning System", callback_data="admin_ref_warnings")
            ],
            [
                InlineKeyboardButton("💬 Message Control", callback_data="admin_ref_messages"),
                InlineKeyboardButton("🏆 Promotions", callback_data="admin_ref_promote")
            ],
            [
                InlineKeyboardButton("🛡️ Protection", callback_data="admin_ref_protection"),
                InlineKeyboardButton("⚙️ Settings", callback_data="admin_ref_settings")
            ],
            [
                InlineKeyboardButton("📊 Analytics", callback_data="admin_ref_analytics"),
                InlineKeyboardButton("🔧 Advanced", callback_data="admin_ref_advanced")
            ],
            [InlineKeyboardButton("◀️ Back to Help", callback_data="start_help")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        admin_text = (
            "╭─「 <b>👑 Complete Admin Command Guide</b> 」\n"
            "│\n"
            "│ 🎯 <b>Professional group management toolkit</b>\n"
            "│ Master every aspect of group administration\n"
            "│\n"
            "├─「 <b>🚀 Command Categories</b> 」\n"
            "│\n"
            "│ 👥 <b>User Management</b>\n"
            "│    └ Ban, mute, kick, restrict users\n"
            "│\n"
            "│ ⚠️ <b>Warning System</b>\n"
            "│    └ Issue, remove, track warnings\n"
            "│\n"
            "│ 💬 <b>Message Control</b>\n"
            "│    └ Delete, purge, pin messages\n"
            "│\n"
            "│ 🏆 <b>Promotions</b>\n"
            "│    └ Promote, demote, manage admins\n"
            "│\n"
            "│ 🛡️ <b>Protection</b>\n"
            "│    └ Anti-spam, NSFW, raid protection\n"
            "│\n"
            "│ ⚙️ <b>Settings</b>\n"
            "│    └ Configure group features\n"
            "│\n"
            "├─「 <b>💡 Quick Tips</b> 」\n"
            "│ • Most commands work by reply or mention\n"
            "│ • Use time formats: 5m, 2h, 1d, 1w\n"
            "│ • All actions are logged automatically\n"
            "│ • Check permissions before using commands\n"
            "│\n"
            "├─「 <b>📊 Current Status</b> 」\n"
            f"│ 💭 Chat: <code>{html.escape(update.effective_chat.title) if update.effective_chat.title else 'Private Chat'}</code>\n"
            f"│ 🆔 ID: <code>{update.effective_chat.id}</code>\n"
            "│ 👑 Admin Panel: <b>Active</b>\n"
            "│ 🛡️ Protection: <b>Enabled</b>\n"
            "│\n"
            "╰─「 <i>Select a category to explore commands!</i> 」"
        )

        if update.effective_chat.type != "private":
            admin_text += f"\n\n<i>⏰ Auto-deletes in 30 seconds</i>"

        await self._send_group_message(
            update,
            context,
            admin_text,
            ParseMode.HTML,
            reply_markup
        )

    async def handle_admin_ref_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle admin reference callbacks"""
        query = update.callback_query
        await query.answer()
        
        action = query.data.replace("admin_ref_", "")
        
        if action == "users":
            await self._show_user_management_ref(query, context)
        elif action == "warnings":
            await self._show_warning_system_ref(query, context)
        elif action == "messages":
            await self._show_message_control_ref(query, context)
        elif action == "promote":
            await self._show_promotion_ref(query, context)
        elif action == "protection":
            await self._show_protection_ref(query, context)
        elif action == "settings":
            await self._show_settings_ref(query, context)
        elif action == "analytics":
            await self._show_analytics_ref(query, context)
        elif action == "advanced":
            await self._show_advanced_ref(query, context)

    async def _show_user_management_ref(self, query, context):
        """Show comprehensive user management commands"""
        keyboard = [
            [
                InlineKeyboardButton("⚔️ Bans", callback_data="admin_ref_bans"),
                InlineKeyboardButton("🤫 Mutes", callback_data="admin_ref_mutes")
            ],
            [
                InlineKeyboardButton("👢 Kicks", callback_data="admin_ref_kicks"),
                InlineKeyboardButton("🔓 Unbans", callback_data="admin_ref_unbans")
            ],
            [InlineKeyboardButton("◀ Back to Admin", callback_data="admin_ref_back")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        text = (
            "╭─「 <b>👥 User Management Commands</b> 」\n"
            "│\n"
            "├─「 <b>⚔️ Ban Commands</b> 」\n"
            "│ • <code>/ban [user] [reason]</code> - Permanent ban\n"
            "│ • <code>/tban [user] [time] [reason]</code> - Temporary ban\n"
            "│ • <code>/sban [user] [reason]</code> - Silent ban (no notification)\n"
            "│ • <code>/dban [reply] [reason]</code> - Delete message & ban\n"
            "│ • <code>/fban [user] [reason]</code> - Federation ban\n"
            "│\n"
            "├─「 <b>🤫 Mute Commands</b> 」\n"
            "│ • <code>/mute [user] [time] [reason]</code> - Mute user\n"
            "│ • <code>/tmute [user] [time]</code> - Temporary mute\n"
            "│ • <code>/smute [user] [reason]</code> - Silent mute\n"
            "│ • <code>/dmute [reply]</code> - Delete message & mute\n"
            "│ • <code>/voicemute [user]</code> - Mute voice messages\n"
            "│ • <code>/mediamute [user]</code> - Mute media sharing\n"
            "│\n"
            "├─「 <b>👢 Kick Commands</b> 」\n"
            "│ • <code>/kick [user] [reason]</code> - Kick user (can rejoin)\n"
            "│ • <code>/dkick [reply]</code> - Delete message & kick\n"
            "│ • <code>/skick [user]</code> - Silent kick\n"
            "│\n"
            "├─「 <b>🔓 Unban/Unmute Commands</b> 」\n"
            "│ • <code>/unban [user]</code> - Remove ban\n"
            "│ • <code>/unmute [user]</code> - Remove mute\n"
            "│ • <code>/unvoicemute [user]</code> - Remove voice mute\n"
            "│ • <code>/unmediamute [user]</code> - Remove media mute\n"
            "│\n"
            "├─「 <b>⏰ Time Formats</b> 」\n"
            "│ • <code>5m</code> = 5 minutes\n"
            "│ • <code>2h</code> = 2 hours\n"
            "│ • <code>1d</code> = 1 day\n"
            "│ • <code>1w</code> = 1 week\n"
            "│ • <code>30s</code> = 30 seconds\n"
            "│\n"
            "├─「 <b>📝 Usage Examples</b> 」\n"
            "│ • <code>/ban @user spam</code>\n"
            "│ • <code>/tban @user 1d inappropriate behavior</code>\n"
            "│ • <code>/mute @user 30m flooding</code>\n"
            "│ • Reply to message + <code>/dban spam</code>\n"
            "│\n"
            "╰─「 <i>Discipline with precision!</i> 」"
        )
        
        await query.edit_message_text(text, parse_mode=ParseMode.HTML, reply_markup=reply_markup)

    async def _show_warning_system_ref(self, query, context):
        """Show warning system commands"""
        keyboard = [
            [InlineKeyboardButton("◀ Back to Admin", callback_data="admin_ref_back")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        text = (
            "╭─「 <b>⚠️ Warning System Commands</b> 」\n"
            "│\n"
            "├─「 <b>📢 Issue Warnings</b> 」\n"
            "│ • <code>/warn [user] [reason]</code> - Issue warning\n"
            "│ • <code>/dwarn [reply] [reason]</code> - Delete msg & warn\n"
            "│ • <code>/swarn [user] [reason]</code> - Silent warning\n"
            "│ • <code>/strongwarn [user] [reason]</code> - Strong warning\n"
            "│\n"
            "├─「 <b>🗑️ Remove Warnings</b> 」\n"
            "│ • <code>/unwarn [user]</code> - Remove latest warning\n"
            "│ • <code>/resetwarns [user]</code> - Clear all warnings\n"
            "│ • <code>/delwarn [user] [number]</code> - Delete specific warning\n"
            "│\n"
            "├─「 <b>📋 Check Warnings</b> 」\n"
            "│ • <code>/warns [user]</code> - Check user warnings\n"
            "│ • <code>/warnings [user]</code> - Detailed warning list\n"
            "│ • <code>/warnlist</code> - All warned users\n"
            "│ • <code>/warnstats</code> - Warning statistics\n"
            "│\n"
            "├─「 <b>⚙️ Warning Settings</b> 」\n"
            "│ • <code>/setwarnlimit [number]</code> - Set warn limit\n"
            "│ • <code>/setwarnmode [ban/mute/kick]</code> - Action on limit\n"
            "│ • <code>/warnmode</code> - Check current settings\n"
            "│ • <code>/resetwarnmode</code> - Reset to defaults\n"
            "│\n"
            "├─「 <b>🔄 Bulk Operations</b> 」\n"
            "│ • <code>/clearallwarns</code> - Clear all warnings (admin only)\n"
            "│ • <code>/exportwarns</code> - Export warning data\n"
            "│ • <code>/warnbackup</code> - Create warning backup\n"
            "│\n"
            "├─「 <b>📊 Default Settings</b> 」\n"
            "│ • Warn Limit: <code>3 warnings</code>\n"
            "│ • Action: <code>Ban user</code>\n"
            "│ • Auto-expire: <code>30 days</code>\n"
            "│ • Notifications: <code>Enabled</code>\n"
            "│\n"
            "╰─「 <i>Progressive discipline system!</i> 」"
        )
        
        await query.edit_message_text(text, parse_mode=ParseMode.HTML, reply_markup=reply_markup)

    async def _show_message_control_ref(self, query, context):
        """Show message control commands"""
        keyboard = [
            [InlineKeyboardButton("◀ Back to Admin", callback_data="admin_ref_back")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        text = (
            "╭─「 <b>💬 Message Control Commands</b> 」\n"
            "│\n"
            "├─「 <b>🗑️ Delete Messages</b> 」\n"
            "│ • <code>/del [reply]</code> - Delete single message\n"
            "│ • <code>/purge [reply]</code> - Delete from reply to latest\n"
            "│ • <code>/purge [reply] [number]</code> - Delete specific count\n"
            "│ • <code>/spurge [reply]</code> - Silent purge (no notification)\n"
            "│ • <code>/delpurge [reply]</code> - Purge + delete command\n"
            "│\n"
            "├─「 <b>📌 Pin Messages</b> 」\n"
            "│ • <code>/pin [reply]</code> - Pin message (with notification)\n"
            "│ • <code>/pin [reply] -quiet</code> - Pin silently\n"
            "│ • <code>/pin [reply] -loud</code> - Pin with notification\n"
            "│ • <code>/unpin [reply]</code> - Unpin specific message\n"
            "│ • <code>/unpinall</code> - Unpin all messages\n"
            "│ • <code>/pinned</code> - Show pinned message\n"
            "│\n"
            "├─「 <b>📝 Edit Messages</b> 」\n"
            "│ • <code>/edit [reply] [new text]</code> - Edit bot message\n"
            "│ • <code>/replace [reply] [old] [new]</code> - Replace text\n"
            "│ • <code>/append [reply] [text]</code> - Add to message\n"
            "│\n"
            "├─「 <b>📋 Message Info</b> 」\n"
            "│ • <code>/msginfo [reply]</code> - Message details\n"
            "│ • <code>/msgsource [reply]</code> - Message source\n"
            "│ • <code>/msgid [reply]</code> - Get message ID\n"
            "│ • <code>/msglink [reply]</code> - Get message link\n"
            "│\n"
            "├─「 <b>🎯 Advanced Purge</b> 」\n"
            "│ • <code>/purgeuser [user]</code> - Delete user's messages\n"
            "│ • <code>/purgefrom [user] [count]</code> - Purge from user\n"
            "│ • <code>/purgefilter [word]</code> - Purge containing word\n"
            "│ • <code>/purgemedia</code> - Purge media messages\n"
            "│ • <code>/purgestickers</code> - Purge stickers only\n"
            "│\n"
            "├─「 <b>⏰ Scheduled Actions</b> 」\n"
            "│ • <code>/schedule [time] /command</code> - Schedule command\n"
            "│ • <code>/autopin [time]</code> - Auto-pin after time\n"
            "│ • <code>/autodel [reply] [time]</code> - Auto-delete\n"
            "│\n"
            "╰─「 <i>Complete message control!</i> 」"
        )
        
        await query.edit_message_text(text, parse_mode=ParseMode.HTML, reply_markup=reply_markup)

    async def _show_protection_ref(self, query, context):
        """Show protection commands"""
        keyboard = [
            [InlineKeyboardButton("◀ Back to Admin", callback_data="admin_ref_back")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        text = (
            "╭─「 <b>🛡️ Protection Commands</b> 」\n"
            "│\n"
            "├─「 <b>🚫 Anti-Spam Protection</b> 」\n"
            "│ • <code>/antiflood [limit] [action]</code> - Set flood protection\n"
            "│ • <code>/setflood [messages] [time]</code> - Advanced flood setting\n"
            "│ • <code>/floodmode [ban/mute/kick]</code> - Flood punishment\n"
            "│ • <code>/antiflood off</code> - Disable flood protection\n"
            "│\n"
            "├─「 <b>🔗 Link Protection</b> 」\n"
            "│ • <code>/antilink on/off</code> - Block all links\n"
            "│ • <code>/antitelegram on/off</code> - Block Telegram links\n"
            "│ • <code>/antiforward on/off</code> - Block forwarded messages\n"
            "│ • <code>/linkwhitelist [domain]</code> - Allow specific domains\n"
            "│\n"
            "├─「 <b>🤖 Anti-Raid Protection</b> 」\n"
            "│ • <code>/raidmode on/off</code> - Toggle raid mode\n"
            "│ • <code>/raidtime [minutes]</code> - Set raid detection time\n"
            "│ • <code>/raidlimit [users]</code> - Users joining threshold\n"
            "│ • <code>/raidaction [ban/mute]</code> - Action on raid\n"
            "│\n"
            "├─「 <b>🔞 NSFW Protection</b> 」\n"
            "│ • <code>/nsfw on/off</code> - Toggle NSFW detection\n"
            "│ • <code>/nsfwmode [delete/warn/ban]</code> - NSFW action\n"
            "│ • <code>/nsfwstrict [1-3]</code> - Detection sensitivity\n"
            "│ • <code>/nsfwwhitelist [user]</code> - Exempt user from NSFW\n"
            "│\n"
            "├─「 <b>👥 New User Protection</b> 」\n"
            "│ • <code>/captcha on/off</code> - New user verification\n"
            "│ • <code>/captchatime [minutes]</code> - Time to solve\n"
            "│ • <code>/welcomemute on/off</code> - Mute new users\n"
            "│ • <code>/restricttime [minutes]</code> - Restriction duration\n"
            "│\n"
            "├─「 <b>📱 Media Protection</b> 」\n"
            "│ • <code>/antimedia on/off</code> - Block all media\n"
            "│ • <code>/antisticker on/off</code> - Block stickers\n"
            "│ • <code>/antivideo on/off</code> - Block videos\n"
            "│ • <code>/antiaudio on/off</code> - Block audio files\n"
            "│ • <code>/antidocument on/off</code> - Block documents\n"
            "│\n"
            "├─「 <b>🌐 Language Protection</b> 」\n"
            "│ • <code>/antiarabic on/off</code> - Block Arabic text\n"
            "│ • <code>/anticyrillic on/off</code> - Block Cyrillic text\n"
            "│ • <code>/antichinese on/off</code> - Block Chinese text\n"
            "│ • <code>/langwhitelist [user]</code> - Exempt user\n"
            "│\n"
            "╰─「 <i>Maximum security protection!</i> 」"
        )
        
        await query.edit_message_text(text, parse_mode=ParseMode.HTML, reply_markup=reply_markup)

    def register_handlers(self, application):
        """Register admin reference handlers"""
        application.add_handler(CommandHandler("admin", self.admin_help))
        application.add_handler(CommandHandler("adminhelp", self.admin_help))
        application.add_handler(CommandHandler("adminguide", self.admin_help))
        
        # Add callback handlers
        application.add_handler(CallbackQueryHandler(self.handle_admin_ref_callback, pattern=r"^admin_ref_"))