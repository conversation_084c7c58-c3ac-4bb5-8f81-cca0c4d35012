"""
🤖 Auto Responder Module
-----------------------
<PERSON>les automatic responses to trigger words/phrases in group chats.
"""

from telegram import Update
from telegram.ext import ContextTypes, CommandHandler, MessageHandler, filters, Application
from telegram.constants import ParseMode
import logging
import re
from datetime import datetime

from src.utils.decorators import admin_only, group_only
from src.database.db import Database

logger = logging.getLogger(__name__)

class AutoResponder:
    def __init__(self, db: Database):
        self.db = db
        logger.info("Auto-responder initialized with MongoDB")

    async def show_autoresponse_help(self, update: Update) -> None:
        """Show help message for auto-response commands with attitude."""
        help_text = (
            "🤖 <b>Auto-Response Commands</b>\n\n"
            "Listen up, here's how to not mess this up:\n\n"
            
            "1️⃣ <code>/addresponse trigger | response</code>\n"
            "   • Adds a new trigger and response\n"
            "   • Use that | symbol or I'll ignore you\n"
            "   • Example: <code>/addresponse hello | Get lost!</code>\n\n"
            
            "2️⃣ <code>/delresponse trigger</code>\n"
            "   • Deletes a trigger because you messed up\n"
            "   • Example: <code>/delresponse hello</code>\n\n"
            
            "3️⃣ <code>/responses</code>\n"
            "   • Lists all your triggers, if you can't remember them 🙄\n\n"
            
            "💡 <b>Pro tip:</b> Try not to break it. But if you do, it's definitely a user error. 😏"
        )
        
        await update.effective_message.reply_text(
            help_text,
            parse_mode=ParseMode.HTML
        )

    async def arhelp(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Public command to show auto-responder help."""
        await self.show_autoresponse_help(update)

    @admin_only()
    @group_only()
    async def add_response(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Add a new trigger and response."""
        if not update.effective_message:
            return

        # If no arguments provided, show help
        if not context.args:
            await self.show_autoresponse_help(update)
            return

        message_text = update.effective_message.text.strip()
        
        # Remove command from the message
        if ' ' in message_text:
            full_text = message_text.split(' ', 1)[1]
        else:
            await self.show_autoresponse_help(update)
            return

        if '|' in full_text:
            parts = [p.strip() for p in full_text.split('|', 1)]
        else:
            parts = full_text.split(None, 1)
        if len(parts) != 2:
            await update.effective_message.reply_text(
                "🤦‍♂️ Usage: <code>/addresponse trigger | response</code>",
                parse_mode=ParseMode.HTML,
            )
            return

        chat_id = update.effective_chat.id
        user_id = update.effective_user.id
        
        trigger = parts[0].strip().lower()
        response = parts[1].strip()
        
        if not trigger or not response:
            await update.effective_message.reply_text(
                "🤨 Empty trigger or response? Really?\n"
                "Try using your brain and actual words next time.\n"
                "Example: <code>/addresponse hello | Get lost!</code>",
                parse_mode=ParseMode.HTML
            )
            return
        
        try:
            success = await self.db.add_auto_response(chat_id, trigger, response, user_id)

            if success:
                await update.effective_message.reply_text(
                    f"✅ Fine, I added your auto-response:\n"
                    f"Trigger: <code>{trigger}</code>\n"
                    f"Response: <code>{response}</code>\n\n"
                    f"Happy now? 😏",
                    parse_mode=ParseMode.HTML
                )
            else:
                await update.effective_message.reply_text(
                    "❌ Database connection failed. Not my fault this time. 🙄"
                )

        except Exception as e:
            logger.error(f"Error adding auto-response: {e}")
            await update.effective_message.reply_text(
                "❌ Something broke. Probably your fault. Try again. 🙄"
            )

    @admin_only()
    @group_only()
    async def list_responses(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """List all triggers and responses for the current group."""
        if not update.effective_message:
            return
        
        chat_id = update.effective_chat.id
        
        try:
            responses = await self.db.get_all_auto_responses(chat_id)

            if not responses:
                await update.effective_message.reply_text(
                    "❌ No auto-responses found.\n"
                    "Maybe try adding some? Just a crazy thought! 🙄"
                )
                return

            text = "<b>📝 Auto-Responses:</b>\n\n"
            for idx, response in enumerate(responses, 1):
                text += f"{idx}. Trigger: <code>{response['trigger']}</code>\n"
                text += f"   Response: <code>{response['response']}</code>\n\n"

            await update.effective_message.reply_text(
                text,
                parse_mode=ParseMode.HTML
            )
            
        except Exception as e:
            logger.error(f"Error listing auto-responses: {e}")
            await update.effective_message.reply_text(
                "❌ Failed to list auto-responses. The database is probably as confused as you are. 🤷‍♂️"
            )

    @admin_only()
    @group_only()
    async def remove_response(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Remove a trigger and its response."""
        if not update.effective_message:
            return
        
        # If no arguments provided, show help
        if not context.args:
            await update.effective_message.reply_text(
                "🤦‍♂️ You're supposed to tell me what to delete.\n"
                "Like this, Einstein:\n"
                "<code>/delresponse trigger</code>",
                parse_mode=ParseMode.HTML
            )
            return
        
        chat_id = update.effective_chat.id
        # Join all arguments to handle multi-word triggers
        trigger = ' '.join(context.args).lower()
        
        try:
            success = await self.db.remove_auto_response(chat_id, trigger)

            if success:
                await update.effective_message.reply_text(
                    f"✅ Deleted the auto-response for <code>{trigger}</code>.\n"
                    f"Hope you meant to do that! 😈",
                    parse_mode=ParseMode.HTML
                )
            else:
                await update.effective_message.reply_text(
                    f"❌ Can't find any auto-response for <code>{trigger}</code>.\n"
                    f"Maybe check your spelling? Just saying... 🙄",
                    parse_mode=ParseMode.HTML
                )
                
        except Exception as e:
            logger.error(f"Error removing auto-response: {e}")
            await update.effective_message.reply_text(
                "❌ Task failed successfully. Try again, if you dare. 😏"
            )

    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Check messages for triggers and respond accordingly."""
        if not update.effective_message or not update.effective_message.text:
            return
            
        # Ignore bot's own messages
        if update.effective_message.from_user.id == context.bot.id:
            return
            
        chat_id = update.effective_chat.id
        message_text = update.effective_message.text.lower()
        
        try:
            responses = await self.db.get_all_auto_responses(chat_id)

            if not responses:
                return

            # Pre-process message text to handle obfuscation
            processed_text = message_text
            # Replace common character substitutions
            char_map = {
                '@': 'a', '4': 'a', '!': 'i', '1': 'i', '0': 'o',
                '$': 's', '5': 's', '3': 'e', '7': 't', '+': 't',
                '|': 'l', '2': 'z', '9': 'g', '6': 'g', '8': 'b'
            }
            for char, replacement in char_map.items():
                processed_text = processed_text.replace(char, replacement)

            # Remove special characters and extra spaces
            processed_text = re.sub(r'[^a-z0-9\s]', '', processed_text)
            processed_text = ' '.join(processed_text.split())

            for response_data in responses:
                trigger = response_data['trigger']
                response = response_data['response']
                # Clean trigger text
                clean_trigger = re.sub(r'[^a-z0-9\s]', '', trigger.lower())
                clean_trigger = ' '.join(clean_trigger.split())
                
                # Create pattern variations
                variations = [
                    # Basic word match
                    r'\b' + re.escape(clean_trigger) + r'\b',
                    
                    # Allow spaces between characters
                    r'\b' + r'\s*'.join(re.escape(c) for c in clean_trigger) + r'\b',
                    
                    # Allow repeated characters
                    ''.join(f'({c}+)' for c in clean_trigger),
                    
                    # Fuzzy match with optional characters
                    r'\b' + r'[a-z0-9\s]*?'.join(re.escape(c) for c in clean_trigger) + r'\b'
                ]
                
                # Check original text
                if any(re.search(pattern, message_text, re.IGNORECASE) for pattern in variations):
                    await update.effective_message.reply_text(
                        response,
                        parse_mode=ParseMode.HTML
                    )
                    break
                
                # Check processed text
                if any(re.search(pattern, processed_text, re.IGNORECASE) for pattern in variations):
                    await update.effective_message.reply_text(
                        response,
                        parse_mode=ParseMode.HTML
                    )
                    break
                
                # Direct substring check for heavily obfuscated text
                if clean_trigger in processed_text:
                    await update.effective_message.reply_text(
                        response,
                        parse_mode=ParseMode.HTML
                    )
                    break
                
        except Exception as e:
            logger.error(f"Error handling auto-response: {e}")
            logger.error(f"Message text: {message_text}")
            logger.error(f"Processed text: {processed_text}")

    def register_handlers(self, application: Application) -> None:
        """Register all handlers for auto-responder functionality."""
        # Command handlers
        application.add_handler(CommandHandler("addresponse", self.add_response))
        application.add_handler(CommandHandler("delresponse", self.remove_response))
        application.add_handler(CommandHandler("responses", self.list_responses))
        application.add_handler(CommandHandler("arhelp", self.arhelp))
        
        # Message handler for auto-responses (low priority to not interfere with commands)
        application.add_handler(
            MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message),
            group=5  # Lower priority than most command handlers
        )
