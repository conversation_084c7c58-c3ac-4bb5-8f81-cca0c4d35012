"""
Advanced utility services for various tasks.
"""
import aiohttp
import json
from typing import Dict, List, Any
import random
import string
from datetime import datetime
import logging
import os
from dotenv import load_dotenv
from src.api_services.pollinations_ai import PollinationsAI
# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

class UtilityServices:
    def __init__(self):
        self.session = None

    async def ensure_session(self):
        if self.session is None:
            self.session = aiohttp.ClientSession()

    async def close_session(self):
        if self.session:
            await self.session.close()
            self.session = None

    async def get_crypto_price(self, symbol: str) -> Dict[str, Any]:
        """Get cryptocurrency price information"""
        await self.ensure_session()
        url = f"https://api.coingecko.com/api/v3/simple/price"
        params = {
            "ids": symbol.lower(),
            "vs_currencies": "usd,eur",
            "include_24hr_change": "true"
        }
        async with self.session.get(url, params=params) as response:
            data = await response.json()
            return data[symbol.lower()]

    async def shorten_url(self, url: str) -> str:
        """Shorten a URL using TinyURL"""
        import pyshorteners
        shortener = pyshorteners.Shortener()
        try:
            return shortener.tinyurl.short(url)
        except Exception as e:
            logger.error(f"Error shortening URL: {e}")
            return url  # Return original URL if shortening fails

    async def get_news(self, query: str = "LATEST AI NEWS") -> List[Dict[str, str]]:
        """Get news based on the query using WEBS"""
        from webscout import WEBS
        webs = WEBS()
        news_items = webs.news(query, max_results=7)  # Get news from last day
        result = []
        for item in news_items:
            short_url = await self.shorten_url(item["url"])
            result.append({
                "title": item["title"],
                "url": short_url,
                "source": item["source"],
                "date": item["date"]
            })
        return result

    async def get_movie_info(self, title: str) -> Dict[str, Any]:
        """Get movie information by scraping IMDb"""
        from bs4 import BeautifulSoup
        import requests
        import re
        import json

        # Search IMDb
        search_url = f"https://www.imdb.com/find?q={title}&s=tt&ttype=ft"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept-Language': 'en-US,en;q=0.9'
        }
        
        try:
            search_response = requests.get(search_url, headers=headers)
            search_soup = BeautifulSoup(search_response.text, 'html.parser')
            
            # Find first movie result
            movie_link = search_soup.find('a', href=re.compile(r'/title/tt\d+/'))
            if not movie_link:
                return {"Response": "False", "Error": "Movie not found!"}
                
            movie_id = re.search(r'tt\d+', movie_link['href']).group()
            movie_url = f"https://www.imdb.com/title/{movie_id}/"
            movie_response = requests.get(movie_url, headers=headers)
            movie_soup = BeautifulSoup(movie_response.text, 'html.parser')

            # Try to extract JSON data from script tag
            script_data = None
            for script in movie_soup.find_all('script', type='application/ld+json'):
                try:
                    data = json.loads(script.string)
                    if data.get('@type') == 'Movie':
                        script_data = data
                        break
                except:
                    continue
            
            # Extract movie information
            title = script_data.get('name', 'N/A') if script_data else movie_soup.find('h1').text.strip()
            
            # Get year
            year_elem = movie_soup.find('span', class_=re.compile(r'release-year|sc-8c396aa2-2'))
            year = year_elem.text.strip() if year_elem else "N/A"
            
            # Get rating
            rating = script_data.get('aggregateRating', {}).get('ratingValue', 'N/A') if script_data else 'N/A'
            
            # Get genres
            genres = script_data.get('genre', []) if script_data else []
            if not genres:
                genre_elems = movie_soup.find_all('span', class_='ipc-chip__text')
                genres = [g.text for g in genre_elems[:3]] if genre_elems else ["N/A"]
            
            # Get runtime
            runtime = script_data.get('duration', 'N/A').replace('PT', '').replace('H', 'h ').replace('M', 'm') if script_data else 'N/A'
            if runtime == 'N/A':
                runtime_elem = movie_soup.find('div', attrs={'data-testid': 'title-techspecs-section'})
                runtime = runtime_elem.text.split('Runtime')[1].split('min')[0].strip() + " min" if runtime_elem else "N/A"
            
            # Get director
            director = "N/A"
            if script_data and 'director' in script_data:
                if isinstance(script_data['director'], list):
                    director = ', '.join(d.get('name', '') for d in script_data['director'])
                else:
                    director = script_data['director'].get('name', 'N/A')
            
            if director == "N/A":
                director_elem = movie_soup.find('a', attrs={'data-testid': 'title-pc-principal-credit'})
                director = director_elem.text.strip() if director_elem else "N/A"
            
            # Get cast
            cast = []
            if script_data and 'actor' in script_data:
                cast = [actor.get('name', '') for actor in script_data['actor'][:5]]
            
            if not cast:
                cast_elems = movie_soup.find_all('a', attrs={'data-testid': 'title-cast-item__actor'})
                cast = [a.text for a in cast_elems[:5]] if cast_elems else ["N/A"]
            
            # Get plot
            plot = script_data.get('description', 'N/A') if script_data else 'N/A'
            if plot == 'N/A':
                plot_elem = movie_soup.find('span', attrs={'data-testid': 'plot-xl'})
                plot = plot_elem.text.strip() if plot_elem else "N/A"
            
            # Get poster
            poster = script_data.get('image', None) if script_data else None
            if not poster:
                poster_elem = movie_soup.find('img', class_='ipc-image')
                poster = poster_elem['src'] if poster_elem and 'src' in poster_elem.attrs else None
            
            # Get additional info
            awards_elem = movie_soup.find('div', attrs={'data-testid': 'awards'})
            awards = awards_elem.text.strip() if awards_elem else "N/A"
            
            # Get box office info
            box_office = "N/A"
            box_office_elem = movie_soup.find('div', attrs={'data-testid': 'title-boxoffice-section'})
            if box_office_elem:
                box_text = box_office_elem.text
                if 'Gross worldwide' in box_text:
                    box_office = box_text.split('Gross worldwide')[1].split('$')[1].strip()
            
            # Get ratings from different sources
            ratings_data = []
            ratings_section = movie_soup.find_all('div', class_=re.compile('ratings.*'))
            if ratings_section:
                for rating_div in ratings_section:
                    rating_text = rating_div.text
                    if 'IMDb' in rating_text:
                        ratings_data.append({"Source": "Internet Movie Database", "Value": f"{rating}/10"})
                    elif 'Metascore' in rating_text:
                        metascore = re.search(r'\d+', rating_text)
                        if metascore:
                            ratings_data.append({"Source": "Metacritic", "Value": f"{metascore.group()}/100"})

            # Get keywords/tags
            keywords = []
            keywords_section = movie_soup.find('div', attrs={'data-testid': 'storyline-plot-keywords'})
            if keywords_section:
                keyword_elems = keywords_section.find_all('a')
                keywords = [k.text.strip() for k in keyword_elems[:5]]

            return {
                "Response": "True",
                "Title": title,
                "Year": year,
                "imdbRating": rating,
                "Genre": ", ".join(genres) if isinstance(genres, list) else genres,
                "Runtime": runtime,
                "Director": director,
                "Actors": ", ".join(cast),
                "Plot": plot,
                "Poster": poster,
                "Awards": awards,
                "BoxOffice": f"${box_office}" if box_office != "N/A" else box_office,
                "Ratings": ratings_data,
                "Keywords": ", ".join(keywords) if keywords else "N/A",
                "IMDbID": movie_id
            }
            
        except Exception as e:
            logger.error(f"Error scraping movie info: {e}")
            return {"Response": "False", "Error": "Failed to fetch movie information"}

    async def get_recipe(self, query: str) -> Dict[str, Any]:
        """Get AI-generated recipe information"""


        recipe_prompt = f"""
<recipe_system>
    <identity>
        <role>Expert Chef and Recipe Generator</role>
        <version>2.0</version>
        <capabilities>
            <capability>International cuisine expertise</capability>
            <capability>Precise measurements and timing</capability>
            <capability>Dietary considerations</capability>
            <capability>Professional cooking techniques</capability>
        </capabilities>
    </identity>

    <recipe_request>
        <query>{query}</query>
        <format_requirements>
            <output_format>XML with embedded JSON</output_format>
            <structure>
                <element>recipe_name</element>
                <element>cuisine_type</element>
                <element>timing_info</element>
                <element>ingredients_list</element>
                <element>cooking_steps</element>
                <element>chef_tips</element>
                <element>nutritional_info</element>
            </structure>
        </format_requirements>
    </recipe_request>

    Please respond with a recipe in this format:
    <recipe_response>
        <recipe_data>
            {{
                "name": "Full recipe name",
                "cuisine_type": "Specific cuisine type",
                "prep_time": "XX minutes",
                "cook_time": "XX minutes",
                "difficulty": "Easy/Medium/Hard",
                "ingredients": [
                    "Detailed ingredient 1 with exact quantity",
                    "Detailed ingredient 2 with exact quantity"
                ],
                "instructions": [
                    "Detailed step 1",
                    "Detailed step 2"
                ],
                "tips": [
                    "Professional tip 1",
                    "Professional tip 2"
                ],
                "nutrition": {{
                    "calories": "XXX calories per serving",
                    "protein": "XX grams",
                    "carbs": "XX grams",
                    "fats": "XX grams"
                }}
            }}
        </recipe_data>
        <recipe_notes>
            <note>Additional cooking insights</note>
            <note>Storage recommendations</note>
            <note>Serving suggestions</note>
        </recipe_notes>
    </recipe_response>
</recipe_system>
"""
        try:
            messages = [
                {
                    "role": "system",
                    "content": "You are a professional chef with expertise in international cuisines. Always respond with properly formatted XML tags containing JSON data."
                },
                {"role": "user", "content": recipe_prompt}
            ]

            recipe_text = PollinationsAI.generate_text(
                model="openai-large",
                messages=messages,
                temperature=0.7
            )

            # Extract JSON between recipe_data tags
            if '<recipe_data>' in recipe_text and '</recipe_data>' in recipe_text:
                data_start = recipe_text.find('<recipe_data>') + len('<recipe_data>')
                data_end = recipe_text.find('</recipe_data>')
                json_text = recipe_text[data_start:data_end].strip()

                # Clean up the JSON text
                json_text = json_text.replace('\n', '').replace('    ', '')
                recipe_json = json.loads(json_text)

                # Extract notes
                notes = []
                if '<recipe_notes>' in recipe_text and '</recipe_notes>' in recipe_text:
                    notes_start = recipe_text.find('<recipe_notes>') + len('<recipe_notes>')
                    notes_end = recipe_text.find('</recipe_notes>')
                    notes_text = recipe_text[notes_start:notes_end].strip()

                    # Extract individual notes
                    import re
                    note_pattern = r'<note>(.*?)</note>'
                    notes = re.findall(note_pattern, notes_text)
                    notes = [note.strip() for note in notes]

                return {
                    "Response": "True",
                    "Recipe": recipe_json,
                    "Notes": notes if notes else None
                }
            else:
                return {
                    "Response": "False",
                    "Error": "Failed to generate recipe: recipe_data tags not found"
                }

        except Exception as e:
            logger.error(f"Error generating recipe: {e}")
            return {
                "Response": "False",
                "Error": f"Failed to generate recipe: {e}"
            }

    async def get_space_info(self) -> Dict[str, Any]:
        """Get information about astronauts currently in space"""
        await self.ensure_session()
        url = "http://api.open-notify.org/astros.json"
        async with self.session.get(url) as response:
            return await response.json()

    async def get_wikipedia(self, query: str) -> Dict[str, str]:
        """Search Wikipedia for a specific query"""
        await self.ensure_session()
        # First, search for the query
        search_url = "https://en.wikipedia.org/w/api.php"
        search_params = {
            "action": "query",
            "format": "json",
            "list": "search",
            "srsearch": query,
            "utf8": 1,
            "srlimit": 1
        }
        
        async with self.session.get(search_url, params=search_params) as response:
            search_data = await response.json()
            
            if not search_data["query"]["search"]:
                return None
                
            # Get the page title from search results
            page_title = search_data["query"]["search"][0]["title"]
            
            # Now get the full page summary
            summary_url = "https://en.wikipedia.org/api/rest_v1/page/summary/" + page_title
            async with self.session.get(summary_url) as response:
                data = await response.json()
                return {
                    "title": data["title"],
                    "extract": data["extract"],
                    "url": data["content_urls"]["desktop"]["page"]
                }

    async def get_random_wikipedia(self) -> Dict[str, str]:
        """Get a random Wikipedia article"""
        await self.ensure_session()
        url = "https://en.wikipedia.org/api/rest_v1/page/random/summary"
        async with self.session.get(url) as response:
            data = await response.json()
            return {
                "title": data["title"],
                "extract": data["extract"],
                "url": data["content_urls"]["desktop"]["page"]
            }

    async def get_word_info(self, word: str) -> Dict[str, Any]:
        """Get word definition and information"""
        await self.ensure_session()
        url = f"https://api.dictionaryapi.dev/api/v2/entries/en/{word}"
        async with self.session.get(url) as response:
            data = await response.json()
            return data[0] if isinstance(data, list) else None

    async def get_github_user(self, username: str) -> Dict[str, Any]:
        """Get GitHub user information"""
        await self.ensure_session()
        url = f"https://api.github.com/users/{username}"
        async with self.session.get(url) as response:
            return await response.json()

    async def get_random_riddle(self) -> Dict[str, str]:
        """Get a random riddle"""
        riddles = [
            {"question": "What has keys, but no locks; space, but no room; and you can enter, but not go in?",
             "answer": "A keyboard"},
            {"question": "What gets wetter and wetter the more it dries?",
             "answer": "A towel"},
            {"question": "What has a head and a tail that will never meet?",
             "answer": "A coin"},
            # Add more riddles here
        ]
        return random.choice(riddles)

    async def get_tech_news(self) -> List[Dict[str, str]]:
        """Get latest technology news"""
        await self.ensure_session()
        url = "https://hn.algolia.com/api/v1/search_by_date"
        params = {
            "tags": "story",
            "numericFilters": "points>100",
            "hitsPerPage": 5
        }
        async with self.session.get(url, params=params) as response:
            data = await response.json()
            return [{"title": hit["title"], "url": hit["url"]} for hit in data["hits"] if hit.get("url")]
