import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Button,
  TextField,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Avatar,
  CircularProgress,
  Alert
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import SearchIcon from '@mui/icons-material/Search';
import WarningIcon from '@mui/icons-material/Warning';
import BlockIcon from '@mui/icons-material/Block';
import { usersAPI } from '../services/api';

export default function Users() {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [search, setSearch] = useState('');
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await usersAPI.getAll();
      setUsers(response.data);
      setLoading(false);
    } catch (err) {
      setError('Failed to fetch users data');
      setLoading(false);
      console.error('Error fetching users:', err);
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearchChange = (event) => {
    setSearch(event.target.value);
    setPage(0);
  };

  const handleEditUser = (user) => {
    setSelectedUser(user);
    setOpenDialog(true);
  };

  const handleDeleteUser = (userId) => {
    // In a real implementation, this would make an API call to delete the user
    console.log('Delete user:', userId);
    // For now, we'll just show an alert
    alert('User deletion would be implemented in a real application');
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedUser(null);
  };

  const handleWarnUser = (userId) => {
    // In a real implementation, this would make an API call to warn the user
    console.log('Warn user:', userId);
    // For now, we'll just show an alert
    alert('User warning would be implemented in a real application');
  };

  const handleBanUser = (userId) => {
    // In a real implementation, this would make an API call to ban the user
    console.log('Ban user:', userId);
    // For now, we'll just show an alert
    alert('User banning would be implemented in a real application');
  };

  const filteredUsers = users.filter(user =>
    (user.name && user.name.toLowerCase().includes(search.toLowerCase())) ||
    (user.username && user.username.toLowerCase().includes(search.toLowerCase()))
  );

  const paginatedUsers = filteredUsers.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Typography variant="h1" gutterBottom>
        User Management
      </Typography>
      
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <TextField
            variant="outlined"
            placeholder="Search users..."
            value={search}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: <SearchIcon />,
            }}
          />
        </Box>
        
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>User</TableCell>
                <TableCell>Username</TableCell>
                <TableCell>Groups</TableCell>
                <TableCell>Warnings</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {paginatedUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <Box display="flex" alignItems="center">
                      <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                        {user.name ? user.name.charAt(0) : 'U'}
                      </Avatar>
                      <Typography>{user.name}</Typography>
                    </Box>
                  </TableCell>
                  <TableCell>{user.username}</TableCell>
                  <TableCell>{user.groups || 0}</TableCell>
                  <TableCell>
                    <Chip 
                      icon={<WarningIcon />}
                      label={user.warnings}
                      color={user.warnings > 3 ? 'error' : user.warnings > 0 ? 'warning' : 'default'}
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={user.status} 
                      color={
                        user.status === 'active' ? 'success' : 
                        user.status === 'muted' ? 'warning' : 'error'
                      } 
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton onClick={() => handleEditUser(user)}>
                      <EditIcon />
                    </IconButton>
                    <IconButton onClick={() => handleWarnUser(user.id)}>
                      <WarningIcon />
                    </IconButton>
                    <IconButton onClick={() => handleBanUser(user.id)}>
                      <BlockIcon />
                    </IconButton>
                    <IconButton onClick={() => handleDeleteUser(user.id)}>
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={filteredUsers.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>
      
      {/* User Detail Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          User Details
        </DialogTitle>
        <DialogContent>
          {selectedUser && (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} display="flex" alignItems="center">
                <Avatar sx={{ mr: 2, width: 56, height: 56, fontSize: 24, bgcolor: 'primary.main' }}>
                  {selectedUser.name ? selectedUser.name.charAt(0) : 'U'}
                </Avatar>
                <Box>
                  <Typography variant="h4">{selectedUser.name}</Typography>
                  <Typography variant="body1">{selectedUser.username}</Typography>
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Typography><strong>Groups:</strong> {selectedUser.groups || 0}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography><strong>Status:</strong> {selectedUser.status}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography><strong>Warnings:</strong> {selectedUser.warnings}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography><strong>Member Since:</strong> {selectedUser.first_seen || 'N/A'}</Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography><strong>Last Active:</strong> {selectedUser.last_active || 'N/A'}</Typography>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}