"""
Handlers for personal features like notes, reminders, and bookmarks.
"""
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.constants import ParseMode
from ..database.db import Database
import logging
from datetime import datetime, timedelta
import re
from typing import List, Dict, Any
import json
import html

logger = logging.getLogger(__name__)

class PersonalCommandHandlers:
    def __init__(self):
        self.db = Database()

    async def handle_private_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle private chat messages and commands"""
        if not update.effective_message or not update.effective_user:
            return

        message = update.effective_message
        
        # If it's a command, let other handlers process it
        if message.text and message.text.startswith('/'):
            return
        
        # Handle non-command messages in private chat
        if message.text:
            text = message.text.lower().strip()
            
            # Quick help for common queries
            if any(word in text for word in ['help', 'what can you do', 'commands']):
                help_text = (
                    "🤖 <b>Available Personal Commands:</b>\n\n"
                    "📝 <b>Notes:</b>\n"
                    "• /note_add Title | Content | tag1, tag2\n"
                    "• /notes_list [tag]\n\n"
                    "⏰ <b>Reminders:</b>\n"
                    "• /remind 1h30m Check email\n\n"
                    "🔖 <b>Bookmarks:</b>\n"
                    "• /bookmark_add url | title | description | tags\n"
                    "• /bookmarks_list [tag]\n\n"
                    "📊 <b>Statistics:</b>\n"
                    "• /stats - View your activity stats\n\n"
                    "💡 <b>Custom Commands:</b>\n"
                    "• /command_add !command | response\n\n"
                    "<i>Use these commands to organize your personal data!</i>"
                )
                await message.reply_text(help_text, parse_mode=ParseMode.HTML)
                return
            
            # Quick note creation (if message starts with "note:")
            if text.startswith('note:'):
                note_content = message.text[5:].strip()
                if note_content:
                    try:
                        await self.db.add_note(
                            update.effective_user.id,
                            f"Quick Note {datetime.now().strftime('%m/%d %H:%M')}",
                            note_content,
                            ['quick']
                        )
                        await message.reply_text("✅ Quick note saved!")
                    except Exception as e:
                        logger.error(f"Error saving quick note: {e}")
                        await message.reply_text("❌ Failed to save note.")
                return
            
            # Quick reminder (if message starts with "remind:")
            if text.startswith('remind:'):
                reminder_text = message.text[7:].strip()
                if reminder_text:
                    try:
                        # Default to 1 hour reminder
                        remind_at = datetime.now() + timedelta(hours=1)
                        await self.db.add_reminder(
                            update.effective_user.id,
                            reminder_text,
                            remind_at
                        )
                        await message.reply_text(f"✅ Reminder set for 1 hour: {reminder_text}")
                    except Exception as e:
                        logger.error(f"Error setting quick reminder: {e}")
                        await message.reply_text("❌ Failed to set reminder.")
                return
            
            # For other messages, provide a helpful response
            await message.reply_text(
                "👋 Hi! I'm here to help you manage your personal data.\n\n"
                "💡 <b>Quick tips:</b>\n"
                "• Type 'help' for available commands\n"
                "• Start with 'note:' to quickly save a note\n"
                "• Start with 'remind:' to set a 1-hour reminder\n"
                "• Use /stats to see your activity\n\n"
                "<i>Try typing 'help' to see all available features!</i>",
                parse_mode=ParseMode.HTML
            )

    # Notes Handlers
    async def note_add(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Add a new note. Usage: /note_add Title | Content | tag1, tag2"""
        if not context.args:
            await update.message.reply_text(
                "Please provide note details in format:\n"
                "/note_add Title | Content | tag1, tag2"
            )
            return

        try:
            text = " ".join(context.args)
            parts = text.split("|")
            title = parts[0].strip()
            content = parts[1].strip() if len(parts) > 1 else ""
            tags = [t.strip() for t in parts[2].split(",")] if len(parts) > 2 else []

            await self.db.add_note(
                update.effective_user.id,
                title,
                content,
                tags
            )
            await update.message.reply_text("✅ Note added successfully!")
        except Exception as e:
            logger.error(f"Error adding note: {e}")
            await update.message.reply_text("❌ Failed to add note. Please check the format.")

    async def notes_list(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """List all notes or filter by tag"""
        tag = context.args[0] if context.args else None
        try:
            notes = await self.db.get_notes(update.effective_user.id, tag)
            if not notes:
                await update.message.reply_text("No notes found!")
                return

            message = "📝 Your Notes:\n\n"
            for note in notes:
                message += f"📌 {note['title']}\n"
                message += f"Content: {note['content']}\n"
                if note['tags']:
                    message += f"Tags: {', '.join(note['tags'])}\n"
                message += "\n"

            await update.message.reply_text(message)
        except Exception as e:
            logger.error(f"Error listing notes: {e}")
            await update.message.reply_text("❌ Failed to retrieve notes.")

    # Reminder Handlers
    async def remind(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Set a reminder. Usage: /remind 1h30m Check email"""
        if not context.args:
            await update.message.reply_text(
                "Please provide time and message:\n"
                "/remind 1h30m Check email\n"
                "Supported units: d (days), h (hours), m (minutes)"
            )
            return

        try:
            time_str = context.args[0]
            message = " ".join(context.args[1:])
            
            # Parse time string
            total_minutes = 0
            time_parts = re.findall(r'(\d+)([dhm])', time_str)
            
            for value, unit in time_parts:
                value = int(value)
                if unit == 'd':
                    total_minutes += value * 24 * 60
                elif unit == 'h':
                    total_minutes += value * 60
                elif unit == 'm':
                    total_minutes += value

            remind_at = datetime.now() + timedelta(minutes=total_minutes)
            
            await self.db.add_reminder(
                update.effective_user.id,
                message,
                remind_at
            )
            await update.message.reply_text(
                f"✅ Reminder set for {remind_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
                f"Message: {message}"
            )
        except Exception as e:
            logger.error(f"Error setting reminder: {e}")
            await update.message.reply_text("❌ Failed to set reminder. Please check the format.")

    # Bookmark Handlers
    async def bookmark_add(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Add a bookmark. Usage: /bookmark_add url | title | description | tag1, tag2"""
        if not context.args:
            await update.message.reply_text(
                "Please provide bookmark details:\n"
                "/bookmark_add url | title | description | tag1, tag2"
            )
            return

        try:
            text = " ".join(context.args)
            parts = text.split("|")
            url = parts[0].strip()
            title = parts[1].strip() if len(parts) > 1 else url
            description = parts[2].strip() if len(parts) > 2 else ""
            tags = [t.strip() for t in parts[3].split(",")] if len(parts) > 3 else []

            await self.db.add_bookmark(
                update.effective_user.id,
                url,
                title,
                description,
                tags
            )
            await update.message.reply_text("✅ Bookmark added successfully!")
        except Exception as e:
            logger.error(f"Error adding bookmark: {e}")
            await update.message.reply_text("❌ Failed to add bookmark. Please check the format.")

    async def bookmarks_list(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """List all bookmarks or filter by tag"""
        tag = context.args[0] if context.args else None
        try:
            bookmarks = await self.db.get_bookmarks(update.effective_user.id, tag)
            if not bookmarks:
                await update.message.reply_text("No bookmarks found!")
                return

            message = "🔖 Your Bookmarks:\n\n"
            for bookmark in bookmarks:
                message += f"📎 {bookmark['title']}\n"
                message += f"URL: {bookmark['url']}\n"
                if bookmark['description']:
                    message += f"Description: {bookmark['description']}\n"
                if bookmark['tags']:
                    message += f"Tags: {', '.join(bookmark['tags'])}\n"
                message += "\n"

            await update.message.reply_text(message)
        except Exception as e:
            logger.error(f"Error listing bookmarks: {e}")
            await update.message.reply_text("❌ Failed to retrieve bookmarks.")

    # Custom Commands Handlers
    async def command_add(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Add a custom command. Usage: /command_add !command | response"""
        if not context.args:
            await update.message.reply_text(
                "Please provide command details:\n"
                "/command_add !command | response"
            )
            return

        try:
            text = " ".join(context.args)
            command, response = [x.strip() for x in text.split("|", 1)]
            
            await self.db.add_custom_command(
                update.effective_chat.id,
                command,
                response,
                update.effective_user.id
            )
            await update.message.reply_text(f"✅ Custom command {command} added successfully!")
        except Exception as e:
            logger.error(f"Error adding custom command: {e}")
            await update.message.reply_text("❌ Failed to add custom command.")

    # Statistics Handler
    async def stats(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show user statistics"""
        if not update.effective_chat or not update.effective_user:
            return

        chat = update.effective_chat
        user = update.effective_user

        # Get user stats
        user_stats = await self.db.get_user_stats(chat.id, user.id) if self.db else None
        user_warnings = await self.db.get_user_warnings(chat.id, user.id) if self.db else []

        # Format dates and stats
        first_seen = None
        last_active = None
        days_since_join = 0
        message_count = 0
        command_count = 0

        if user_stats:
            if user_stats.get('first_seen'):
                first_seen = datetime.fromisoformat(user_stats['first_seen'])
            if user_stats.get('last_active'):
                last_active = datetime.fromisoformat(user_stats['last_active'])
            days_since_join = user_stats.get('days_since_join', 0)
            message_count = user_stats.get('message_count', 0)
            command_count = user_stats.get('command_count', 0)

        # Calculate daily average
        daily_avg = round(message_count / max(days_since_join, 1), 1) if days_since_join > 0 else 0

        stats_text = f"""
╔═══『 <b>📊 USER STATISTICS</b> 』═══╗

║ <b>👤 User:</b> {html.escape(user.full_name)}
║ <b>🆔 ID:</b> <code>{user.id}</code>

╠═══『 <b>💭 ACTIVITY</b> 』═══╣

║ <b>📨 Total Messages:</b> <code>{message_count:,}</code>
║ <b>📝 Commands Used:</b> <code>{command_count:,}</code>
║ <b>📈 Daily Average:</b> <code>{daily_avg}</code> messages
║ <b>⚠️ Warnings:</b> <code>{len(user_warnings)}</code>

╠═══『 <b>⏰ TIMELINE</b> 』═══╣

║ <b>📅 First Seen:</b>
║ <code>{first_seen.strftime('%B %d, %Y at %I:%M %p') if first_seen else "Not tracked yet"}</code>
║ <b>🕒 Last Active:</b>
║ <code>{last_active.strftime('%B %d, %Y at %I:%M %p') if last_active else "Not tracked yet"}</code>
║ <b>📊 Active Days:</b> <code>{days_since_join}</code>

╚═══『 <b>Powered by HelpingAI</b> 』═══╝"""

        try:
            await update.message.reply_text(
                text=stats_text,
                parse_mode=ParseMode.HTML
            )
        except Exception as e:
            await update.message.reply_text(
                "❌ Error displaying stats. Please try again later."
            )

    def register_handlers(self, application):
        """Register all personal command handlers"""
        from telegram.ext import CommandHandler
        
        # Notes commands
        application.add_handler(CommandHandler("note_add", self.note_add))
        application.add_handler(CommandHandler("notes_list", self.notes_list))
        
        # Reminder commands
        application.add_handler(CommandHandler("remind", self.remind))
        
        # Bookmark commands
        application.add_handler(CommandHandler("bookmark_add", self.bookmark_add))
        application.add_handler(CommandHandler("bookmarks_list", self.bookmarks_list))
        
        # Custom command commands
        application.add_handler(CommandHandler("command_add", self.command_add))
        
        # Statistics command
        application.add_handler(CommandHandler("stats", self.stats))
