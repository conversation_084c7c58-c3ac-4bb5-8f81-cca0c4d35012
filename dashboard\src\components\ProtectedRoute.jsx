import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import {
  Box,
  Paper,
  Typography,
  Button,
  Container,
  Alert,
  AlertTitle
} from '@mui/material';
import { Login as LoginIcon, Security as SecurityIcon } from '@mui/icons-material';

const ProtectedRoute = ({ children, requireAuth = true }) => {
  const { isAuthenticated, loading } = useAuth();
  const location = useLocation();

  if (loading) {
    return (
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        <Typography variant="h6">Loading...</Typography>
      </Box>
    );
  }

  if (requireAuth && !isAuthenticated) {
    return (
      <Container maxWidth="md" sx={{ mt: 8 }}>
        <Paper 
          elevation={3} 
          sx={{ 
            p: 6, 
            textAlign: 'center',
            borderRadius: 3,
            background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)'
          }}
        >
          <SecurityIcon sx={{ fontSize: 80, color: 'primary.main', mb: 3 }} />
          
          <Typography variant="h4" fontWeight="bold" gutterBottom>
            Authentication Required
          </Typography>
          
          <Typography variant="body1" color="text.secondary" sx={{ mb: 4, maxWidth: 500, mx: 'auto' }}>
            This section requires administrator privileges. Please sign in to access 
            settings and administrative features.
          </Typography>
          
          <Alert severity="info" sx={{ mb: 4, textAlign: 'left' }}>
            <AlertTitle>Note</AlertTitle>
            You can view most dashboard sections without signing in. Only administrative 
            functions like settings require authentication.
          </Alert>
          
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              size="large"
              startIcon={<LoginIcon />}
              component="a"
              href="/login"
              sx={{ 
                borderRadius: 3,
                px: 4,
                py: 1.5
              }}
            >
              Sign In
            </Button>
            
            <Button
              variant="outlined"
              size="large"
              component="a"
              href="/"
              sx={{ 
                borderRadius: 3,
                px: 4,
                py: 1.5
              }}
            >
              Back to Home
            </Button>
          </Box>
        </Paper>
      </Container>
    );
  }

  return children;
};

export default ProtectedRoute;
