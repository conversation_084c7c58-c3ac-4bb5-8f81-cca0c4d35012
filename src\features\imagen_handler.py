"""
🖼️ Imagen Handler Module
------------------------
<PERSON>les AI image generation commands.
"""

import os
import logging
import asyncio
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Context<PERSON>ypes, CommandHandler, CallbackQueryHandler
from telegram.constants import ParseMode, ChatAction
import requests
import urllib.parse
from datetime import datetime

# Set up logging
logger = logging.getLogger(__name__)

class ImagenHandler:
    """Handler for AI image generation commands."""
    
    def __init__(self):
        """Initialize the imagen handler."""
        self.api_url = "https://1yjs1yldj7.execute-api.us-east-1.amazonaws.com/default/ai_image"
        
    async def generate_image(self, prompt, link="telegram-bot"):
        """
        Generate an AI image using the provided API.
        
        Args:
            prompt (str): The description of the image to generate
            link (str, optional): The referrer link
            
        Returns:
            dict: The JSON response from the API
        """
        # URL encode the parameters
        params = {
            "prompt": prompt,
            "link": link
        }
        
        try:
            # Make the GET request
            response = requests.get(self.api_url, params=params)
            
            # Check if the request was successful
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"API request failed with status code {response.status_code}: {response.text}")
                return {
                    "status": "error",
                    "message": f"Request failed with status code {response.status_code}",
                    "response": response.text
                }
        except Exception as e:
            logger.error(f"Error generating image: {str(e)}")
            return {
                "status": "error",
                "message": f"Error: {str(e)}"
            }
    
    async def _delete_message_later(self, message, delay=60):
        """Delete a message after a specified delay in seconds."""
        try:
            await asyncio.sleep(delay)
            await message.delete()
        except Exception as e:
            logger.error(f"Error deleting message: {e}")
    
    async def imagen_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle the /imagen command to generate AI images."""
        # Check if command is used in a private chat
        if update.effective_chat.type != 'private':
            # Command used in a group - inform user it only works in DM
            help_message = await update.message.reply_text(
                "🖼️ <b>AI Image Generator</b>\n\n"
                "This feature only works in private chat with the bot.\n"
                "Please message me directly to use this command.",
                parse_mode=ParseMode.HTML
            )
            
            # Delete messages after delay
            try:
                # Try to delete the command message
                await update.message.delete()
            except Exception as e:
                logger.error(f"Error deleting command message: {e}")
                
            # Delete the help message after 30 seconds
            asyncio.create_task(self._delete_message_later(help_message, 30))
            return
        
        # Continue with normal processing for private chats
        # Check if the user provided a prompt
        if not context.args:
            # Show help message if no prompt provided
            help_text = (
                "🖼️ <b>AI Image Generator</b>\n\n"
                "<b>Usage:</b>\n"
                "<code>/imagen [prompt]</code>\n\n"
                "<b>Example:</b>\n"
                "<code>/imagen a red sports car on a mountain road</code>"
            )
            
            await update.message.reply_text(
                help_text,
                parse_mode=ParseMode.HTML
            )
            return
        
        # Get the prompt from arguments
        prompt = " ".join(context.args)
        
        # Store the prompt in user_data for later use
        if not context.user_data.get("imagen_prompts"):
            context.user_data["imagen_prompts"] = {}
        
        user_id = update.effective_user.id
        context.user_data["imagen_prompts"][user_id] = prompt
        
        # Send "generating" message
        await context.bot.send_chat_action(
            chat_id=update.effective_chat.id,
            action=ChatAction.UPLOAD_PHOTO
        )
        
        processing_message = await update.message.reply_text(
            "🎨 <b>Generating your image...</b>\n"
            "This may take a few moments.",
            parse_mode=ParseMode.HTML
        )
        
        # Generate the image
        result = await self.generate_image(prompt)
        
        # Delete the processing message
        await processing_message.delete()
        
        if result.get("status") == "success":
            image_url = result.get("image_link")
            
            # Create caption with prompt
            caption = f"🖼️ <b>Generated Image</b>\n\n<i>{prompt}</i>\n\n⏳ <i>This image will self-destruct in 60 seconds</i>"
            
            # Create keyboard for regeneration
            keyboard = [
                [
                    InlineKeyboardButton("🔄 Regenerate", callback_data=f"imagen_regen_{user_id}"),
                    InlineKeyboardButton("🎨 Change Style", callback_data=f"imagen_style_{user_id}")
                ]
            ]
            
            # Send the image
            sent_message = await update.message.reply_photo(
                photo=image_url,
                caption=caption,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode=ParseMode.HTML
            )
            
            # Schedule message deletion after 60 seconds
            asyncio.create_task(self._delete_message_later(sent_message, 60))
            
            # Try to delete the command message
            try:
                await update.message.delete()
            except Exception as e:
                logger.error(f"Error deleting command message: {e}")
                
        else:
            error_message = result.get("message", "Unknown error")
            error_msg = await update.message.reply_text(
                f"❌ <b>Failed to generate image</b>\n\n{error_message}",
                parse_mode=ParseMode.HTML
            )
            # Delete error message after 30 seconds
            asyncio.create_task(self._delete_message_later(error_msg, 30))
    
    async def handle_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle callback queries for imagen commands."""
        query = update.callback_query
        await query.answer()
        
        # Get the callback data
        data = query.data
        
        if data.startswith("imagen_regen_"):
            # Handle regeneration request
            user_id = int(data.replace("imagen_regen_", ""))
            
            # Get the stored prompt
            prompt = context.user_data.get("imagen_prompts", {}).get(user_id)
            
            if not prompt:
                error_msg = await query.message.reply_text(
                    "❌ Sorry, I couldn't find your original prompt. Please try again with /imagen command.",
                    parse_mode=ParseMode.HTML
                )
                # Delete error message after 30 seconds
                asyncio.create_task(self._delete_message_later(error_msg, 30))
                return
            
            # Send "generating" message
            await context.bot.send_chat_action(
                chat_id=update.effective_chat.id,
                action=ChatAction.UPLOAD_PHOTO
            )
            
            processing_message = await query.message.reply_text(
                "🎨 <b>Regenerating your image...</b>\n"
                "This may take a few moments.",
                parse_mode=ParseMode.HTML
            )
            
            # Generate the image
            result = await self.generate_image(prompt)
            
            # Delete the processing message
            await processing_message.delete()
            
            if result.get("status") == "success":
                image_url = result.get("image_link")
                
                # Create caption with prompt
                caption = f"🖼️ <b>Regenerated Image</b>\n\n<i>{prompt}</i>\n\n⏳ <i>This image will self-destruct in 60 seconds</i>"
                
                # Create keyboard for regeneration
                keyboard = [
                    [
                        InlineKeyboardButton("🔄 Regenerate", callback_data=f"imagen_regen_{user_id}"),
                        InlineKeyboardButton("🎨 Change Style", callback_data=f"imagen_style_{user_id}")
                    ]
                ]
                
                # Send the image
                sent_message = await query.message.reply_photo(
                    photo=image_url,
                    caption=caption,
                    reply_markup=InlineKeyboardMarkup(keyboard),
                    parse_mode=ParseMode.HTML
                )
                
                # Schedule message deletion after 60 seconds
                asyncio.create_task(self._delete_message_later(sent_message, 60))
                
            else:
                error_message = result.get("message", "Unknown error")
                error_msg = await query.message.reply_text(
                    f"❌ <b>Failed to regenerate image</b>\n\n{error_message}",
                    parse_mode=ParseMode.HTML
                )
                # Delete error message after 30 seconds
                asyncio.create_task(self._delete_message_later(error_msg, 30))
        
        elif data.startswith("imagen_style_"):
            # Handle style change request
            style_msg = await query.message.reply_text(
                "🎨 <b>Style change feature coming soon!</b>",
                parse_mode=ParseMode.HTML
            )
            # Delete message after 30 seconds
            asyncio.create_task(self._delete_message_later(style_msg, 30))

def setup(application):
    """Set up the imagen handler."""
    imagen_handler = ImagenHandler()
    
    # Add command handler
    application.add_handler(CommandHandler("imagen", imagen_handler.imagen_command))
    
    # Add callback query handler
    application.add_handler(CallbackQueryHandler(
        imagen_handler.handle_callback,
        pattern=r"^imagen_"
    ))
    
    logger.info("Imagen handlers registered")
