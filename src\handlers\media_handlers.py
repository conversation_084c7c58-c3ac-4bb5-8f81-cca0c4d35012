"""
Handlers for media-related commands.
"""
from telegram import Update
from telegram.constants import ParseMode
from telegram.ext import ContextTypes
from ..api_services.media_services import MediaServices
import logging
import asyncio

logger = logging.getLogger(__name__)

class MediaCommandHandlers:
    def __init__(self):
        self.media_service = MediaServices()

    async def meme(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Send a random meme that auto-deletes after 30 seconds"""
        try:
            # Send "loading" message
            loading_message = await update.message.reply_text(
                "🔍 Finding a fire meme for you...",
                parse_mode=ParseMode.HTML
            )

            # Get and send meme
            meme_data = await self.media_service.get_random_meme()
            caption = f"📸 {meme_data['title']}\nBy: {meme_data['author']}\n\n⏳ This meme will self-destruct in 30s!"
            meme_message = await update.message.reply_photo(meme_data['url'], caption=caption)
            
            # Delete the loading message
            await loading_message.delete()
            
            # Delete command message
            try:
                await update.message.delete()
            except Exception:
                pass  # Ignore if we can't delete command
            
            # Schedule deletion after 30 seconds
            async def delete_meme():
                await asyncio.sleep(30)
                try:
                    await meme_message.delete()
                except Exception:
                    pass  # Ignore if message is already deleted
                    
            # Run deletion task in background
            asyncio.create_task(delete_meme())
            
        except Exception as e:
            logger.error(f"Error getting meme: {e}")
            error_msg = await update.message.reply_text(
                "❌ Yo my bad, couldn't fetch a meme! Try again later fam!"
            )
            # Delete error message after 5 seconds
            await asyncio.sleep(5)
            await error_msg.delete()

    async def qr(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Generate QR code from text that auto-deletes after 30 seconds"""
        if not context.args:
            error_msg = await update.message.reply_text(
                "❌ Yo fam! Drop some text after /qr\n"
                "Example: <code>/qr https://example.com</code>",
                parse_mode=ParseMode.HTML
            )
            # Delete error message after 5 seconds
            await asyncio.sleep(5)
            await error_msg.delete()
            return

        text = " ".join(context.args)
        try:
            # Send loading message
            loading_message = await update.message.reply_text(
                "🎨 Creating your QR code...",
                parse_mode=ParseMode.HTML
            )

            # Generate and send QR
            qr_image = self.media_service.generate_qr_code(text)
            qr_message = await update.message.reply_photo(
                qr_image,
                caption=f"🔲 QR Code for: <code>{text}</code>\n\n⏳ This QR will self-destruct in 30s!",
                parse_mode=ParseMode.HTML
            )
            
            # Delete loading message
            await loading_message.delete()
            
            # Delete command message
            try:
                await update.message.delete()
            except Exception:
                pass  # Ignore if we can't delete command
            
            # Schedule deletion after 30 seconds
            async def delete_qr():
                await asyncio.sleep(30)
                try:
                    await qr_message.delete()
                except Exception:
                    pass  # Ignore if message is already deleted
                    
            # Run deletion task in background
            asyncio.create_task(delete_qr())
            
        except Exception as e:
            logger.error(f"Error generating QR code: {e}")
            error_msg = await update.message.reply_text(
                "❌ Oops! Something went wrong generating that QR code!\n"
                "Try again with different text fam!"
            )
            # Delete error message after 5 seconds
            await asyncio.sleep(5)
            await error_msg.delete()

    async def handle_media(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle incoming media messages"""
        if not update.effective_message or not update.effective_chat:
            return

        try:
            # Get message type
            message_type = None
            if update.message.photo:
                message_type = "photo"
            elif update.message.video:
                message_type = "video"
            elif update.message.document:
                message_type = "document"
            
            if not message_type:
                return
                
            # Log media message
            logger.info(
                f"Media received in {update.effective_chat.title} ({update.effective_chat.id}): "
                f"{message_type} from {update.effective_user.full_name}"
            )
            
            # Here you can add more media handling logic
            # For example:
            # - Check file size
            # - Verify file type
            # - Scan for NSFW content
            # - Apply watermarks
            # - Convert formats
            # etc.
            
        except Exception as e:
            logger.error(f"Error handling media: {str(e)}")
