# Multi-stage Dockerfile for HelpingAI Dashboard

# Stage 1: Build the React frontend
FROM node:18-alpine AS frontend-builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY backend/package*.json ./backend/

# Install dependencies
RUN npm install

# Copy frontend source
COPY . .

# Build the frontend
RUN npm run build

# Stage 2: Build the backend
FROM node:18-alpine AS backend-builder

WORKDIR /app

# Copy backend package files
COPY backend/package*.json ./

# Install backend dependencies
RUN npm install --only=production

# Copy backend source
COPY backend/*.js ./

# Stage 3: Production image
FROM node:18-alpine

WORKDIR /app

# Copy backend dependencies and source
COPY --from=backend-builder /app/node_modules ./node_modules
COPY --from=backend-builder /app/*.js ./

# Copy built frontend
COPY --from=frontend-builder /app/dist ./dist

# Expose port
EXPOSE 5000

# Start the application
CMD ["node", "server.js"]