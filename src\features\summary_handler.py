import asyncio
from datetime import datetime, timedelta, timezone
from sqlalchemy import and_
from telegram import Update
from telegram.ext import ContextTypes

# Importing models as defined in the DB code.
from src.database.models import Message, ChatSummary, User, init_db
from src.api_services.pollinations_ai import PollinationsAI

class SummaryHandler:
    def __init__(self):
        # Initialize DB session using the provided init_db function.
        self.db_session = init_db()

    def _extract_message_text(self, msg) -> str:
        """
        Safely extract text from a message object.
        For database Message objects, use the message_text attribute.
        For Telegram Message objects, use the text attribute.
        """
        if hasattr(msg, "message_text"):
            return msg.message_text
        elif hasattr(msg, "text"):
            return msg.text
        return ""
    
    async def store_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """
        Stores a user's message in real time to the database.
        This method should be registered as a message handler so that every incoming message is saved.
        """
        if not update.message or not update.message.text:
            return  # Skip non-text messages

        chat_id = update.effective_chat.id
        user_data = update.message.from_user

        # Check if the user already exists in the DB; if not, create one.
        db_user = self.db_session.query(User).filter(User.telegram_id == user_data.id).first()
        if not db_user:
            db_user = User(
                telegram_id=user_data.id,
                username=user_data.username,
                first_name=user_data.first_name
            )
            self.db_session.add(db_user)
            self.db_session.commit()

        # Create a new message record using the 'message_text' field as defined in the DB schema.
        new_message = Message(
            user_id=db_user.id,
            chat_id=chat_id,
            message_text=update.message.text,
            timestamp=datetime.now(timezone.utc)
        )
        self.db_session.add(new_message)
        self.db_session.commit()

    async def generate_daily_summary(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Generate a summary of chat messages from the last 24 hours."""
        chat_id = update.effective_chat.id
        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(days=1)

        messages = self.db_session.query(Message).filter(
            and_(
                Message.chat_id == chat_id,
                Message.timestamp >= start_time,
                Message.timestamp <= end_time
            )
        ).all()

        if not messages:
            await update.message.reply_text("No messages found in the last 24 hours.")
            return

        message_texts = [self._extract_message_text(msg) for msg in messages]
        prompt = "Create a concise summary of the chat discussions."
        summary = await self._generate_summary(message_texts, prompt)

        # Create a ChatSummary record with the given summary.
        chat_summary = ChatSummary(
            chat_id=chat_id,
            summary_text=summary,
            start_time=start_time,
            end_time=end_time,
            summary_type='daily'
        )
        self.db_session.add(chat_summary)
        self.db_session.commit()

        await update.message.reply_text(f"📅 Daily Summary:\n\n{summary}")

    async def generate_user_summary(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """
        Generate a summary of messages from a specific user.
        Supports providing a username as a command argument or via replying to a message.
        Uses up to the last 10 messages from the specified user (minimum 1, maximum 10) as context.
        """
        username = None
        # Determine username from command args or reply message.
        if context.args:
            username = context.args[0].lstrip('@')
        elif update.message.reply_to_message:
            if update.message.reply_to_message.from_user.username:
                username = update.message.reply_to_message.from_user.username
            else:
                username = update.message.reply_to_message.from_user.first_name

        if not username:
            await update.message.reply_text(
                "Please provide a username to summarize or reply to a user's message."
            )
            return

        chat_id = update.effective_chat.id

        # Query up to the last 10 messages from the specified user in this chat.
        messages = self.db_session.query(Message).join(Message.user).filter(
            and_(
                Message.chat_id == chat_id,
                Message.user.has(User.username == username)
            )
        ).order_by(Message.timestamp.desc()).limit(10).all()

        # If no messages are found in the DB and the command was a reply, use the replied message.
        if not messages and update.message.reply_to_message:
            messages = [update.message.reply_to_message]

        if not messages:
            await update.message.reply_text(f"No messages found from user @{username}")
            return

        # Reverse the list to maintain chronological order
        messages.reverse()
        messages_texts = [self._extract_message_text(msg) for msg in messages]

        prompt = (
            f"You are an assistant that produces comprehensive summaries. Given the full context of up to the last 10 messages "
            f"from the user with username @{username} in chat with ID {chat_id}, produce an insightful, detailed summary "
            "that captures the key points, mood, and overall tone of the conversation."
        )
        summary = await self._generate_summary(messages_texts, prompt)
        await update.message.reply_text(f"👤 User Summary for chat {chat_id} (@{username}):\n\n{summary}")

    async def fact_check(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Fact check a statement."""
        if not update.message.reply_to_message:
            await update.message.reply_text("Please reply to a message to fact-check it.")
            return

        statement = self._extract_message_text(update.message.reply_to_message)
        response = await self._generate_fact_check(statement)
        await update.message.reply_text(f"✅ Fact Check:\n\n{response}")

    async def ai_commentary(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Provide AI commentary on the current discussion."""
        chat_id = update.effective_chat.id
        recent_messages = self.db_session.query(Message).filter(
            Message.chat_id == chat_id
        ).order_by(Message.timestamp.desc()).limit(10).all()

        if not recent_messages:
            await update.message.reply_text("No recent messages found to comment on.")
            return

        message_texts = [self._extract_message_text(msg) for msg in recent_messages]
        prompt = ("Provide an insightful commentary on the current discussion based on the full context of "
                  "the recent messages.")
        commentary = await self._generate_summary(message_texts, prompt)
        await update.message.reply_text(f"🗣 AI Commentary:\n\n{commentary}")

    async def _generate_summary(self, messages: list, prompt: str) -> str:
        """Generate a summary using PollinationsAI's API."""
        messages_text = "\n".join(messages)
        try:
            payload = [
                {"role": "system", "content": prompt},
                {"role": "user", "content": messages_text}
            ]
            response = await PollinationsAI.generate_text_async(model="openai-large", messages=payload)
            return response
        except Exception as e:
            return f"Error generating summary: {str(e)}"

    async def _generate_fact_check(self, statement: str) -> str:
        """Generate a fact check using PollinationsAI's API."""
        try:
            payload = [
                {"role": "system", "content": "You are a fact-checker. Verify the following statement and provide evidence to support or refute it."},
                {"role": "user", "content": statement}
            ]
            response = await PollinationsAI.generate_text_async(model="openai-large", messages=payload)
            return response
        except Exception as e:
            return f"Error generating fact check: {str(e)}"

# End of summary_handler module.