@echo off
echo Installing HelpingAI Dashboard dependencies...
echo.

echo Installing frontend dependencies...
npm install
if %errorlevel% neq 0 (
    echo Failed to install frontend dependencies
    exit /b %errorlevel%
)

echo.
echo Installing backend dependencies...
cd backend
npm install
if %errorlevel% neq 0 (
    echo Failed to install backend dependencies
    exit /b %errorlevel%
)

echo.
echo Dashboard setup complete!
echo.
echo To start the dashboard:
echo 1. Start the backend: cd backend ^&^& npm start
echo 2. Start the frontend: npm run dev