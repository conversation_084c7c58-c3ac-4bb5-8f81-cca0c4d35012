import asyncio
import logging
from typing import Dict

# Simulated Telegram API objects for testing purposes
class Update:
    def __init__(self, chat_id: int, args: list, effective_message: 'Message'):
        self.effective_chat = type("Chat", (), {"id": chat_id, "type": "group"})
        # Note: In real usage, arguments are provided via context.args, not update.
        self.args = args  
        self.effective_message = effective_message

class Message:
    def __init__(self):
        self.text = ""
    async def reply_text(self, text, parse_mode=None):
        print(text)

class ChatPermissions:
    # Simulate ChatPermissions as a simple wrapper.
    def __init__(self, **kwargs):
        self.permissions = kwargs
    def __str__(self):
        return str(self.permissions)

# Simulated ContextTypes and Bot
class Context:
    def __init__(self, bot, args=None):
        self.bot = bot
        self.args = args or []
        
class Bot:
    async def set_chat_permissions(self, chat_id: int, permissions: ChatPermissions):
        print(f"Bot updated chat permissions for chat_id {chat_id}: {permissions}")

# Simulated decorators
def admin_only():
    def decorator(func):
        async def wrapper(self, update, context, *args, **kwargs):
            # For testing, always assume admin access
            return await func(self, update, context, *args, **kwargs)
        return wrapper
    return decorator

def group_only():
    def decorator(func):
        async def wrapper(self, update, context, *args, **kwargs):
            # For testing, always assume group command
            return await func(self, update, context, *args, **kwargs)
        return wrapper
    return decorator

# Simulated Database class; settings are stored in memory.
class Database:
    def __init__(self):
        # key: (chat_id, setting) -> value
        self.settings = {}
    async def get_group_setting(self, chat_id: int, setting: str, default_value):
        key = (chat_id, setting)
        if key not in self.settings or self.settings[key] is None:
            # Simulate saving default in DB if not exists.
            self.settings[key] = default_value
            return default_value
        return self.settings[key]
    async def set_group_setting(self, chat_id: int, setting: str, value):
        key = (chat_id, setting)
        self.settings[key] = value
        return True

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

class RestrictionTools:
    """
    Provides commands to enable or disable specific restrictions in a group and
    updates the group default permissions to restrict members from sending
    particular types of content.

    Available restrictions (flags):
      • media    - Restrict media messages (key: media_filter_enabled)
      • stickers - Restrict stickers and other non-text messages (key: sticker_filter_enabled)
      • polls    - Restrict polls (key: poll_filter_enabled)
      • invite   - Restrict invite links (key: invite_filter_enabled)
      • preview  - Restrict link previews (key: preview_filter_enabled)
      • all      - Apply change to all of the above restrictions

    Note: By default, all restrictions are off.
    """
    def __init__(self, db: Database):
        self.db = db

        # Mapping of command flags to database keys.
        self.flag_mapping: Dict[str, str] = {
            "media": "media_filter_enabled",
            "stickers": "sticker_filter_enabled",
            "polls": "poll_filter_enabled",
            "invite": "invite_filter_enabled",
            "preview": "preview_filter_enabled"
        }

    async def update_group_permissions(self, chat_id: int, context: Context) -> None:
        """
        Update the default chat permissions for the group based on the current
        restriction settings stored in the database.
        By default, restrictions are off.
        """
        try:
            media_restricted = await self.db.get_group_setting(chat_id, "media_filter_enabled", False)
            sticker_restricted = await self.db.get_group_setting(chat_id, "sticker_filter_enabled", False)
            poll_restricted = await self.db.get_group_setting(chat_id, "poll_filter_enabled", False)
            invite_restricted = await self.db.get_group_setting(chat_id, "invite_filter_enabled", False)
            preview_restricted = await self.db.get_group_setting(chat_id, "preview_filter_enabled", False)

            # Map restrictions to ChatPermissions (allowed if not restricted)
            permissions = ChatPermissions(
                can_send_messages=True,
                can_send_polls=not poll_restricted,
                can_send_other_messages=not sticker_restricted,
                can_add_web_page_previews=not preview_restricted,
                can_invite_users=not invite_restricted,
                can_send_audios=not media_restricted,
                can_send_documents=not media_restricted,
                can_send_photos=not media_restricted,
                can_send_videos=not media_restricted,
                can_send_video_notes=not media_restricted,
                can_send_voice_notes=not media_restricted
            )

            await context.bot.set_chat_permissions(chat_id, permissions)
            logger.info(f"Updated chat permissions for chat_id {chat_id}")
        except Exception as e:
            logger.error(f"Error updating group permissions: {e}")

    @admin_only()
    @group_only()
    async def restrict_command(self, update: Update, context: Context) -> None:
        """
        Command to update restriction settings and subsequently update group permissions.
        Usage: /restrict [flag] [on/off]
        Flags:
            media, stickers, polls, invite, preview, all
        Example:
            /restrict stickers on
            /restrict all off

        Note: By default, all restrictions are off.
        """
        message = update.effective_message
        if not message:
            logger.error("No effective message available to reply.")
            return

        chat_id = update.effective_chat.id

        # Use context.args rather than update.args
        if not context.args or len(context.args) < 2:
            current_settings = []
            for flag, key in self.flag_mapping.items():
                state = await self.db.get_group_setting(chat_id, key, False)
                current_settings.append(f"• {flag}: {'On' if state else 'Off'}")
            response_text = (
                "<b>🚫 Restriction Settings</b>\n\n" +
                "\n".join(current_settings) +
                "\n\nUsage:\n<code>/restrict [flag] [on/off]</code>\n" +
                "Flags: media, stickers, polls, invite, preview, all"
            )
            await message.reply_text(response_text, parse_mode="HTML")
            return

        flag = context.args[0].lower()
        state_arg = context.args[1].lower()

        # Validate state
        if state_arg not in ["on", "off"]:
            await message.reply_text("❌ Invalid state! Use 'on' or 'off'.", parse_mode="HTML")
            return

        new_state = True if state_arg == "on" else False

        try:
            if flag == "all":
                for key in self.flag_mapping.values():
                    await self.db.set_group_setting(chat_id, key, new_state)
                response = f"✅ All restrictions have been turned {'ON' if new_state else 'OFF'}."
            elif flag in self.flag_mapping:
                setting_key = self.flag_mapping[flag]
                await self.db.set_group_setting(chat_id, setting_key, new_state)
                response = f"✅ Restriction for <b>{flag}</b> has been turned {'ON' if new_state else 'OFF'}."
            else:
                await message.reply_text(
                    "❌ Invalid flag! Available flags: media, stickers, polls, invite, preview, all.",
                    parse_mode="HTML"
                )
                return

            await message.reply_text(response, parse_mode="HTML")
            await self.update_group_permissions(chat_id, context)
        except Exception as e:
            logger.error(f"Error in restrict_command: {e}")
            await message.reply_text(f"❌ Error: {e}", parse_mode="HTML")

# ----- Testing simulation -----
async def simulate_commands():
    db = Database()
    bot = Bot()

    # Simulate: /restrict stickers off
    msg1 = Message()
    # In simulation, context.args simulates the command arguments.
    context1 = Context(bot, args=["stickers", "off"])
    update1 = Update(chat_id=12345, args=[], effective_message=msg1)
    print("\nSimulating: /restrict stickers off")
    await RestrictionTools(db).restrict_command(update1, context1)

    # Simulate: /restrict with no arguments to show current settings.
    msg2 = Message()
    context2 = Context(bot, args=[])
    update2 = Update(chat_id=12345, args=[], effective_message=msg2)
    print("\nSimulating: /restrict (no arguments)")
    await RestrictionTools(db).restrict_command(update2, context2)

if __name__ == "__main__":
    asyncio.run(simulate_commands())
    print("RestrictionTools module loaded. Integrate /restrict command with your bot.")