"""Group settings and management with advanced features 🛡️"""

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ChatPermissions
from telegram.ext import ContextTypes
from telegram.constants import ParseMode, ChatType
from telegram.error import TelegramError
import html
import logging
import traceback
import asyncio
import datetime
from collections import defaultdict
from time import time

from ...database.db import Database
from ...utils.decorators import admin_only, group_only
from .protection import ProtectionHandler
from .welcome import WelcomeHandler
from .rules import RulesHandler
from .antiflood import AntiFloodHandler
from ..admin_tools import AdminTools
from src.features import admin_tools

from src.database import db

logger = logging.getLogger(__name__)

class GroupSettings:
    def __init__(self, db: Database, admin_tools: AdminTools = None):
        self.db = db
        self.admin_tools = admin_tools
        self.welcome = WelcomeHandler(db)
        self.rules = RulesHandler(db)
        self.antiflood = AntiFloodHandler(db)
        # Initialize protection to None by default
        self.protection = None
        
    async def initialize(self):
        """Initialize async components"""
        await self.antiflood.initialize()
        
        # Initialize protection handler only if we have admin tools
        if self.admin_tools:
            self.protection = ProtectionHandler(self.db, self.admin_tools)
        # No need for else as protection is already None

    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle all incoming messages with protection checks"""
        try:
            # Skip non-group messages
            if not update.effective_chat or update.effective_chat.type == ChatType.PRIVATE:
                return

            # Check for flooding first
            if await self.antiflood.check_flood(update, context):
                return
            
            # Then check other protections if initialized
            if self.protection:
                await self.protection.handle_message(update, context)
            
        except Exception as e:
            logger.error(f"Error in handle_message: {str(e)}")
            logger.error(traceback.format_exc())

    async def handle_new_member(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Welcome new members with style 🎉"""
        try:
            if not update.effective_chat:
                return

            chat_id = update.effective_chat.id
            
            # Set default permissions for new members using correct parameters
            permissions = ChatPermissions(
                can_send_messages=True,
                can_send_polls=True,
                can_send_other_messages=True,  # For stickers, GIFs, games, inline bots
                can_add_web_page_previews=True,
                can_change_info=False,
                can_invite_users=True,
                can_pin_messages=False,
                can_manage_topics=False,
                can_send_audios=True,
                can_send_documents=True,
                can_send_photos=True,
                can_send_videos=True,
                can_send_video_notes=True,
                can_send_voice_notes=True
            )
            
            try:
                # Set chat permissions
                await context.bot.set_chat_permissions(chat_id, permissions)
            except TelegramError as e:
                logger.error(f"Failed to set chat permissions: {e}")

            # Handle welcome message
            await self.welcome.handle_new_member(update, context)
            
        except Exception as e:
            logger.error(f"Error in handle_new_member: {str(e)}")
            logger.error(traceback.format_exc())

    async def handle_member_left(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Say goodbye to leaving members 👋"""
        try:
            await self.welcome.handle_member_left(update, context)
        except Exception as e:
            logger.error(f"Error in handle_member_left: {str(e)}")
            logger.error(traceback.format_exc())

    # Protection Commands 🛡️
    @admin_only()
    @group_only()
    async def setup_protection(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Quick setup for protection features"""
        if self.protection:
            await self.protection.setup_protection(update, context)

    # toggle_protection method removed - functionality moved to /nsfw command

    # Welcome Message Commands 👋
    @admin_only()
    @group_only()
    async def set_welcome(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Set custom welcome message"""
        await self.welcome.set_welcome(update, context)

    @admin_only()
    @group_only()
    async def toggle_welcome(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Toggle welcome messages"""
        await self.welcome.toggle_welcome(update, context)

    async def handle_welcome_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle callback queries from welcome message buttons"""
        try:
            await self.welcome.handle_callback(update, context)
        except Exception as e:
            logger.error(f"Error in handle_welcome_callback: {str(e)}")
            logger.error(traceback.format_exc())
            if update.callback_query:
                await update.callback_query.answer("An error occurred. Please try again later.")

    @admin_only()
    @group_only()
    async def welcome_stats(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show welcome message statistics"""
        await self.welcome.welcome_stats(update, context)

    @admin_only()
    @group_only()
    async def set_goodbye(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Set goodbye message"""
        await self.welcome.set_goodbye(update, context)

    # Rules Commands 📜
    @admin_only()
    @group_only()
    async def set_rules(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Set group rules"""
        await self.rules.set_rules(update, context)

    async def show_rules(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show group rules"""
        await self.rules.show_rules(update, context)

    @admin_only()
    @group_only()
    async def settings_panel(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Display comprehensive group settings panel"""
        chat_id = update.effective_chat.id
        settings = await self.db.get_chat_settings(chat_id) or {}
        
        # Create settings keyboard
        keyboard = [
            [
                InlineKeyboardButton("🛡️ Protection", callback_data="settings_protection"),
                InlineKeyboardButton("👋 Welcome System", callback_data="settings_welcome")
            ],
            [
                InlineKeyboardButton("📜 Rules & Moderation", callback_data="settings_rules"),
                InlineKeyboardButton("🌊 Anti-Flood", callback_data="settings_flood")
            ],
            [
                InlineKeyboardButton("🔞 NSFW Filter", callback_data="settings_nsfw"),
                InlineKeyboardButton("🤖 AI Features", callback_data="settings_ai")
            ],
            [
                InlineKeyboardButton("📊 Analytics", callback_data="settings_analytics"),
                InlineKeyboardButton("🔧 Advanced", callback_data="settings_advanced")
            ],
            [
                InlineKeyboardButton("🔄 Refresh Panel", callback_data="settings_refresh")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        # Get current protection status
        nsfw_enabled = settings.get('nsfw_filter_enabled', True)
        welcome_enabled = settings.get('welcome_enabled', True)
        antiflood_enabled = settings.get('antiflood_enabled', True)
        captcha_enabled = settings.get('captcha_enabled', False)
        
        settings_text = (
            f"╭─「 <b>⚙️ Group Settings Panel</b> 」\n"
            f"│\n"
            f"│ 🎯 <b>Quick Status Overview</b>\n"
            f"│\n"
            f"├─「 <b>🛡️ Protection Systems</b> 」\n"
            f"│ 🔞 NSFW Filter: {'✅ Active' if nsfw_enabled else '❌ Disabled'}\n"
            f"│ 🌊 Anti-Flood: {'✅ Active' if antiflood_enabled else '❌ Disabled'}\n"
            f"│ 🤖 Captcha: {'✅ Active' if captcha_enabled else '❌ Disabled'}\n"
            f"│ 🛡️ AI Moderation: ✅ Always Active\n"
            f"│\n"
            f"├─「 <b>👋 User Experience</b> 」\n"
            f"│ 🎉 Welcome Messages: {'✅ Enabled' if welcome_enabled else '❌ Disabled'}\n"
            f"│ 📜 Rules System: ✅ Configured\n"
            f"│ 🔗 Auto-Cleanup: ✅ 30s Timer\n"
            f"│\n"
            f"├─「 <b>📊 Group Info</b> 」\n"
            f"│ 🏷️ Chat ID: <code>{chat_id}</code>\n"
            f"│ 👥 Members: <b>{await context.bot.get_chat_member_count(chat_id)}</b>\n"
            f"│ 🤖 Bot Status: <b>Fully Operational</b>\n"
            f"│\n"
            f"╰─「 <i>Select category to configure</i> 」"
        )
        
        message = await update.effective_message.reply_text(
            settings_text,
            parse_mode=ParseMode.HTML,
            reply_markup=reply_markup,
            disable_web_page_preview=True
        )
        
        # Auto-delete after 5 minutes
        asyncio.create_task(self._delete_after_delay(message, 300))

    async def handle_settings_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle settings panel callbacks"""
        query = update.callback_query
        await query.answer()
        
        action = query.data.replace("settings_", "")
        
        if action == "protection":
            await self._show_protection_settings(query, context)
        elif action == "welcome":
            await self._show_welcome_settings(query, context)
        elif action == "rules":
            await self._show_rules_settings(query, context)
        elif action == "nsfw":
            await self._show_nsfw_settings(query, context)
        elif action == "refresh":
            # Recreate the main settings panel
            chat_id = query.message.chat.id
            settings = await self.db.get_chat_settings(chat_id) or {}
            
            keyboard = [
                [
                    InlineKeyboardButton("🛡️ Protection", callback_data="settings_protection"),
                    InlineKeyboardButton("👋 Welcome System", callback_data="settings_welcome")
                ],
                [
                    InlineKeyboardButton("📜 Rules & Moderation", callback_data="settings_rules"),
                    InlineKeyboardButton("🌊 Anti-Flood", callback_data="settings_flood")
                ],
                [
                    InlineKeyboardButton("🔞 NSFW Filter", callback_data="settings_nsfw"),
                    InlineKeyboardButton("🤖 AI Features", callback_data="settings_ai")
                ],
                [
                    InlineKeyboardButton("📊 Analytics", callback_data="settings_analytics"),
                    InlineKeyboardButton("🔧 Advanced", callback_data="settings_advanced")
                ],
                [
                    InlineKeyboardButton("🔄 Refresh Panel", callback_data="settings_refresh")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            nsfw_enabled = settings.get('nsfw_filter_enabled', True)
            welcome_enabled = settings.get('welcome_enabled', True)
            antiflood_enabled = settings.get('antiflood_enabled', True)
            captcha_enabled = settings.get('captcha_enabled', False)
            
            settings_text = (
                f"╭─「 <b>⚙️ Group Settings Panel</b> 」\n"
                f"│\n"
                f"│ 🎯 <b>Quick Status Overview</b>\n"
                f"│\n"
                f"├─「 <b>🛡️ Protection Systems</b> 」\n"
                f"│ 🔞 NSFW Filter: {'✅ Active' if nsfw_enabled else '❌ Disabled'}\n"
                f"│ 🌊 Anti-Flood: {'✅ Active' if antiflood_enabled else '❌ Disabled'}\n"
                f"│ 🤖 Captcha: {'✅ Active' if captcha_enabled else '❌ Disabled'}\n"
                f"│ 🛡️ AI Moderation: ✅ Always Active\n"
                f"│\n"
                f"├─「 <b>👋 User Experience</b> 」\n"
                f"│ 🎉 Welcome Messages: {'✅ Enabled' if welcome_enabled else '❌ Disabled'}\n"
                f"│ 📜 Rules System: ✅ Configured\n"
                f"│ 🔗 Auto-Cleanup: ✅ 30s Timer\n"
                f"│\n"
                f"├─「 <b>📊 Group Info</b> 」\n"
                f"│ 🏷️ Chat ID: <code>{chat_id}</code>\n"
                f"│ 👥 Members: <b>{await context.bot.get_chat_member_count(chat_id)}</b>\n"
                f"│ 🤖 Bot Status: <b>Fully Operational</b>\n"
                f"│\n"
                f"╰─「 <i>Select category to configure</i> 」"
            )
            
            await query.edit_message_text(settings_text, parse_mode=ParseMode.HTML, reply_markup=reply_markup)

    async def _show_nsfw_settings(self, query, context):
        """Show NSFW filter settings"""
        chat_id = query.message.chat.id
        settings = await self.db.get_chat_settings(chat_id) or {}
        current_state = settings.get('nsfw_filter_enabled', True)
        
        keyboard = [
            [
                InlineKeyboardButton(f"{'🔴 Disable' if current_state else '🟢 Enable'} NSFW Filter", 
                                   callback_data=f"nsfw_toggle_{chat_id}"),
                InlineKeyboardButton("📊 View Stats", callback_data=f"nsfw_stats_{chat_id}")
            ],
            [
                InlineKeyboardButton("🔧 Advanced Config", callback_data=f"nsfw_advanced_{chat_id}"),
                InlineKeyboardButton("🧪 Test Filter", callback_data=f"nsfw_test_{chat_id}")
            ],
            [InlineKeyboardButton("◀ Back to Settings", callback_data="settings_refresh")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        text = (
            f"╭─「 <b>🔞 NSFW Filter Configuration</b> 」\n"
            f"│\n"
            f"├─「 <b>📊 Current Status</b> 」\n"
            f"│ Status: {'🟢 Active' if current_state else '🔴 Disabled'}\n"
            f"│ Detection: 3-Layer AI System\n"
            f"│ Response: Auto-delete + Admin alert\n"
            f"│ Coverage: Text + Images + Context\n"
            f"│\n"
            f"├─「 <b>🛡️ Protection Features</b> 」\n"
            f"│ ✅ 10k+ multilingual word database\n"
            f"│ ✅ TypeGPT API moderation\n"
            f"│ ✅ AI context analysis\n"
            f"│ ✅ OCR text extraction from images\n"
            f"│ ✅ Smart false-positive detection\n"
            f"│\n"
            f"├─「 <b>⚙️ Configuration</b> 」\n"
            f"│ Use buttons below to configure settings\n"
            f"│ Test functionality before deployment\n"
            f"│\n"
            f"╰─「 <i>Professional content moderation</i> 」"
        )
        
        await query.edit_message_text(text, parse_mode=ParseMode.HTML, reply_markup=reply_markup)

    async def _delete_after_delay(self, message, delay: int) -> None:
        """Delete a message after a specified delay in seconds."""
        if message:
            await asyncio.sleep(delay)
            try:
                await message.delete()
            except Exception as e:
                logger.debug(f"Failed to delete message: {e}")

    # Anti-flood Commands 🌊
    @admin_only()
    @group_only()
    async def set_antiflood(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Configure anti-flood settings"""
        await self.antiflood.set_antiflood(update, context)

    # Callback Handler
    async def handle_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle button callbacks"""
        query = update.callback_query
        if not query:
            return

        try:
            data = query.data
            chat_id = update.effective_chat.id
            
            if data == "show_rules":
                await self.show_rules(update, context)
            elif data == "show_settings":
                settings = await self.db.get_chat_settings(chat_id) or {}
                text = (
                    "<b>🛡️ Group Protection Settings</b>\n\n"
                    f"• {'✅' if settings.get('link_filter_enabled', False) else '❌'} Link Filter\n"
                    f"• {'✅' if settings.get('media_filter_enabled', False) else '❌'} Media Filter\n"
                    f"• {'✅' if settings.get('nsfw_filter_enabled', False) else '❌'} NSFW Filter\n"
                    f"• {'✅' if settings.get('restrict_new_users', False) else '❌'} Restrict New\n\n"
                    "<i>Available commands:</i>\n"
                    "• /nsfw on/off - Master protection control\n"
                    "• /nsfw links on/off - Toggle link filter\n"
                    "• /nsfw media on/off - Toggle media filter\n"
                    "• /nsfw text on/off - Toggle text NSFW filter\n"
                    "• /nsfw ai on/off - Toggle AI moderation"
                )
                await query.message.edit_text(text, parse_mode=ParseMode.HTML)
            
            await query.answer()
            
        except Exception as e:
            logger.error(f"Error in handle_callback: {str(e)}")
            logger.error(traceback.format_exc())
            await query.answer("❌ Error processing callback!")
