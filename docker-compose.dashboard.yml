version: '3.8'

services:
  dashboard-frontend:
    build:
      context: ./dashboard
      dockerfile: Dockerfile.frontend
    ports:
      - "3000:3000"
    volumes:
      - ./dashboard:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
    depends_on:
      - dashboard-backend

  dashboard-backend:
    build:
      context: ./dashboard
      dockerfile: Dockerfile.backend
    ports:
      - "5000:5000"
    volumes:
      - ./dashboard/backend:/app
      - ./data:/app/data
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - JWT_SECRET=haibot_dashboard_secret_key
    depends_on:
      - db

  db:
    image: sqlite:latest
    volumes:
      - ./data:/data
    environment:
      - SQLITE_DATABASE=bot_database.db

volumes:
  dashboard-data: