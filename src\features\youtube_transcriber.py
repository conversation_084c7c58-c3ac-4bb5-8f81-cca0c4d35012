from typing import Dict, List, Optional
from .transcriber import YTTranscriber
from ..api_services.pollinations_ai import PollinationsAI
import json
import os
import tempfile
from datetime import datetime
import asyncio

class YouTubeTranscriber:
    def __init__(self):
        self.model = "openai-large"

    async def get_transcript(self, video_url: str) -> List[Dict]:
        """Get transcript from YouTube video"""
        try:
            # Validate video URL format
            if not video_url or not isinstance(video_url, str):
                raise ValueError("Invalid YouTube URL format")

            # Attempt to get transcript
            transcript = YTTranscriber.get_transcript(video_url, languages=None)
            
            # Check if transcript is None or empty
            if not transcript:
                raise ValueError("No transcript available for this video. The video might be unavailable, have no captions, or require authentication.")
                
            return transcript
        except Exception as e:
            # Handle specific error cases
            error_msg = str(e)
            if "not iterable" in error_msg:
                error_msg = "No transcript available for this video. The video might be unavailable, have no captions, or require authentication."
            elif "Video unavailable" in error_msg:
                error_msg = "The video is unavailable or may have been removed."
            elif "could not find the specified video" in error_msg:
                error_msg = "Could not find the video. Please check if the URL is correct."
            
            raise ValueError(f"Failed to get transcript: {error_msg}")

    async def save_raw_transcript(self, video_url: str, transcript: List[Dict]) -> str:
        """Save raw transcript to a temporary JSON file"""
        try:
            # Extract video ID from URL (assuming standard YouTube URL format)
            video_id = video_url.split('/')[-1] if '/' in video_url else video_url
            if 'v=' in video_id:
                video_id = video_id.split('v=')[1].split('&')[0]

            # Create a temporary file with a custom suffix
            with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix=f'_{video_id}.json', encoding='utf-8') as temp_file:
                # Save transcript to temp file
                json.dump(transcript, temp_file, ensure_ascii=False, indent=2)
                filepath = temp_file.name

            # Schedule file deletion after 5 minutes
            async def delete_file():
                await asyncio.sleep(300)  # 5 minutes
                try:
                    os.remove(filepath)
                except Exception:
                    pass

            asyncio.create_task(delete_file())
            return filepath
        except Exception as e:
            raise ValueError(f"Failed to save transcript: {str(e)}")

    async def analyze_transcript(self, transcript: List[Dict], analysis_type: str = "summary") -> str:
        """Analyze transcript using AI"""
        # Combine all transcript segments into one text
        full_text = " ".join([segment["text"] for segment in transcript])

        # Prepare prompt based on analysis type
        if analysis_type == "summary":
            prompt = f"Please provide a concise summary of this video transcript: {full_text}"
        elif analysis_type == "key_points":
            prompt = f"Extract the main key points from this video transcript: {full_text}"
        else:
            prompt = f"Analyze this video transcript and provide insights: {full_text}"

        try:
            messages = [
                {"role": "system", "content": """
You are a specialized AI assistant for analyzing YouTube video transcripts. Your responses must:

1. FORMAT:
- Use Telegram HTML formatting tags only (<b>, <i>, <code>, <pre>)
- Properly escape special characters (<, >, &)
- Keep paragraphs short and readable
- Use bullet points with • for lists

2. STRUCTURE:
- Start with a clear, bold title
- Organize content into logical sections
- Use appropriate emphasis for key points
- End with a brief conclusion

3. CONTENT GUIDELINES:
- Be concise and informative
- Focus on the most important information
- Use timestamps when relevant [HH:MM:SS]
- Maintain professional tone
- Avoid repetition

4. LENGTH:
- Keep responses under 4000 characters
- Use bullet points for efficiency
- Break long paragraphs into smaller ones

Respond in a way that's both informative and easy to read in Telegram."""},
                {"role": "user", "content": prompt}
            ]

            response = PollinationsAI.generate_text(
                model=self.model,
                messages=messages,
                temperature=0.7
            )
            return response
        except Exception as e:
            raise ValueError(f"Failed to analyze transcript: {str(e)}")

    async def process_video(self, video_url: str, analysis_type: str = "summary") -> Dict[str, str]:
        """Process YouTube video - get transcript and analyze it"""
        try:
            # Get transcript
            transcript = await self.get_transcript(video_url)
            
            # Handle raw transcript request
            if analysis_type == "raw":
                filepath = await self.save_raw_transcript(video_url, transcript)
                return {
                    "status": "success",
                    "transcript": transcript,
                    "analysis": f"Raw transcript saved to: {filepath}",
                    "file_path": filepath
                }
            
            # Analyze transcript
            analysis = await self.analyze_transcript(transcript, analysis_type)
            
            return {
                "status": "success",
                "transcript": transcript,
                "analysis": analysis
            }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
