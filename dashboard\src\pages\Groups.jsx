import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Button,
  TextField,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Grid,
  CircularProgress,
  Alert
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import SearchIcon from '@mui/icons-material/Search';
import { groupsAPI } from '../services/api';

export default function Groups() {
  const [groups, setGroups] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [search, setSearch] = useState('');
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    protection: false,
    welcome: false,
    floodProtection: false,
    nsfwFilter: false
  });

  useEffect(() => {
    fetchGroups();
  }, []);

  const fetchGroups = async () => {
    try {
      setLoading(true);
      const response = await groupsAPI.getAll();
      setGroups(response.data);
      setLoading(false);
    } catch (err) {
      setError('Failed to fetch groups data');
      setLoading(false);
      console.error('Error fetching groups:', err);
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearchChange = (event) => {
    setSearch(event.target.value);
    setPage(0);
  };

  const handleEditGroup = (group) => {
    setSelectedGroup(group);
    setFormData({
      name: group.name,
      protection: group.protection,
      welcome: group.welcome,
      floodProtection: group.floodProtection || false,
      nsfwFilter: false // Not available in current data
    });
    setOpenDialog(true);
  };

  const handleDeleteGroup = (groupId) => {
    // In a real implementation, this would make an API call to delete the group
    console.log('Delete group:', groupId);
    // For now, we'll just show an alert
    alert('Group deletion would be implemented in a real application');
  };

  const handleAddGroup = () => {
    setSelectedGroup(null);
    setFormData({
      name: '',
      protection: false,
      welcome: false,
      floodProtection: false,
      nsfwFilter: false
    });
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedGroup(null);
  };

  const handleSaveGroup = () => {
    if (selectedGroup) {
      // Update existing group
      // In a real implementation, this would make an API call to update the group
      console.log('Update group:', selectedGroup.id, formData);
    } else {
      // Add new group
      // In a real implementation, this would make an API call to create the group
      console.log('Add new group:', formData);
    }
    handleCloseDialog();
    // Refresh the data
    fetchGroups();
  };

  const handleFormChange = (event) => {
    const { name, value, type, checked } = event.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const filteredGroups = groups.filter(group =>
    group.name.toLowerCase().includes(search.toLowerCase())
  );

  const paginatedGroups = filteredGroups.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Typography variant="h1" gutterBottom>
        Group Management
      </Typography>
      
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <TextField
            variant="outlined"
            placeholder="Search groups..."
            value={search}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: <SearchIcon />,
            }}
          />
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddGroup}
          >
            Add Group
          </Button>
        </Box>
        
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Group Name</TableCell>
                <TableCell>Members</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Protection</TableCell>
                <TableCell>Welcome</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {paginatedGroups.map((group) => (
                <TableRow key={group.id}>
                  <TableCell>{group.name}</TableCell>
                  <TableCell>{group.members.toLocaleString()}</TableCell>
                  <TableCell>
                    <Chip 
                      label={group.status} 
                      color={
                        group.status === 'active' ? 'success' : 
                        group.status === 'inactive' ? 'default' : 'error'
                      } 
                    />
                  </TableCell>
                  <TableCell>
                    <Switch 
                      checked={group.protection} 
                      color="primary" 
                      disabled
                    />
                  </TableCell>
                  <TableCell>
                    <Switch 
                      checked={group.welcome} 
                      color="primary" 
                      disabled
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton onClick={() => handleEditGroup(group)}>
                      <EditIcon />
                    </IconButton>
                    <IconButton onClick={() => handleDeleteGroup(group.id)}>
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={filteredGroups.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>
      
      {/* Group Edit/Add Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {selectedGroup ? 'Edit Group' : 'Add New Group'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Group Name"
                name="name"
                value={formData.name}
                onChange={handleFormChange}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.protection}
                    onChange={handleFormChange}
                    name="protection"
                  />
                }
                label="Enable Protection"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.welcome}
                    onChange={handleFormChange}
                    name="welcome"
                  />
                }
                label="Enable Welcome Messages"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.floodProtection}
                    onChange={handleFormChange}
                    name="floodProtection"
                  />
                }
                label="Enable Flood Protection"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.nsfwFilter}
                    onChange={handleFormChange}
                    name="nsfwFilter"
                  />
                }
                label="Enable NSFW Filter"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSaveGroup} variant="contained">
            {selectedGroup ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}