"""
Database Module
-------------
Handles database operations with MongoDB Atlas.
"""

import os
from typing import Optional, Dict, List, Any
from datetime import datetime, timedelta
import logging
import json
import traceback
from .mongodb import MongoDBDatabase

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class Database:
    def __init__(self):
        """Initialize database connection"""
        self.mongodb = MongoDBDatabase()
        # Keep memory storage for compatibility
        self.memory_storage = self.mongodb.memory_storage
        self.default_settings = self.mongodb.default_settings

    def _create_tables(self):
        """Create necessary database tables based on schema.sql"""
        try:
            schema_path = os.path.join(os.path.dirname(__file__), "schema.sql")
            with open(schema_path, "r", encoding="utf-8") as f:
                schema_sql = f.read()
            with sqlite3.connect(self.db_path) as conn:
                conn.executescript(schema_sql)
                conn.commit()
            logger.info("Database tables created from schema.sql successfully")
        except Exception as e:
            logger.error(f"Error creating tables from schema.sql: {e}")

    def setup_database(self):
        """Initialize database tables"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Create necessary tables
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS group_settings (
                    chat_id INTEGER PRIMARY KEY,
                    welcome_enabled BOOLEAN DEFAULT TRUE,
                    welcome_message TEXT DEFAULT NULL,
                    rules TEXT DEFAULT NULL,
                    language TEXT DEFAULT 'en',
                    max_warns INTEGER DEFAULT 5,
                    antiflood_enabled BOOLEAN DEFAULT TRUE,
                    antiflood_limit INTEGER DEFAULT 5,
                    antiflood_time INTEGER DEFAULT 30,
                    autodelete_commands BOOLEAN DEFAULT TRUE,
                    restrict_new_users BOOLEAN DEFAULT TRUE
                )
                """)

                cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    user_id INTEGER PRIMARY KEY,
                    username TEXT,
                    first_name TEXT,
                    last_name TEXT,
                    language_code TEXT,
                    is_bot BOOLEAN DEFAULT FALSE,
                    data TEXT DEFAULT '{}',
                    first_seen TEXT,
                    last_active TEXT
                )
                """)

                cursor.execute("""
                CREATE TABLE IF NOT EXISTS warnings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    chat_id INTEGER NOT NULL,
                    user_id INTEGER NOT NULL,
                    admin_id INTEGER NOT NULL,
                    reason TEXT,
                    timestamp TEXT NOT NULL
                )
                """)

                cursor.execute("""
                CREATE TABLE IF NOT EXISTS notes (
                    chat_id INTEGER NOT NULL,
                    note_name TEXT NOT NULL,
                    content TEXT NOT NULL,
                    PRIMARY KEY (chat_id, note_name)
                )
                """)

                cursor.execute("""
                CREATE TABLE IF NOT EXISTS filters (
                    chat_id INTEGER NOT NULL,
                    filter_name TEXT NOT NULL,
                    filter_data TEXT NOT NULL,
                    PRIMARY KEY (chat_id, filter_name)
                )
                """)

                cursor.execute("""
                CREATE TABLE IF NOT EXISTS admin_actions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    chat_id INTEGER NOT NULL,
                    admin_id INTEGER NOT NULL,
                    action TEXT NOT NULL,
                    target_user_id INTEGER,
                    reason TEXT,
                    timestamp TEXT NOT NULL
                )
                """)

                cursor.execute("""
                CREATE TABLE IF NOT EXISTS appeals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    chat_id INTEGER NOT NULL,
                    message TEXT NOT NULL,
                    status TEXT NOT NULL,
                    admin_id INTEGER,
                    decision TEXT,
                    decision_reason TEXT,
                    timestamp TEXT NOT NULL,
                    decision_timestamp TEXT
                )
                """)

                # Add new tables for user statistics
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_activity (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    chat_id INTEGER NOT NULL,
                    chat_type TEXT NOT NULL,
                    activity_type TEXT NOT NULL,
                    timestamp TEXT NOT NULL
                )
                """)

                cursor.execute("""
                CREATE TABLE IF NOT EXISTS command_stats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    command TEXT NOT NULL,
                    user_id INTEGER NOT NULL,
                    chat_id INTEGER NOT NULL,
                    chat_type TEXT NOT NULL,
                    timestamp TEXT NOT NULL
                )
                """)

                conn.commit()
                logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Error creating tables: {e}")

    def connect(self):
        """Establish database connection"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.execute("PRAGMA foreign_keys = ON")
            conn.commit()
            return conn
        except Exception as e:
            logger.error(f"Error connecting to database: {e}")
            raise

    def is_connected(self) -> bool:
        """Check if database is connected"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("SELECT 1")
                return True
        except:
            return False

    # Warning management
    async def add_warning(self, chat_id: int, user_id: int, admin_id: int, reason: str = None) -> int:
        """Add warning to user and return total warnings"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                INSERT INTO warnings (chat_id, user_id, admin_id, reason, timestamp)
                VALUES (?, ?, ?, ?, ?)
                """, (chat_id, user_id, admin_id, reason, datetime.now().isoformat()))
                conn.commit()

                # Get updated warning count
                cursor.execute("""
                SELECT COUNT(*) FROM warnings
                WHERE chat_id = ? AND user_id = ?
                """, (chat_id, user_id))
                return cursor.fetchone()[0]
        except Exception as e:
            logger.error(f"Error adding warning: {e}")
            return 0

    # Alias for backward compatibility
    async def add_warning_sqlite(self, chat_id: int, user_id: int, admin_id: int, reason: str = None) -> int:
        """Alias for add_warning - for backward compatibility"""
        return await self.add_warning(chat_id, user_id, admin_id, reason)

    async def log_admin_action(self, chat_id: int, admin_id: int, action: str, target_user_id: int, reason: str = None):
        """Log an admin action"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                INSERT INTO admin_actions (chat_id, admin_id, action, target_user_id, reason, timestamp)
                VALUES (?, ?, ?, ?, ?, ?)
                """, (chat_id, admin_id, action, target_user_id, reason, datetime.now().isoformat()))
                conn.commit()
        except Exception as e:
            logger.error(f"Error logging admin action: {e}")

    async def get_user_warn_count(self, chat_id: int, user_id: int) -> int:
        """Get the number of warnings for a user in a chat"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                SELECT COUNT(*) FROM warnings
                WHERE chat_id = ? AND user_id = ?
                """, (chat_id, user_id))
                return cursor.fetchone()[0]
        except Exception as e:
            logger.error(f"Error getting warning count: {e}")
            return 0

    # Alias for backward compatibility
    async def get_warnings(self, chat_id: int, user_id: int) -> int:
        """Alias for get_user_warn_count - for backward compatibility"""
        return await self.get_user_warn_count(chat_id, user_id)



    async def remove_warning(self, chat_id: int, user_id: int) -> bool:
        """Remove the latest warning from a user"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Get the latest warning ID
                cursor.execute("""
                SELECT id FROM warnings
                WHERE chat_id = ? AND user_id = ?
                ORDER BY timestamp DESC LIMIT 1
                """, (chat_id, user_id))

                result = cursor.fetchone()
                if not result:
                    return False

                # Delete the warning
                cursor.execute("""
                DELETE FROM warnings
                WHERE id = ?
                """, (result[0],))

                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error removing warning: {e}")
            return False

    async def get_user_warnings(self, chat_id: int, user_id: int) -> List[Dict]:
        """Get all warnings for a user in a chat"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Create warnings table if it doesn't exist
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS warnings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    chat_id INTEGER,
                    user_id INTEGER,
                    admin_id INTEGER,
                    reason TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """)
                conn.commit()
                
                cursor.execute("""
                SELECT w.*, u.first_name as admin_name
                FROM warnings w
                LEFT JOIN users u ON w.admin_id = u.user_id
                WHERE w.chat_id = ? AND w.user_id = ?
                ORDER BY w.timestamp DESC
                """, (chat_id, user_id))

                warnings = []
                for row in cursor.fetchall():
                    warnings.append({
                        'id': row[0],
                        'reason': row[4],
                        'timestamp': row[5],
                        'admin_id': row[3],
                        'admin_name': row[6] if row[6] else "Unknown"
                    })
                return warnings
        except Exception as e:
            logger.error(f"Error getting user warnings: {e}")
            return []

    async def clear_warnings(self, chat_id: int, user_id: int) -> bool:
        """Clear all warnings for a user in a chat"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                DELETE FROM warnings
                WHERE chat_id = ? AND user_id = ?
                """, (chat_id, user_id))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error clearing warnings: {e}")
            return False

    async def add_appeal(self, user_id: int, chat_id: int, message: str) -> int:
        """Add a new appeal to the database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                INSERT INTO appeals (user_id, chat_id, message, status, timestamp)
                VALUES (?, ?, ?, 'pending', ?)
                """, (user_id, chat_id, message, datetime.now().isoformat()))
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            logger.error(f"Error adding appeal: {e}")
            return None

    async def update_appeal_status(self, appeal_id: int, status: str, admin_id: int, decision: str = None, reason: str = None) -> bool:
        """Update the status of an appeal"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                UPDATE appeals
                SET status = ?, admin_id = ?, decision = ?, decision_reason = ?, decision_timestamp = ?
                WHERE id = ?
                """, (status, admin_id, decision, reason, datetime.now().isoformat(), appeal_id))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error updating appeal: {e}")
            return False

    async def get_appeal(self, appeal_id: int) -> Dict:
        """Get appeal details by ID"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                SELECT * FROM appeals WHERE id = ?
                """, (appeal_id,))
                result = cursor.fetchone()
                if result:
                    return {
                        'id': result[0],
                        'user_id': result[1],
                        'chat_id': result[2],
                        'message': result[3],
                        'status': result[4],
                        'admin_id': result[5],
                        'timestamp': result[6],
                        'decision': result[7],
                        'decision_reason': result[8],
                        'decision_timestamp': result[9]
                    }
                return None
        except Exception as e:
            logger.error(f"Error getting appeal: {e}")
            return None

    async def get_user_appeals(self, user_id: int, chat_id: int = None) -> List[Dict]:
        """Get all appeals by a user, optionally filtered by chat"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                if chat_id:
                    cursor.execute("""
                    SELECT * FROM appeals
                    WHERE user_id = ? AND chat_id = ?
                    ORDER BY timestamp DESC
                    """, (user_id, chat_id))
                else:
                    cursor.execute("""
                    SELECT * FROM appeals
                    WHERE user_id = ?
                    ORDER BY timestamp DESC
                    """, (user_id,))

                results = cursor.fetchall()
                return [{
                    'id': row[0],
                    'user_id': row[1],
                    'chat_id': row[2],
                    'message': row[3],
                    'status': row[4],
                    'admin_id': row[5],
                    'timestamp': row[6],
                    'decision': row[7],
                    'decision_reason': row[8],
                    'decision_timestamp': row[9]
                } for row in results]
        except Exception as e:
            logger.error(f"Error getting user appeals: {e}")
            return []

    # Chat settings
    async def get_group_setting(self, chat_id: int, setting: str, default_value: Any = None) -> Any:
        """Get a group setting"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Ensure chat exists
                cursor.execute(
                    "INSERT OR IGNORE INTO group_settings (chat_id) VALUES (?)",
                    (chat_id,)
                )
                conn.commit()

                # Special handling for nsfw_filter_enabled
                if setting == 'nsfw_filter_enabled':
                    # Check if the setting exists
                    cursor.execute("PRAGMA table_info(group_settings)")
                    columns = [row[1] for row in cursor.fetchall()]

                    if 'nsfw_filter_enabled' not in columns:
                        # Add the column
                        cursor.execute("ALTER TABLE group_settings ADD COLUMN nsfw_filter_enabled INTEGER")
                        conn.commit()

                        # Set the default value
                        db_value = 1 if default_value else 0
                        cursor.execute(
                            "UPDATE group_settings SET nsfw_filter_enabled = ? WHERE chat_id = ?",
                            (db_value, chat_id)
                        )
                        conn.commit()
                        logger.info(f"Created nsfw_filter_enabled column with default value: {default_value}")
                        return default_value

                    # Get the current value
                    cursor.execute(
                        "SELECT nsfw_filter_enabled FROM group_settings WHERE chat_id = ?",
                        (chat_id,)
                    )
                    result = cursor.fetchone()

                    if result is None or result[0] is None:
                        # Set the default value
                        db_value = 1 if default_value else 0
                        cursor.execute(
                            "UPDATE group_settings SET nsfw_filter_enabled = ? WHERE chat_id = ?",
                            (db_value, chat_id)
                        )
                        conn.commit()
                        logger.info(f"Set nsfw_filter_enabled to default value: {default_value}")
                        return default_value

                    # Convert the stored value to boolean
                    value = result[0]
                    bool_value = value == 1
                    logger.info(f"Retrieved nsfw_filter_enabled: {value} -> {bool_value}")
                    return bool_value

                # Standard handling for other settings
                # Check if column exists
                cursor.execute("PRAGMA table_info(group_settings)")
                columns = [row[1] for row in cursor.fetchall()]

                if setting not in columns:
                    # Add column with appropriate type
                    col_type = "INTEGER" if isinstance(default_value, bool) else "TEXT"
                    cursor.execute(f"ALTER TABLE group_settings ADD COLUMN {setting} {col_type}")
                    conn.commit()

                    # After creating column, set default value
                    db_value = None
                    if isinstance(default_value, bool):
                        db_value = 1 if default_value else 0
                    elif isinstance(default_value, (list, dict)):
                        db_value = json.dumps(default_value)
                    else:
                        db_value = default_value

                    cursor.execute(
                        f"UPDATE group_settings SET {setting} = ? WHERE chat_id = ?",
                        (db_value, chat_id)
                    )
                    conn.commit()
                    return default_value

                # Get setting value
                cursor.execute(
                    f"SELECT {setting} FROM group_settings WHERE chat_id = ?",
                    (chat_id,)
                )
                result = cursor.fetchone()

                if result is None or result[0] is None:
                    # Set default value if not found
                    db_value = None
                    if isinstance(default_value, bool):
                        db_value = 1 if default_value else 0
                    elif isinstance(default_value, (list, dict)):
                        db_value = json.dumps(default_value)
                    else:
                        db_value = default_value

                    cursor.execute(
                        f"UPDATE group_settings SET {setting} = ? WHERE chat_id = ?",
                        (db_value, chat_id)
                    )
                    conn.commit()
                    return default_value

                value = result[0]
                # Convert stored value back to appropriate type
                if isinstance(default_value, bool):
                    return value == 1
                elif isinstance(default_value, (list, dict)):
                    try:
                        return json.loads(value) if value else default_value
                    except (json.JSONDecodeError, TypeError):
                        return default_value
                return value

        except Exception as e:
            logger.error(f"Error getting group setting: {str(e)}")
            logger.error(traceback.format_exc())
            return default_value

    async def update_chat_settings(self, chat_id: int, settings: Dict):
        """Update chat settings"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                INSERT OR REPLACE INTO settings (chat_id, settings)
                VALUES (?, ?)
                """, (chat_id, json.dumps(settings)))
                conn.commit()
        except Exception as e:
            logger.error(f"Error updating chat settings: {e}")

    # Notes management
    async def save_note(self, chat_id: int, note_name: str, content: str):
        """Save a note"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                INSERT OR REPLACE INTO notes (chat_id, note_name, content)
                VALUES (?, ?, ?)
                """, (chat_id, note_name, content))
                conn.commit()
        except Exception as e:
            logger.error(f"Error saving note: {e}")

    async def get_note(self, chat_id: int, note_name: str) -> Optional[str]:
        """Get a note"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                SELECT content FROM notes
                WHERE chat_id = ? AND note_name = ?
                """, (chat_id, note_name))
                result = cursor.fetchone()
                return result[0] if result else None
        except Exception as e:
            logger.error(f"Error getting note: {e}")
            return None

    # Rules management
    async def set_rules(self, chat_id: int, rules: str):
        """Set chat rules"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                INSERT OR REPLACE INTO rules (chat_id, rules)
                VALUES (?, ?)
                """, (chat_id, rules))
                conn.commit()
        except Exception as e:
            logger.error(f"Error setting rules: {e}")

    async def get_rules(self, chat_id: int) -> Optional[str]:
        """Get chat rules"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                SELECT rules FROM rules
                WHERE chat_id = ?
                """, (chat_id,))
                result = cursor.fetchone()
                return result[0] if result else None
        except Exception as e:
            logger.error(f"Error getting rules: {e}")
            return None

    async def set_group_setting(self, chat_id: int, setting: str, value: Any) -> bool:
        """Set a group setting"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Ensure chat exists
                cursor.execute(
                    "INSERT OR IGNORE INTO group_settings (chat_id) VALUES (?)",
                    (chat_id,)
                )

                # Check if column exists
                cursor.execute("PRAGMA table_info(group_settings)")
                columns = [row[1] for row in cursor.fetchall()]

                if setting not in columns:
                    # Add column with appropriate type
                    col_type = "INTEGER" if isinstance(value, bool) else "TEXT"
                    cursor.execute(f"ALTER TABLE group_settings ADD COLUMN {setting} {col_type}")

                # Convert value to appropriate storage format
                db_value = None
                if isinstance(value, bool):
                    db_value = 1 if value else 0
                elif isinstance(value, (list, dict)):
                    db_value = json.dumps(value)
                else:
                    db_value = value

                # Update setting
                cursor.execute(
                    f"UPDATE group_settings SET {setting} = ? WHERE chat_id = ?",
                    (db_value, chat_id)
                )
                conn.commit()

                # Verify the update
                cursor.execute(
                    f"SELECT {setting} FROM group_settings WHERE chat_id = ?",
                    (chat_id,)
                )
                result = cursor.fetchone()
                if result is None:
                    return False

                stored_value = result[0]

                # For debugging
                logger.info(f"Stored value for {setting}: {stored_value}, Original value: {value}, DB value: {db_value}")

                # Special handling for nsfw_filter_enabled
                if setting == 'nsfw_filter_enabled':
                    # Force a direct update to ensure it's saved correctly
                    cursor.execute(
                        "UPDATE group_settings SET nsfw_filter_enabled = ? WHERE chat_id = ?",
                        (db_value, chat_id)
                    )
                    conn.commit()

                    # Double-check the update
                    cursor.execute(
                        "SELECT nsfw_filter_enabled FROM group_settings WHERE chat_id = ?",
                        (chat_id,)
                    )
                    result = cursor.fetchone()
                    if result is not None:
                        logger.info(f"Double-checked nsfw_filter_enabled: {result[0]}")

                return True  # Always return True as verification is handled in get_group_setting

        except Exception as e:
            logger.error(f"Error setting group setting: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    async def get_welcome_message(self, chat_id: int) -> str:
        """Get welcome message for a chat, creating the column if missing"""
        try:
            # Use get_group_setting to ensure the column exists
            return await self.get_group_setting(chat_id, "welcome_message", None)
        except Exception as e:
            logger.error(f"Error getting welcome message: {e}")
            logger.error(traceback.format_exc())
            return None

    async def get_welcome_setting(self, chat_id: int) -> bool:
        """Get welcome enabled setting for a chat"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT welcome_enabled FROM group_settings WHERE chat_id = ?", (chat_id,))
                result = cursor.fetchone()
                return result[0] if result else True  # Default to True
        except Exception as e:
            logger.error(f"Error getting welcome setting: {e}")
            return True  # Default to True if there's an error

    async def set_welcome_message(self, chat_id: int, message: str) -> bool:
        """Set welcome message for a chat, creating the column if needed"""
        try:
            return await self.set_group_setting(chat_id, "welcome_message", message)
        except Exception as e:
            logger.error(f"Error setting welcome message: {e}")
            logger.error(traceback.format_exc())
            return False

    async def set_welcome_setting(self, chat_id: int, enabled: bool) -> bool:
        """Set welcome enabled setting for a chat"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO group_settings (chat_id, welcome_enabled)
                    VALUES (?, ?)
                    ON CONFLICT(chat_id)
                    DO UPDATE SET welcome_enabled = ?
                """, (chat_id, enabled, enabled))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error setting welcome enabled: {e}")
            return False

    async def get_group_rules(self, chat_id: int) -> str:
        """Get the rules for a group"""
        return await self.get_group_setting(chat_id, "rules", "DEFAULT_RULES")

    async def set_group_rules(self, chat_id: int, rules: str) -> bool:
        """Set the rules for a group"""
        return await self.set_group_setting(chat_id, "rules", rules)

    async def add_report(self, report_data: Dict[str, Any]) -> None:
        """Add a new report to the database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO reports
                    (chat_id, reporter_id, reported_user_id, message_id, reason, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    report_data['chat_id'],
                    report_data['reporter_id'],
                    report_data['reported_user_id'],
                    report_data['message_id'],
                    report_data['reason'],
                    report_data['timestamp']
                ))
                conn.commit()
        except Exception as e:
            logger.error(f"Error adding report: {e}")

    async def get_report_chat_id(self, message_id: int) -> Optional[int]:
        """Get chat ID for a report by message ID"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT chat_id FROM reports WHERE message_id = ?", (message_id,))
                result = cursor.fetchone()
                return result[0] if result else None
        except Exception as e:
            logger.error(f"Error getting report chat ID: {e}")
            return None





    
    async def track_user_activity(self, chat_id: int, user_id: int, activity_type: str) -> None:
        """Track user activity in a chat"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Insert activity
                cursor.execute("""
                    INSERT INTO user_activity (chat_id, user_id, activity_type)
                    VALUES (?, ?, ?)
                """, (chat_id, user_id, activity_type))

                # Insert or update user stats
                cursor.execute("""
                    INSERT INTO user_stats (
                        chat_id, user_id, message_count, command_count,
                        total_media, total_stickers, total_reactions,
                        last_active
                    )
                    VALUES (?, ?,
                        CASE WHEN ? = 'message' THEN 1 ELSE 0 END,
                        CASE WHEN ? LIKE '/%' THEN 1 ELSE 0 END,
                        CASE WHEN ? = 'media' THEN 1 ELSE 0 END,
                        CASE WHEN ? = 'sticker' THEN 1 ELSE 0 END,
                        CASE WHEN ? = 'reaction' THEN 1 ELSE 0 END,
                        CURRENT_TIMESTAMP
                    )
                    ON CONFLICT(chat_id, user_id) DO UPDATE SET
                        message_count = message_count + CASE WHEN ? = 'message' THEN 1 ELSE 0 END,
                        command_count = command_count + CASE WHEN ? LIKE '/%' THEN 1 ELSE 0 END,
                        total_media = total_media + CASE WHEN ? = 'media' THEN 1 ELSE 0 END,
                        total_stickers = total_stickers + CASE WHEN ? = 'sticker' THEN 1 ELSE 0 END,
                        total_reactions = total_reactions + CASE WHEN ? = 'reaction' THEN 1 ELSE 0 END,
                        last_active = CURRENT_TIMESTAMP
                """, (chat_id, user_id,
                      activity_type, activity_type, activity_type, activity_type, activity_type,
                      activity_type, activity_type, activity_type, activity_type, activity_type))

                conn.commit()
        except Exception as e:
            logger.error(f"Error tracking user activity: {e}")

    async def get_user_stats(self, chat_id: int, user_id: int) -> Dict[str, Any]:
        """Get user statistics for a chat"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Update weekly activity first
                cursor.execute("""
                    UPDATE user_stats
                    SET weekly_activity = (
                        SELECT COUNT(*)
                        FROM user_activity
                        WHERE user_activity.chat_id = user_stats.chat_id
                        AND user_activity.user_id = user_stats.user_id
                        AND user_activity.timestamp >= datetime('now', '-7 days')
                    ),
                    days_active = (
                        SELECT COUNT(DISTINCT date(timestamp))
                        FROM user_activity
                        WHERE user_activity.chat_id = user_stats.chat_id
                        AND user_activity.user_id = user_stats.user_id
                    )
                    WHERE chat_id = ? AND user_id = ?
                """, (chat_id, user_id))

                # Get updated stats
                cursor.execute("""
                    SELECT
                        message_count,
                        command_count,
                        first_seen,
                        last_active,
                        weekly_activity,
                        days_active,
                        total_reactions,
                        total_media,
                        total_stickers
                    FROM user_stats
                    WHERE chat_id = ? AND user_id = ?
                """, (chat_id, user_id))

                result = cursor.fetchone()
                if result:
                    return {
                        'message_count': result[0],
                        'command_count': result[1],
                        'first_seen': result[2],
                        'last_active': result[3],
                        'weekly_activity': result[4],
                        'days_active': result[5],
                        'total_reactions': result[6],
                        'total_media': result[7],
                        'total_stickers': result[8]
                    }
                return None
        except Exception as e:
            logger.error(f"Error getting user stats: {e}")
            return None

    async def update_user_weekly_activity(self) -> None:
        """Update weekly activity counts for all users"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE user_stats
                    SET weekly_activity = (
                        SELECT COUNT(*)
                        FROM user_activity
                        WHERE user_activity.chat_id = user_stats.chat_id
                        AND user_activity.user_id = user_stats.user_id
                        AND user_activity.timestamp >= datetime('now', '-7 days')
                    )
                """)
                conn.commit()
        except Exception as e:
            logger.error(f"Error updating weekly activity: {e}")

    async def update_days_active(self) -> None:
        """Update days active count for all users"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE user_stats
                    SET days_active = (
                        SELECT COUNT(DISTINCT date(timestamp))
                        FROM user_activity
                        WHERE user_activity.chat_id = user_stats.chat_id
                        AND user_activity.user_id = user_stats.user_id
                    )
                """)
                conn.commit()
        except Exception as e:
            logger.error(f"Error updating days active: {e}")

    async def get_goodbye_message(self, chat_id: int) -> str:
        """Get goodbye message for a chat"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT goodbye_message FROM group_settings WHERE chat_id = ?", (chat_id,))
                result = cursor.fetchone()
                return result[0] if result else None
        except Exception as e:
            logger.error(f"Error getting goodbye message: {e}")
            return None

    async def set_goodbye_message(self, chat_id: int, message: str) -> bool:
        """Set goodbye message for a chat"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO group_settings (chat_id, goodbye_message)
                    VALUES (?, ?)
                    ON CONFLICT(chat_id)
                    DO UPDATE SET goodbye_message = ?
                """, (chat_id, message, message))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error setting goodbye message: {e}")
            return False





    async def get_all_chats(self) -> List[int]:
        """Get all chat IDs where the bot is present."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                SELECT chat_id FROM group_settings
                """)
                chats = [row[0] for row in cursor.fetchall()]
                return chats
        except Exception as e:
            logger.error(f"Error getting all chats: {str(e)}")
            return []

    async def get_all_groups(self) -> List[Dict[str, Any]]:
        """Get all group chats for morning greetings."""
        query = """
        SELECT DISTINCT chat_id
        FROM group_settings
        WHERE chat_id < 0
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # Check if group_settings table exists
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='group_settings'")
                if not cursor.fetchone():
                    logger.warning("Group settings table does not exist")
                    return []

                cursor.execute(query)
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"Error getting all groups: {e}")
            return []

    async def add_chat(self, chat_id: int, title: str, chat_type: str, is_admin: bool = False) -> bool:
        """Add a chat to the database and ensure group settings exist."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Ensure the chat exists in group_settings
                cursor.execute(
                    "INSERT OR IGNORE INTO group_settings (chat_id) VALUES (?)",
                    (chat_id,)
                )

                joined_date = datetime.now().isoformat()
                cursor.execute(
                    """
                    INSERT OR REPLACE INTO chats (chat_id, title, chat_type, joined_date, is_admin)
                    VALUES (?, ?, ?, ?, ?)
                    """,
                    (chat_id, title, chat_type, joined_date, is_admin),
                )

                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error adding chat to database: {e}")
            return False

    async def remove_chat(self, chat_id: int) -> bool:
        """Remove a chat from the database and related group settings."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM chats WHERE chat_id = ?", (chat_id,))
                cursor.execute("DELETE FROM group_settings WHERE chat_id = ?", (chat_id,))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error removing chat from database: {e}")
            return False

    async def get_chat_settings(self, chat_id: int) -> Dict[str, Any]:
        """Get settings for a chat"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                SELECT settings FROM settings
                WHERE chat_id = ?
                """, (chat_id,))
                result = cursor.fetchone()
                return json.loads(result[0]) if result else {}
        except Exception as e:
            logger.error(f"Error getting chat settings: {e}")
            return {}

    async def set_chat_settings(self, chat_id: int, settings: Dict[str, Any]):
        """Set settings for a chat"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                INSERT OR REPLACE INTO settings (chat_id, settings)
                VALUES (?, ?)
                """, (chat_id, json.dumps(settings)))
                conn.commit()
        except Exception as e:
            logger.error(f"Error setting chat settings: {e}")

    async def get_user_data(self, user_id: int) -> Dict[str, Any]:
        """Get data for a user"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                SELECT data FROM users
                WHERE user_id = ?
                """, (user_id,))
                result = cursor.fetchone()
                return json.loads(result[0]) if result else {}
        except Exception as e:
            logger.error(f"Error getting user data: {e}")
            return {}

    async def set_user_data(self, user_id: int, data: Dict[str, Any]):
        """Set data for a user"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                INSERT OR REPLACE INTO users (user_id, data)
                VALUES (?, ?)
                """, (user_id, json.dumps(data)))
                conn.commit()
        except Exception as e:
            logger.error(f"Error setting user data: {e}")

    async def add_filter(self, chat_id: int, filter_name: str, filter_data: Dict[str, Any]):
        """Add a filter to a chat"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                INSERT OR REPLACE INTO filters (chat_id, filter_name, filter_data)
                VALUES (?, ?, ?)
                """, (chat_id, filter_name, json.dumps(filter_data)))
                conn.commit()
        except Exception as e:
            logger.error(f"Error adding filter: {e}")

    async def remove_filter(self, chat_id: int, filter_name: str):
        """Remove a filter from a chat"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                DELETE FROM filters
                WHERE chat_id = ? AND filter_name = ?
                """, (chat_id, filter_name))
                conn.commit()
        except Exception as e:
            logger.error(f"Error removing filter: {e}")

    async def get_filters(self, chat_id: int) -> List[Dict[str, Any]]:
        """Get all filters for a chat"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                SELECT filter_name, filter_data FROM filters
                WHERE chat_id = ?
                """, (chat_id,))
                filters = []
                for row in cursor.fetchall():
                    filters.append({
                        'name': row[0],
                        'data': json.loads(row[1])
                    })
                return filters
        except Exception as e:
            logger.error(f"Error getting filters: {e}")
            return []

    async def reset_warnings(self, chat_id: int, user_id: int) -> bool:
        """Reset all warnings for a user in a specific chat."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                DELETE FROM warnings
                WHERE chat_id = ? AND user_id = ?
                """, (chat_id, user_id))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error resetting warnings: {e}")
            return False

    async def get_all_users(self):
        """Get all users from the database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # Check if users table exists
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
                if not cursor.fetchone():
                    logger.warning("Users table does not exist")
                    return []

                cursor.execute("SELECT * FROM users")
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"Error getting all users: {e}")
            return []

    async def get_all_groups_full(self):
        """Get all groups and settings from the database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # Check if group_settings table exists
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='group_settings'")
                if not cursor.fetchone():
                    logger.warning("Group settings table does not exist")
                    return []

                cursor.execute("SELECT * FROM group_settings")
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"Error getting all groups: {e}")
            return []

    async def get_active_users(self, days=7):
        """Get users who were active in the last X days"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # Check if user_activity table exists
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='user_activity'")
                if not cursor.fetchone():
                    logger.warning("User activity table does not exist")
                    return []

                # Calculate date X days ago
                days_ago = (datetime.now() - timedelta(days=days)).isoformat()

                cursor.execute("""
                SELECT DISTINCT user_id FROM user_activity
                WHERE timestamp > ?
                """, (days_ago,))

                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"Error getting active users: {e}")
            return []

    async def get_user_count_by_chat_type(self):
        """Get count of users by chat type"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Check if user_activity table exists
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='user_activity'")
                if not cursor.fetchone():
                    logger.warning("User activity table does not exist")
                    return {}

                cursor.execute("""
                SELECT chat_type, COUNT(DISTINCT user_id) as count
                FROM user_activity
                GROUP BY chat_type
                """)

                results = cursor.fetchall()
                return {chat_type: count for chat_type, count in results}
        except Exception as e:
            logger.error(f"Error getting user count by chat type: {e}")
            return {}

    async def get_command_stats(self, limit=None):
        """Get command usage statistics"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Check if command_stats table exists
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='command_stats'")
                if not cursor.fetchone():
                    logger.warning("Command stats table does not exist")
                    return {}

                query = """
                SELECT command, COUNT(*) as count
                FROM command_stats
                GROUP BY command
                ORDER BY count DESC
                """

                if limit:
                    query += f" LIMIT {limit}"

                cursor.execute(query)
                results = cursor.fetchall()
                return {command: count for command, count in results}
        except Exception as e:
            logger.error(f"Error getting command stats: {e}")
            return {}

    # Alias for backward compatibility
    async def log_user_activity(self, user_id, chat_id, chat_type, activity_type="message"):
        """Alias for track_user_activity - for backward compatibility"""
        return await self.track_user_activity(chat_id, user_id, activity_type)

    async def log_command(self, command, user_id, chat_id, chat_type):
        """Log command usage"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Create command_stats table if it doesn't exist
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS command_stats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    command TEXT NOT NULL,
                    user_id INTEGER NOT NULL,
                    chat_id INTEGER NOT NULL,
                    chat_type TEXT NOT NULL,
                    timestamp TEXT NOT NULL
                )
                """)

                cursor.execute("""
                INSERT INTO command_stats (command, user_id, chat_id, chat_type, timestamp)
                VALUES (?, ?, ?, ?, ?)
                """, (command, user_id, chat_id, chat_type, datetime.now().isoformat()))

                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error logging command: {e}")
            return False

    async def get_bot_stats(self):
        """Get comprehensive bot statistics"""
        stats = {}

        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # Total users
                cursor.execute("SELECT COUNT(DISTINCT user_id) as count FROM user_activity")
                result = cursor.fetchone()
                stats['total_users'] = result['count'] if result else 0

                # Total groups
                cursor.execute("SELECT COUNT(DISTINCT chat_id) as count FROM group_settings")
                result = cursor.fetchone()
                stats['total_groups'] = result['count'] if result else 0

                # Active users in last 7 days
                days_ago = (datetime.now() - timedelta(days=7)).isoformat()
                cursor.execute("""
                SELECT COUNT(DISTINCT user_id) as count FROM user_activity
                WHERE timestamp > ?
                """, (days_ago,))
                result = cursor.fetchone()
                stats['active_users_7d'] = result['count'] if result else 0

                # Messages processed
                cursor.execute("SELECT COUNT(*) as count FROM user_activity WHERE activity_type = 'message'")
                result = cursor.fetchone()
                stats['total_messages'] = result['count'] if result else 0

                # Commands processed
                cursor.execute("SELECT COUNT(*) as count FROM command_stats")
                result = cursor.fetchone()
                stats['total_commands'] = result['count'] if result else 0

                # User count by chat type
                cursor.execute("""
                SELECT chat_type, COUNT(DISTINCT user_id) as count
                FROM user_activity
                GROUP BY chat_type
                """)
                stats['users_by_chat_type'] = {row['chat_type']: row['count'] for row in cursor.fetchall()}

                # Top 5 commands
                cursor.execute("""
                SELECT command, COUNT(*) as count
                FROM command_stats
                GROUP BY command
                ORDER BY count DESC
                LIMIT 5
                """)
                stats['top_commands'] = {row['command']: row['count'] for row in cursor.fetchall()}

                # New users in last 24 hours
                day_ago = (datetime.now() - timedelta(days=1)).isoformat()
                cursor.execute("""
                SELECT COUNT(DISTINCT user_id) as count FROM user_activity
                WHERE timestamp > ? AND activity_type = 'join'
                """, (day_ago,))
                result = cursor.fetchone()
                stats['new_users_24h'] = result['count'] if result else 0

                return stats
        except Exception as e:
            logger.error(f"Error getting bot stats: {e}")
            return stats

    async def clear_all_chats(self) -> bool:
        """Clear all chats from the database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM chats")
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error clearing chats: {e}")
            return False


    async def add_ban(self, chat_id: int, user_id: int, admin_id: int, reason: str = None,
                     duration: int = None, ban_type: str = "ban") -> bool:
        """Add a ban record to the database.

        Args:
            chat_id: The ID of the chat where the ban occurred
            user_id: The ID of the banned user
            admin_id: The ID of the admin who performed the ban
            reason: The reason for the ban (optional)
            duration: Ban duration in seconds (optional, for temporary bans)
            ban_type: Type of ban ("ban", "tban", or "sban")
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Create bans table if it doesn't exist
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS bans (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    chat_id INTEGER NOT NULL,
                    user_id INTEGER NOT NULL,
                    admin_id INTEGER NOT NULL,
                    reason TEXT,
                    ban_type TEXT NOT NULL,
                    duration INTEGER,
                    timestamp TEXT NOT NULL,
                    active BOOLEAN DEFAULT TRUE
                )
                """)

                # Add the ban record
                cursor.execute("""
                INSERT INTO bans (chat_id, user_id, admin_id, reason, ban_type, duration, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (chat_id, user_id, admin_id, reason, ban_type, duration, datetime.now().isoformat()))

                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error adding ban record: {e}")
            return False

    async def remove_ban(self, chat_id: int, user_id: int) -> bool:
        """Remove a ban record from the database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                UPDATE bans
                SET active = FALSE
                WHERE chat_id = ? AND user_id = ? AND active = TRUE
                """, (chat_id, user_id))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error removing ban record: {e}")
            return False
