"""
🗳️ Vote Ban Handler Module
----------------------------
Refactored democratic vote-based ban system with configurable (lowered) thresholds and improved structure.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any
from telegram.constants import ParseMode
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.error import TelegramError
from telegram.ext import ContextTypes, CommandHandler, CallbackQueryHandler

from .log_handler import LogHandler

logger = logging.getLogger(__name__)

class VoteBanHandler:
    def __init__(self, db: Any):
        self.db = db
        # Active vote sessions: {chat_id: {target_user_id: vote_info_dict}}
        self.active_votes: Dict[int, Dict[int, Dict[str, Any]]] = {}
        # Lowered thresholds: regular users need 50% approval; admins need 60% approval.
        self.vote_threshold: float = 0.50
        self.admin_vote_threshold: float = 0.60
        self.log_handler = LogHandler(db)

    async def _delete_message_later(self, context: ContextTypes.DEFAULT_TYPE, chat_id: int,
                                    message_id: int, delay: int = 60) -> None:
        """Delete a message after a specified delay in seconds."""
        await asyncio.sleep(delay)
        try:
            await context.bot.delete_message(chat_id, message_id)
        except Exception as e:
            logger.error(f"Error deleting message: {e}")

    async def _send_delayed_task(self, delay: float, coro) -> None:
        """Helper to schedule a coroutine after delay."""
        await asyncio.sleep(delay)
        await coro

    async def notify_admins(self, context: ContextTypes.DEFAULT_TYPE, chat_id: int, user_id: int,
                            reason: str, votes: Dict[int, bool]) -> None:
        """Notify admins about the vote ban result."""
        try:
            admins = await context.bot.get_chat_administrators(chat_id)
            # Get target user info
            try:
                user = await context.bot.get_chat_member(chat_id, user_id)
                user_name = user.user.full_name
                username = f" (@{user.user.username})" if user.user.username else ""
            except Exception:
                user_name = "Unknown User"
                username = ""
            
            yes_votes = sum(1 for vote in votes.values() if vote)
            total_votes = len(votes)
            vote_percentage = (yes_votes / total_votes) * 100 if total_votes > 0 else 0
            
            voters_list = []
            for voter_id, vote in votes.items():
                try:
                    voter = await context.bot.get_chat_member(chat_id, voter_id)
                    voter_name = voter.user.full_name
                    voter_username = f" (@{voter.user.username})" if voter.user.username else ""
                    vote_emoji = "✅" if vote else "❌"
                    voters_list.append(f"{vote_emoji} {voter_name}{voter_username}")
                except Exception:
                    continue

            notification = (
                "<b>📊 Community Vote Ban Results</b>\n\n"
                f"<b>👤 User:</b> {user_name}{username}\n"
                f"<b>🆔 ID:</b> <code>{user_id}</code>\n"
                f"<b>📝 Reason:</b> {reason}\n"
                f"<b>✅ Yes Votes:</b> {yes_votes}\n"
                f"<b>❌ No Votes:</b> {total_votes - yes_votes}\n"
                f"<b>📊 Vote Percentage:</b> {vote_percentage:.1f}%\n\n"
                "<i>🔍 Please review and take appropriate action.</i>"
            )

            for admin in admins:
                try:
                    await context.bot.send_message(
                        admin.user.id,
                        notification,
                        parse_mode=ParseMode.HTML
                    )
                except TelegramError:
                    continue

        except TelegramError as e:
            logger.error(f"Error notifying admins: {e}")

    async def handle_vote(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle vote callback."""
        query = update.callback_query
        if not query:
            return

        try:
            # Extract data from callback; expected format: vote_{chat_id}_{target_id}_{yes/no}
            data = query.data.split("_")
            if len(data) != 4:
                await query.answer("❌ Invalid vote data!")
                return

            _, chat_id_str, target_id_str, vote_str = data
            chat_id, target_id = int(chat_id_str), int(target_id_str)
            vote = (vote_str.lower() == "yes")

            if chat_id not in self.active_votes or target_id not in self.active_votes[chat_id]:
                await query.answer("⌛ This vote has expired!")
                await query.message.delete()
                return

            vote_info = self.active_votes[chat_id][target_id]

            if query.from_user.id == target_id:
                await query.answer("❌ You cannot vote on your own ban!")
                return

            voter_id = query.from_user.id
            if voter_id in vote_info["votes"]:
                previous_vote = vote_info["votes"][voter_id]
                if previous_vote == vote:
                    await query.answer("✋ You've already voted this way!")
                    return
                else:
                    await query.answer("🔄 Changed your vote!")
            else:
                await query.answer("✅ Vote recorded!")

            vote_info["votes"][voter_id] = vote

            # Fetch voter info for display
            try:
                voter = await context.bot.get_chat_member(chat_id, voter_id)
                voter_name = voter.user.full_name
                username = f" (@{voter.user.username})" if voter.user.username else ""
            except Exception:
                voter_name = "Unknown User"
                username = ""

            yes_votes = sum(1 for v in vote_info["votes"].values() if v)
            no_votes = len(vote_info["votes"]) - yes_votes

            vote_text = (
                f"{vote_info['message']}\n\n"
                f"<b>Current Votes:</b>\n"
                f"✅ Yes: {yes_votes} | ❌ No: {no_votes}\n\n"
                f"<i>Last vote: {voter_name}{username} voted {'✅ Yes' if vote else '❌ No'}</i>"
            )

            await query.message.edit_text(
                vote_text,
                reply_markup=vote_info["markup"],
                parse_mode=ParseMode.HTML
            )

        except Exception as e:
            logger.error(f"Error handling vote: {e}")
            await query.answer("❌ An error occurred!")

    async def end_vote(self, context: ContextTypes.DEFAULT_TYPE, chat_id: int, target_id: int) -> None:
        """End a vote session and process results."""
        if chat_id not in self.active_votes or target_id not in self.active_votes[chat_id]:
            return

        vote_info = self.active_votes[chat_id][target_id]
        votes = vote_info["votes"]
        message = vote_info["original_message"]
        is_admin = vote_info.get("is_admin", False)
        threshold = self.admin_vote_threshold if is_admin else self.vote_threshold

        yes_votes = sum(1 for vote in votes.values() if vote)
        total_votes = len(votes)
        vote_percentage = (yes_votes / total_votes) if total_votes > 0 else 0

        if total_votes > 0:
            if vote_percentage >= threshold:
                await self.notify_admins(context, chat_id, target_id, vote_info["reason"], votes)
                try:
                    target_user = await context.bot.get_chat_member(chat_id, target_id)
                    await self.log_handler.log_event(
                        chat_id,
                        "voteban",
                        {
                            "user": target_user.user,
                            "reason": vote_info["reason"],
                            "votes": f"✅ {yes_votes}/{total_votes} ({vote_percentage * 100:.1f}%)",
                            "threshold": f"{threshold * 100:.0f}% {'(Admin Vote)' if is_admin else '(Regular Vote)'}"
                        },
                        context
                    )
                except Exception as e:
                    logger.error(f"Failed to log vote ban event: {e}")

                result_msg = await message.reply_text(
                    "<b>🗳️ Vote Ban Results</b>\n\n"
                    f"✅ <b>{yes_votes}/{total_votes}</b> members voted to ban.\n"
                    f"📊 {vote_percentage * 100:.1f}% approval rate.\n"
                    f"<i>Required: {threshold * 100:.0f}% approval{' (Admin Vote)' if is_admin else ''}</i>\n"
                    "<i>📝 This case has been forwarded to admins for review.</i>\n\n"
                    "<b>Note:</b> <i>The accused can use /appeal to present their case.</i>",
                    parse_mode=ParseMode.HTML
                )
            else:
                result_msg = await message.reply_text(
                    "<b>🗳️ Vote Ban Results</b>\n\n"
                    f"❌ Not enough support (<b>{yes_votes}/{total_votes}</b> votes).\n"
                    f"📊 {vote_percentage * 100:.1f}% approval rate.\n"
                    f"<i>Required: {threshold * 100:.0f}% approval{' (Admin Vote)' if is_admin else ''}</i>",
                    parse_mode=ParseMode.HTML
                )
        else:
            result_msg = await message.reply_text(
                "<b>🗳️ Vote Ban Results</b>\n\n"
                "❌ No votes were cast.\n"
                "<i>Vote session ended without participation.</i>",
                parse_mode=ParseMode.HTML
            )

        try:
            await vote_info["vote_message"].delete()
        except Exception:
            pass

        # Schedule deletion of result message after 60 seconds
        asyncio.create_task(self._delete_message_later(context, chat_id, result_msg.message_id))
        # Clean up vote session data
        del self.active_votes[chat_id][target_id]
        if not self.active_votes[chat_id]:
            del self.active_votes[chat_id]

    async def voteban(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Start a vote ban session."""
        if not update.effective_message or not update.effective_chat:
            return

        message = update.effective_message
        chat_id = update.effective_chat.id

        if not message.reply_to_message:
            help_msg = await message.reply_text(
                "<b>❌ How to Start a Vote Ban:</b>\n\n"
                "<i>1. Reply to the user's message\n"
                "2. Use the command with time and reason:</i>\n"
                "<code>/voteban [time] [reason]</code>\n\n"
                "<b>Time Options:</b>\n"
                "• Minutes: 1m to 59m\n"
                "• Hours: 1h to 24h\n\n"
                "<b>Note:</b>\n"
                f"• Regular users need {self.vote_threshold * 100:.0f}% approval\n"
                f"• Admin votes need {self.admin_vote_threshold * 100:.0f}% approval\n\n"
                "<b>Example:</b>\n"
                "<code>/voteban 1h spamming and harassment</code>",
                parse_mode=ParseMode.HTML
            )
            asyncio.create_task(self._delete_message_later(context, chat_id, help_msg.message_id, 30))
            asyncio.create_task(self._delete_message_later(context, chat_id, message.message_id, 30))
            return

        target_user = message.reply_to_message.from_user
        if not target_user:
            await message.reply_text("❌ Cannot identify the target user!")
            return

        is_admin = False
        try:
            target_member = await context.bot.get_chat_member(chat_id, target_user.id)
            is_admin = target_member.status in ['administrator', 'creator']
        except TelegramError:
            error_msg = await message.reply_text("❌ Error checking user status!")
            asyncio.create_task(self._delete_message_later(context, chat_id, error_msg.message_id))
            return

        if not context.args:
            error_msg = await message.reply_text("❌ Please provide duration and reason!")
            asyncio.create_task(self._delete_message_later(context, chat_id, error_msg.message_id))
            asyncio.create_task(self._delete_message_later(context, chat_id, message.message_id))
            return

        duration_str = context.args[0].lower()
        reason = " ".join(context.args[1:]) if len(context.args) > 1 else "No reason provided"

        try:
            if duration_str.endswith('m'):
                minutes = int(duration_str[:-1])
                if not 1 <= minutes <= 59:
                    raise ValueError
                duration = timedelta(minutes=minutes)
            elif duration_str.endswith('h'):
                hours = int(duration_str[:-1])
                if not 1 <= hours <= 24:
                    raise ValueError
                duration = timedelta(hours=hours)
            else:
                raise ValueError
        except ValueError:
            error_msg = await message.reply_text(
                "❌ Invalid duration format!\n"
                "Use: 1m to 59m for minutes\n"
                "Or: 1h to 24h for hours"
            )
            asyncio.create_task(self._delete_message_later(context, chat_id, error_msg.message_id))
            asyncio.create_task(self._delete_message_later(context, chat_id, message.message_id))
            return

        if chat_id in self.active_votes and target_user.id in self.active_votes[chat_id]:
            error_msg = await message.reply_text("❌ There's already an active vote for this user!")
            asyncio.create_task(self._delete_message_later(context, chat_id, error_msg.message_id))
            asyncio.create_task(self._delete_message_later(context, chat_id, message.message_id))
            return

        if chat_id not in self.active_votes:
            self.active_votes[chat_id] = {}

        vote_message = (
            "<b>🗳️ Vote Ban Initiated</b>\n\n"
            f"<b>Target:</b> {target_user.full_name}"
            f"{f' (@{target_user.username})' if target_user.username else ''}\n"
            f"<b>Status:</b> {'👑 Admin' if is_admin else '👤 Regular User'}\n"
            f"<b>Required:</b> {self.admin_vote_threshold * 100:.0f}% approval" if is_admin \
                else f"<b>Required:</b> {self.vote_threshold * 100:.0f}% approval" + "\n"
            f"<b>Reason:</b> {reason}\n"
            f"<b>Duration:</b> {duration_str}\n"
            f"<b>Initiated by:</b> {message.from_user.full_name}"
            f"{f' (@{message.from_user.username})' if message.from_user.username else ''}"
        )

        keyboard = [
            [
                InlineKeyboardButton("✅ Yes", callback_data=f"vote_{chat_id}_{target_user.id}_yes"),
                InlineKeyboardButton("❌ No", callback_data=f"vote_{chat_id}_{target_user.id}_no")
            ]
        ]
        markup = InlineKeyboardMarkup(keyboard)

        vote_msg = await message.reply_text(
            vote_message,
            reply_markup=markup,
            parse_mode=ParseMode.HTML
        )

        self.active_votes[chat_id][target_user.id] = {
            "votes": {},
            "message": vote_message,
            "markup": markup,
            "vote_message": vote_msg,
            "original_message": message,
            "reason": reason,
            "duration": duration,
            "is_admin": is_admin
        }

        asyncio.create_task(self._delete_message_later(context, chat_id, message.message_id))
        # Schedule vote duration delay and forcing vote end concurrently.
        asyncio.create_task(self._delayed_end_vote(context, chat_id, target_user.id, duration.total_seconds()))

    async def _delayed_end_vote(self, context: ContextTypes.DEFAULT_TYPE, chat_id: int, target_id: int, delay: float) -> None:
        await asyncio.sleep(delay)
        await self.end_vote(context, chat_id, target_id)

def setup(application) -> None:
    """Setup vote ban handlers."""
    db = application.bot_data.get('db')
    handler_instance = VoteBanHandler(db)
    application.add_handler(CommandHandler("voteban", handler_instance.voteban))
    application.add_handler(CallbackQueryHandler(handler_instance.handle_vote, pattern="^vote_"))
    
if __name__ == "__main__":
    # For local testing purposes only.
    import sys
    logging.basicConfig(level=logging.DEBUG)
    sys.exit("This module is not intended to be run standalone.")
