# 🤖 Advanced Telegram Group Management Bot

A powerful and feature-rich Telegram bot for managing groups with advanced capabilities.

## ✨ Features

### 👮‍♂️ User Management
- Ban/Unban users
- Kick users
- Mute/Unmute users
- Warning system
- User notes

### 🛡️ Advanced Protection
1. 🚫 **Anti-Spam System**
   - Message frequency monitoring
   - Automatic muting of spammers
   - Customizable thresholds

2. 🌙 **Night Mode**
   - Restrict chat during specific hours
   - Customizable quiet hours
   - Automatic enforcement

3. 🤖 **Captcha Verification**
   - New member verification
   - Math-based captcha
   - Automatic restriction until verified

4. 🛡️ **Raid Protection**
   - Detect sudden member influx
   - Automatic lockdown mode
   - Admin notifications

5. 🔍 **Content Filtering**
   - Banned word detection
   - URL whitelist system
   - Automatic message deletion

### 👋 Welcome Features
- Customizable welcome messages
- Goodbye messages
- User statistics

### 📝 Notes & Rules
- Save and retrieve notes
- Set group rules
- Quick access commands

### ⚙️ Group Settings
- Flexible configuration
- Permission management
- Custom command prefixes

### 💾 Storage Options
- MongoDB support (optional)
- Fallback to in-memory storage
- Data persistence

## 🚀 Setup

### Option 1: Manual Setup

1. Clone this repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Configure your environment variables in `.env`:
   ```env
   TELEGRAM_BOT_TOKEN=your_bot_token
   BOT_OWNER=your_username
   SQLITE_DB_PATH=data/bot_database.db (optional)
   ```

4. Run the bot:
   ```bash
   python bot.py
   ```

### Option 2: Automated Setup (Windows)

1. Run the setup script:
   ```cmd
   setup.bat
   ```

2. Start all services:
   ```cmd
   start.bat
   ```

### Option 3: Using npm (Cross-platform)

1. Install dependencies:
   ```bash
   npm run install:all
   ```

2. Start all services:
   ```bash
   npm start
   ```

## 📚 Commands

### 👮‍♂️ Admin Commands
- `/ban` - Ban a user
- `/unban` - Unban a user
- `/kick` - Kick a user
- `/mute` - Mute a user
- `/unmute` - Unmute a user
- `/warn` - Warn a user
- `/unwarn` - Remove warnings

### 🛡️ Protection Commands
- `/antispam` - Toggle anti-spam
- `/nightmode` - Set night mode
- `/raid` - Toggle raid protection
- `/captcha` - Toggle captcha verification

### 📝 Notes & Rules
- `/save` - Save a note
- `/get` - Get a note
- `/notes` - List all notes
- `/setrules` - Set group rules
- `/rules` - Show group rules

### ⚙️ Settings
- `/settings` - Group settings
- `/welcome` - Set welcome message
- `/goodbye` - Set goodbye message

## 🤝 Contributing

Feel free to submit issues and enhancement requests!

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
