"""
Plugin Manager for NexusAI Telegram Bot
"""
import os
import sys
import importlib
import logging
import inspect
import asyncio
from typing import Dict, List, Type, Optional, Any
from pathlib import Path
from telegram.ext import Application
from .base_plugin import BasePlugin
from .plugin_types import PluginMetadata

class PluginLoadError(Exception):
    """Raised when a plugin fails to load"""
    pass

class PluginManager:
    def __init__(self):
        self.plugins: Dict[str, BasePlugin] = {}
        self.logger = logging.getLogger("plugin_manager")
        self._load_order: List[str] = []  # Track plugin load order for dependencies
        
    async def load_plugin(self, plugin_class: Type[BasePlugin], application: Application) -> bool:
        """
        Load a single plugin
        
        Args:
            plugin_class: The plugin class to load
            application: The telegram application instance
            
        Returns:
            bool: True if plugin was loaded successfully
            
        Raises:
            PluginLoadError: If plugin fails to load
        """
        try:
            # Check if plugin is already loaded
            if plugin_class.metadata.name in self.plugins:
                self.logger.warning(f"Plugin {plugin_class.metadata.name} is already loaded")
                return False
                
            # Check plugin dependencies
            if plugin_class.metadata.requires:
                for dep_name, dep_version in plugin_class.metadata.requires.items():
                    if dep_name not in self.plugins:
                        raise PluginLoadError(f"Missing required plugin: {dep_name}")
                    # TODO: Add version compatibility check
                    
            # Initialize plugin
            plugin = plugin_class()
            await plugin.setup(application)
            
            # Register plugin
            self.plugins[plugin.name] = plugin
            self._load_order.append(plugin.name)
            
            self.logger.info(
                f"Loaded plugin: {plugin.name} v{plugin.metadata.version} "
                f"by {plugin.metadata.author}"
            )
            return True
            
        except Exception as e:
            raise PluginLoadError(f"Failed to load plugin {plugin_class.__name__}: {str(e)}")
            
    async def load_plugins_from_directory(self, directory: str, application: Application) -> None:
        """
        Load all plugins from a directory
        
        Args:
            directory: Path to plugins directory
            application: The telegram application instance
        """
        directory = Path(directory)
        if not directory.exists():
            self.logger.warning(f"Plugins directory not found: {directory}")
            return
            
        # Add plugins directory to Python path
        if str(directory) not in sys.path:
            sys.path.append(str(directory))
            
        for file_path in directory.glob("*.py"):
            if file_path.name.startswith("__"):
                continue
                
            try:
                module_name = file_path.stem
                module = importlib.import_module(module_name)
                
                # Find plugin classes in module
                for _, obj in inspect.getmembers(module):
                    if (inspect.isclass(obj) and 
                        issubclass(obj, BasePlugin) and 
                        obj != BasePlugin):
                        await self.load_plugin(obj, application)
                        
            except Exception as e:
                self.logger.error(f"Error loading plugin from {file_path}: {str(e)}")
                
    def get_plugin(self, name: str) -> Optional[BasePlugin]:
        """Get a plugin by name"""
        return self.plugins.get(name)
        
    def get_all_plugins(self) -> List[BasePlugin]:
        """Get all loaded plugins in load order"""
        return [self.plugins[name] for name in self._load_order]
        
    def enable_plugin(self, name: str) -> bool:
        """Enable a plugin"""
        plugin = self.get_plugin(name)
        if plugin:
            plugin.enable()
            return True
        return False
        
    def disable_plugin(self, name: str) -> bool:
        """Disable a plugin"""
        plugin = self.get_plugin(name)
        if plugin:
            plugin.disable()
            return True
        return False
        
    async def reload_plugin(self, name: str, application: Application) -> bool:
        """
        Reload a plugin
        
        Args:
            name: Name of plugin to reload
            application: The telegram application instance
            
        Returns:
            bool: True if plugin was reloaded successfully
        """
        plugin = self.get_plugin(name)
        if not plugin:
            return False
            
        # Clean up old plugin
        await plugin.cleanup()
        del self.plugins[name]
        self._load_order.remove(name)
        
        # Reload module and plugin
        module = importlib.import_module(plugin.__class__.__module__)
        importlib.reload(module)
        
        # Find plugin class and load it
        for _, obj in inspect.getmembers(module):
            if (inspect.isclass(obj) and 
                issubclass(obj, BasePlugin) and 
                obj != BasePlugin and 
                obj.metadata.name == name):
                return await self.load_plugin(obj, application)
                
        return False
        
    async def cleanup_plugins(self) -> None:
        """Clean up all plugins in reverse load order"""
        for name in reversed(self._load_order):
            try:
                await self.plugins[name].cleanup()
            except Exception as e:
                self.logger.error(f"Error cleaning up plugin {name}: {str(e)}")
                
    def get_all_commands(self) -> Dict[str, Dict[str, Any]]:
        """Get all commands from all enabled plugins"""
        commands = {}
        for plugin in self.get_all_plugins():
            if plugin.is_enabled:
                plugin_commands = plugin.get_commands()
                if plugin_commands:
                    commands[plugin.name] = {
                        name: cmd.__dict__ 
                        for name, cmd in plugin_commands.items()
                    }
        return commands
