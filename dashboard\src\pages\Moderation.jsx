import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Button,
  TextField,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Avatar,
  Card,
  CardContent,
  CardActions
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import CheckIcon from '@mui/icons-material/Check';
import CloseIcon from '@mui/icons-material/Close';
import DeleteIcon from '@mui/icons-material/Delete';
import VisibilityIcon from '@mui/icons-material/Visibility';
import { moderationAPI } from '../services/api';

export default function Moderation() {
  const [queue, setQueue] = useState([]);
  const [flaggedUsers, setFlaggedUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [search, setSearch] = useState('');
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);

  useEffect(() => {
    const fetchModerationData = async () => {
      try {
        const response = await moderationAPI.getQueue();
        setQueue(response.data);
        // For flagged users, we'll use a subset of the queue data for now
        // In a real implementation, this would come from a separate API endpoint
        setFlaggedUsers(response.data.slice(0, 2).map(item => ({
          id: item.id,
          name: item.user,
          username: item.username,
          violations: Math.floor(Math.random() * 10) + 1,
          lastViolation: item.timestamp,
          status: Math.random() > 0.5 ? 'active' : 'muted'
        })));
        setLoading(false);
      } catch (error) {
        console.error('Error fetching moderation data:', error);
        setLoading(false);
      }
    };

    fetchModerationData();
  }, []);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearchChange = (event) => {
    setSearch(event.target.value);
    setPage(0);
  };

  const handleApprove = (itemId) => {
    setQueue(queue.filter(item => item.id !== itemId));
  };

  const handleReject = (itemId) => {
    setQueue(queue.filter(item => item.id !== itemId));
  };

  const handleDelete = (itemId) => {
    setQueue(queue.filter(item => item.id !== itemId));
  };

  const handleViewDetails = (item) => {
    setSelectedItem(item);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedItem(null);
  };

  const filteredQueue = queue.filter(item =>
    item.user.toLowerCase().includes(search.toLowerCase()) ||
    item.group.toLowerCase().includes(search.toLowerCase()) ||
    item.content.toLowerCase().includes(search.toLowerCase())
  );

  const paginatedQueue = filteredQueue.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Typography variant="h4">Loading moderation data...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Typography variant="h1" gutterBottom>
        Content Moderation
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2, mb: 3 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <TextField
                variant="outlined"
                placeholder="Search moderation queue..."
                value={search}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: <SearchIcon />,
                }}
              />
            </Box>
            
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>User</TableCell>
                    <TableCell>Content</TableCell>
                    <TableCell>Group</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Time</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {paginatedQueue.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>
                        <Box display="flex" alignItems="center">
                          <Avatar sx={{ mr: 2, width: 32, height: 32, fontSize: 14 }}>
                            {item.user.split(' ').map(n => n[0]).join('')}
                          </Avatar>
                          <Box>
                            <Typography variant="body2">{item.user}</Typography>
                            <Typography variant="caption" color="textSecondary">
                              {item.username}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">{item.content}</Typography>
                      </TableCell>
                      <TableCell>{item.group}</TableCell>
                      <TableCell>
                        <Chip 
                          label={item.type} 
                          size="small"
                          color={
                            item.type === 'text' ? 'primary' : 
                            item.type === 'image' ? 'secondary' : 'warning'
                          }
                        />
                      </TableCell>
                      <TableCell>{item.timestamp}</TableCell>
                      <TableCell>
                        <IconButton onClick={() => handleViewDetails(item)}>
                          <VisibilityIcon />
                        </IconButton>
                        <IconButton onClick={() => handleApprove(item.id)} color="success">
                          <CheckIcon />
                        </IconButton>
                        <IconButton onClick={() => handleReject(item.id)} color="warning">
                          <CloseIcon />
                        </IconButton>
                        <IconButton onClick={() => handleDelete(item.id)} color="error">
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            
            <TablePagination
              rowsPerPageOptions={[5, 10, 25]}
              component="div"
              count={filteredQueue.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h2" gutterBottom>
                Flagged Users
              </Typography>
              {flaggedUsers.map((user) => (
                <Box key={user.id} sx={{ mb: 2, pb: 2, borderBottom: '1px solid #eee' }}>
                  <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Box>
                      <Typography variant="body1">{user.name}</Typography>
                      <Typography variant="caption" color="textSecondary">
                        {user.username}
                      </Typography>
                    </Box>
                    <Chip 
                      label={user.status} 
                      size="small"
                      color={user.status === 'active' ? 'success' : 'warning'}
                    />
                  </Box>
                  <Box display="flex" justifyContent="space-between" mt={1}>
                    <Typography variant="body2">
                      Violations: {user.violations}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Last: {user.lastViolation}
                    </Typography>
                  </Box>
                </Box>
              ))}
            </CardContent>
            <CardActions>
              <Button size="small" fullWidth variant="outlined">
                View All Flagged Users
              </Button>
            </CardActions>
          </Card>
          
          <Card>
            <CardContent>
              <Typography variant="h2" gutterBottom>
                Quick Actions
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Button variant="contained" color="primary" fullWidth>
                    Approve All
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button variant="contained" color="secondary" fullWidth>
                    Reject All
                  </Button>
                </Grid>
                <Grid item xs={12}>
                  <Button variant="outlined" fullWidth>
                    Export Report
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      {/* Item Detail Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          Moderation Item Details
        </DialogTitle>
        <DialogContent>
          {selectedItem && (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <Typography variant="h3">{selectedItem.user}</Typography>
                <Typography variant="subtitle1" color="textSecondary">
                  {selectedItem.username}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle1"><strong>Content:</strong></Typography>
                <Paper variant="outlined" sx={{ p: 2, mt: 1 }}>
                  <Typography>{selectedItem.content}</Typography>
                </Paper>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="subtitle1"><strong>Group:</strong> {selectedItem.group}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="subtitle1"><strong>Type:</strong> {selectedItem.type}</Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle1"><strong>Timestamp:</strong> {selectedItem.timestamp}</Typography>
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>Action</InputLabel>
                  <Select defaultValue="">
                    <MenuItem value="approve">Approve</MenuItem>
                    <MenuItem value="reject">Reject</MenuItem>
                    <MenuItem value="ban">Ban User</MenuItem>
                    <MenuItem value="warn">Issue Warning</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleCloseDialog} variant="contained" color="primary">
            Apply Action
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}