import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  FormControlLabel,
  FormGroup,
  Divider,
  Chip,
  Alert,
  Grid
} from '@mui/material';
import SendIcon from '@mui/icons-material/Send';
import ScheduleIcon from '@mui/icons-material/Schedule';
import AttachFileIcon from '@mui/icons-material/AttachFile';

export default function Broadcast() {
  const [message, setMessage] = useState('');
  const [selectedGroups, setSelectedGroups] = useState([]);
  const [schedule, setSchedule] = useState(false);
  const [scheduleTime, setScheduleTime] = useState('');
  const [media, setMedia] = useState(null);
  const [preview, setPreview] = useState(false);

  const groups = [
    { id: 1, name: 'All Groups', members: 12450 },
    { id: 2, name: 'Tech Enthusiasts', members: 1240 },
    { id: 3, name: 'AI Researchers', members: 890 },
    { id: 4, name: 'Python Developers', members: 2100 },
    { id: 5, name: 'Data Science Community', members: 1560 },
  ];

  const handleGroupChange = (event) => {
    const value = event.target.value;
    setSelectedGroups(typeof value === 'string' ? value.split(',') : value);
  };

  const handleSendBroadcast = () => {
    // Handle broadcast sending logic here
    console.log('Sending broadcast:', { message, selectedGroups, schedule, scheduleTime, media });
    // Show success message
  };

  const handlePreview = () => {
    setPreview(!preview);
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Typography variant="h1" gutterBottom>
        Broadcast Message
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h2" gutterBottom>
              Compose Message
            </Typography>
            
            <TextField
              fullWidth
              multiline
              rows={6}
              variant="outlined"
              placeholder="Type your broadcast message here..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              sx={{ mb: 2 }}
            />
            
            <Button
              variant="outlined"
              startIcon={<AttachFileIcon />}
              sx={{ mb: 2 }}
            >
              Attach Media
            </Button>
            
            <Divider sx={{ my: 2 }} />
            
            <Typography variant="h2" gutterBottom>
              Target Groups
            </Typography>
            
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Select Groups</InputLabel>
              <Select
                multiple
                value={selectedGroups}
                onChange={handleGroupChange}
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => {
                      const group = groups.find(g => g.id === value);
                      return group ? (
                        <Chip key={group.id} label={group.name} size="small" />
                      ) : null;
                    })}
                  </Box>
                )}
              >
                {groups.map((group) => (
                  <MenuItem key={group.id} value={group.id}>
                    <Checkbox checked={selectedGroups.indexOf(group.id) > -1} />
                    <ListItemText 
                      primary={group.name} 
                      secondary={`${group.members.toLocaleString()} members`} 
                    />
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            
            <FormControlLabel
              control={
                <Checkbox
                  checked={schedule}
                  onChange={(e) => setSchedule(e.target.checked)}
                />
              }
              label="Schedule for later"
            />
            
            {schedule && (
              <TextField
                type="datetime-local"
                fullWidth
                value={scheduleTime}
                onChange={(e) => setScheduleTime(e.target.value)}
                sx={{ mt: 1 }}
              />
            )}
            
            <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
              <Button
                variant="contained"
                startIcon={<SendIcon />}
                onClick={handleSendBroadcast}
                disabled={!message || selectedGroups.length === 0}
              >
                Send Broadcast
              </Button>
              <Button
                variant="outlined"
                onClick={handlePreview}
              >
                Preview
              </Button>
            </Box>
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h2" gutterBottom>
              Broadcast History
            </Typography>
            
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1">Today</Typography>
              <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                <Typography variant="body2">Weekly update for all groups</Typography>
                <Typography variant="caption" color="textSecondary">
                  Sent to 12,450 users • 2 hours ago
                </Typography>
              </Paper>
              <Paper variant="outlined" sx={{ p: 2 }}>
                <Typography variant="body2">New feature announcement</Typography>
                <Typography variant="caption" color="textSecondary">
                  Sent to 8,760 users • 5 hours ago
                </Typography>
              </Paper>
            </Box>
            
            <Box>
              <Typography variant="subtitle1">Yesterday</Typography>
              <Paper variant="outlined" sx={{ p: 2 }}>
                <Typography variant="body2">Maintenance notification</Typography>
                <Typography variant="caption" color="textSecondary">
                  Sent to 15,230 users • 1 day ago
                </Typography>
              </Paper>
            </Box>
          </Paper>
          
          <Paper sx={{ p: 3 }}>
            <Typography variant="h2" gutterBottom>
              Statistics
            </Typography>
            
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1">Delivery Rate</Typography>
              <Typography variant="h3" color="success.main">98.7%</Typography>
            </Box>
            
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1">Read Rate</Typography>
              <Typography variant="h3" color="primary.main">76.2%</Typography>
            </Box>
            
            <Box>
              <Typography variant="subtitle1">Avg. Response Time</Typography>
              <Typography variant="h3">4.2 min</Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
}