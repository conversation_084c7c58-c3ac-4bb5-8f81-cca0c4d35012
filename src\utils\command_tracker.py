"""
Command tracker middleware for tracking command usage in the database.
"""
import logging
from typing import Any, Callable, Dict, Optional, Awaitable
from telegram import Update
from telegram.ext import ContextTypes, Application
from src.database.tracker import db_tracker

logger = logging.getLogger(__name__)

async def track_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Track command usage in the database.
    
    This function should be called before command handlers to track command usage.
    """
    if not update.effective_message or not update.effective_message.text or not update.effective_message.text.startswith('/'):
        return
    
    try:
        # Extract command and arguments
        text = update.effective_message.text
        command_parts = text.split(' ', 1)
        command = command_parts[0].split('@')[0][1:]  # Remove / and @bot_username if present
        arguments = command_parts[1] if len(command_parts) > 1 else None
        
        # Get user and chat info
        user_id = update.effective_user.id if update.effective_user else None
        chat_id = update.effective_chat.id if update.effective_chat else None
        
        if user_id:
            # Track command usage
            db_tracker.track_command(
                user_id=user_id,
                command=command,
                server_id=chat_id if chat_id != user_id else None,  # Only track server if not a private chat
                arguments=arguments,
                success=True  # We don't know if it will succeed yet, but we can update this later if needed
            )
    except Exception as e:
        logger.error(f"Error tracking command: {e}")

class CommandTrackerMiddleware:
    """Middleware for tracking command usage."""
    
    def __init__(self, application: Application):
        """
        Initialize the command tracker middleware.
        
        Args:
            application: The Telegram application
        """
        self.application = application
        self._setup_middleware()
    
    def _setup_middleware(self) -> None:
        """Set up the middleware to track commands."""
        # Store original process_update method
        original_process_update = self.application.process_update
        
        # Define new process_update method with tracking
        async def process_update_with_tracking(update: Update) -> None:
            # Track command if it's a command
            if update.effective_message and update.effective_message.text and update.effective_message.text.startswith('/'):
                await track_command(update, None)
            
            # Call original method
            await original_process_update(update)
        
        # Replace process_update method
        self.application.process_update = process_update_with_tracking