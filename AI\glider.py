from typing import Dict, List, Optional, Generator, Union, Set
import json
import requests
from pydantic import BaseModel
from .logger import log_model_request, log_model_response, log_stream_chunk


DEFAULT_HEADERS = {
    'Accept': '*/*',
    'Accept-Language': 'en-US,en;q=0.9',
    'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
    'Content-Type': 'application/json',
    'Origin': 'https://glider.so',
    'Referer': 'https://glider.so/'
}


class Message(BaseModel):
    role: str
    content: str


class Usage(BaseModel):
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int


class Choice(BaseModel):
    index: int
    delta: Dict[str, str]
    logprobs: Optional[Dict] = None
    finish_reason: Optional[str] = None


class ChatCompletionChunk(BaseModel):
    id: str
    object: str
    created: int
    model: str
    choices: List[Choice]
    usage: Optional[Usage] = None


class GliderAI:
    label: str = "Glider AI"
    url: str = "https://glider.so"
    working: bool = True
    supports_system_message: bool = True
    supports_message_history: bool = True

    # Available models
    AVAILABLE_MODELS: Set[str] = {
        "chat-llama-3-1-70b",
        "chat-llama-3-1-8b",
        "chat-llama-3-2-3b",
        "deepseek-ai/DeepSeek-R1",
    }

    # API endpoint for chat completions
    chat_api_endpoint: str = "https://glider.so/api/chat"

    @classmethod
    def get_available_models(cls) -> Set[str]:
        """Returns the set of available models."""
        return cls.AVAILABLE_MODELS

    @classmethod
    def generate_text(
        cls,
        model: str,
        messages: List[Dict[str, str]],
        temperature: Optional[float] = 0.7,
        max_tokens: Optional[int] = 600,
        top_p: Optional[float] = 1,
        frequency_penalty: Optional[float] = None,
        stop: Optional[List[str]] = None,
        stream: Optional[bool] = False,
        api_key: Optional[str] = None
    ) -> Union[str, Generator[str, None, None]]:
        # Log model request
        log_model_request(model, messages, temperature=temperature, max_tokens=max_tokens)

        if model not in cls.AVAILABLE_MODELS:
            error_msg = f"Invalid model: {model}. Available models: {', '.join(sorted(cls.AVAILABLE_MODELS))}"
            log_model_response(model, error_msg, error=True)
            raise ValueError(error_msg)

        headers = DEFAULT_HEADERS.copy()
        if api_key:
            headers.update({'Authorization': f'Bearer {api_key}'})
        
        payload = {
            'model': model,
            'messages': messages,
            'temperature': temperature,
            'max_tokens': max_tokens,
            'top_p': top_p,
            'frequency_penalty': frequency_penalty,
            'stop': stop,
            'stream': True  # Always use streaming internally
        }
        # Remove keys with None values
        payload = {k: v for k, v in payload.items() if v is not None}

        def process_stream():
            try:
                response = requests.post(
                    cls.chat_api_endpoint,
                    headers=headers,
                    json=payload,
                    stream=True
                )
                response.raise_for_status()
                
                full_text = ""
                for line in response.iter_lines(decode_unicode=True):
                    if line and line.startswith("data: "):
                        if line.strip() == "data: [DONE]":
                            break
                        try:
                            chunk_data = json.loads(line[6:])
                            chunk = ChatCompletionChunk.model_validate(chunk_data)
                            if chunk.choices and chunk.choices[0].delta.get("content"):
                                content = chunk.choices[0].delta["content"]
                                full_text += content
                                log_stream_chunk(model, content)
                                yield content
                        except json.JSONDecodeError:
                            continue
                # Log successful response
                log_model_response(model, full_text)
                return full_text
            except Exception as e:
                error_msg = str(e)
                log_model_response(model, error_msg, error=True)
                raise

        if stream:
            # For streaming, return the generator directly
            return process_stream()
        else:
            # For non-streaming, collect all chunks and return the complete text
            full_text = ""
            for chunk in process_stream():
                full_text += chunk
            return full_text


if __name__ == "__main__":
    # Example usage with a system message to simulate a real conversation context
    api_key = None
    model = "chat-llama-3-1-70b"
    messages = [
        {'role': 'system', 'content': 'You are a helpful AI assistant.'},
        {'role': 'user', 'content': 'Hello!'}
    ]
    
    print("Available models:", ", ".join(sorted(GliderAI.get_available_models())))
    print("\nTesting both streaming and non-streaming modes:")
    

    # For streaming output
    print("\nStreaming response:")
    for chunk in GliderAI.generate_text(model, messages, api_key=api_key, stream=True):
        print(chunk, end='', flush=True)