"""Report and user notes management system"""

from typing import Optional, Dict, List
from datetime import datetime
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.constants import ParseMode
from ..database.db import Database
from ..utils.decorators import admin_only, group_only
from ..utils.ai_moderator import AIModerator
import asyncio
import json
import random
import logging

logger = logging.getLogger(__name__)

class ReportHandler:
    def __init__(self, db: Database):
        self.db = db
        self.ai_mod = AIModerator()

    async def report(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Report a message to group admins"""
        if not update.message.reply_to_message:
            await update.message.reply_text(
                "❌ <b>How to Report Messages:</b>\n\n"
                "1️⃣ Reply to the message you want to report\n"
                "2️⃣ Type <code>/report [reason]</code>\n\n"
                "<i>Example: /report spam</i>",
                parse_mode=ParseMode.HTML
            )
            return

        chat_id = update.effective_chat.id
        reported_msg_id = update.message.reply_to_message.message_id
        reported_user = update.message.reply_to_message.from_user
        reason = " ".join(context.args) if context.args else "No reason provided"

        # Get usernames or mentions
        reporter_username = f"@{update.effective_user.username}" if update.effective_user.username else update.effective_user.mention_html()
        reported_username = f"@{reported_user.username}" if reported_user.username else reported_user.mention_html()

        # Store reason in bot_data for retrieval
        if not context.bot_data.get('report_reasons'):
            context.bot_data['report_reasons'] = {}
        context.bot_data['report_reasons'][str(reported_msg_id)] = reason

        # Create alert message with better formatting
        alert_message = (
            "🚨 <code>@admin</code>\n\n"
            "⚠️ <b>REPORT ALERT</b> ⚠️\n"
            "╔═════════════════╗\n"
            f"║ <b>Reporter:</b> {reporter_username}\n"
            f"║ <b>Reported:</b> {reported_username}\n"
            f"║ <b>Message ID:</b> {reported_msg_id}\n"
            "╚═════════════════╝\n\n"
            "<i>Use the buttons below to take action</i>"
        )

        # Create inline keyboard with moderation options
        keyboard = [
            [
                InlineKeyboardButton("📝 View Reason", callback_data=f"report_reason_{reported_msg_id}"),
                InlineKeyboardButton("🗑️ Delete Msg", callback_data=f"report_del_{reported_msg_id}")
            ],
            [
                InlineKeyboardButton("⚠️ Warn User", callback_data=f"report_warn_{reported_user.id}"),
                InlineKeyboardButton("🚫 Ban User", callback_data=f"report_ban_{reported_user.id}")
            ],
            [
                InlineKeyboardButton("✖️ Dismiss", callback_data=f"report_ignore_{reported_msg_id}")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        # Send the report message
        try:
            await context.bot.send_message(
                chat_id=chat_id,
                text=alert_message,
                reply_markup=reply_markup,
                reply_to_message_id=reported_msg_id,
                parse_mode=ParseMode.HTML
            )

            # Delete the original /report command message
            try:
                await update.message.delete()
            except:
                pass

        except Exception as e:
            await update.message.reply_text(
                "❌ Failed to send report. Please make sure I have proper permissions."
            )

    async def handle_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle report and abuse report callbacks"""
        query = update.callback_query
        data = query.data.split("_")

        if len(data) < 3:
            return

        # Check if user is admin
        user = await context.bot.get_chat_member(update.effective_chat.id, update.effective_user.id)
        if user.status not in ['administrator', 'creator']:
            await query.answer("❌ Only administrators can handle reports!", show_alert=True)
            return

        action = data[1]

        # Handle abuse report actions
        if data[0] == "abuse":
            msg_id = int(data[2])  # Convert to int
            user_id = int(data[3]) if len(data) > 3 else None  # Convert to int

            # Answer callback query first
            await query.answer()

            try:
                if action == "delwarn":
                    # Delete message
                    await context.bot.delete_message(chat_id=update.effective_chat.id, message_id=msg_id)

                    # Add warning
                    current_warns = await self.db.get_user_warn_count(update.effective_chat.id, user_id)
                    await self.db.add_warning(
                        chat_id=update.effective_chat.id,
                        user_id=user_id,
                        warned_by=update.effective_user.id,
                        reason="AI Moderation: Content violation"
                    )

                    # Get user mention
                    user_member = await context.bot.get_chat_member(update.effective_chat.id, user_id)
                    user_mention = user_member.user.mention_html()

                    # Update message
                    await query.edit_message_text(
                        f"✅ Message deleted and warning issued to {user_mention}\n"
                        f"Current warnings: {current_warns + 1}/5",
                        parse_mode=ParseMode.HTML
                    )

                    # Check if max warnings reached
                    if current_warns + 1 >= 5:
                        try:
                            await context.bot.ban_chat_member(update.effective_chat.id, user_id)
                            await query.edit_message_text(
                                f"🚫 {user_mention} has been banned for reaching maximum warnings.",
                                parse_mode=ParseMode.HTML
                            )
                        except Exception as e:
                            await query.edit_message_text(
                                f"⚠️ Failed to ban user after max warnings: {str(e)}",
                                parse_mode=ParseMode.HTML
                            )

                elif action == "delban":
                    # Delete message
                    await context.bot.delete_message(chat_id=update.effective_chat.id, message_id=msg_id)

                    # Ban user
                    user_member = await context.bot.get_chat_member(update.effective_chat.id, user_id)
                    user_mention = user_member.user.mention_html()
                    await context.bot.ban_chat_member(update.effective_chat.id, user_id)

                    await query.edit_message_text(
                        f"✅ Message deleted and {user_mention} has been banned.",
                        parse_mode=ParseMode.HTML
                    )

                elif action == "warn":
                    # Extract message ID from the callback data
                    msg_id = None
                    if len(data) >= 4:  # Check if message ID is included in the callback data
                        msg_id = data[3]

                    # Add warning
                    current_warns = await self.db.get_user_warn_count(update.effective_chat.id, user_id)
                    await self.db.add_warning(
                        chat_id=update.effective_chat.id,
                        user_id=user_id,
                        warned_by=update.effective_user.id,
                        reason="AI Moderation: Content violation"
                    )

                    # Get user mention
                    user_member = await context.bot.get_chat_member(update.effective_chat.id, user_id)
                    user_mention = user_member.user.mention_html()

                    await query.edit_message_text(
                        f"⚠️ Warning issued to {user_mention}\n"
                        f"Current warnings: {current_warns + 1}/5",
                        parse_mode=ParseMode.HTML
                    )

                    # Check if max warnings reached
                    if current_warns + 1 >= 5:
                        try:
                            await context.bot.ban_chat_member(update.effective_chat.id, user_id)
                            await query.edit_message_text(
                                f"🚫 {user_mention} has been banned for reaching maximum warnings.",
                                parse_mode=ParseMode.HTML
                            )
                        except Exception as e:
                            await query.edit_message_text(
                                f"⚠️ Failed to ban user after max warnings: {str(e)}",
                                parse_mode=ParseMode.HTML
                            )

                elif action == "dismiss":
                    # Handle false alarm
                    success, warning_msg_id = await self.ai_mod.handle_false_alarm(str(msg_id))
                    if success and warning_msg_id:
                        try:
                            await context.bot.delete_message(
                                chat_id=update.effective_chat.id,
                                message_id=int(warning_msg_id)
                            )
                        except:
                            pass

                    await query.edit_message_text(
                        f"✖️ Report dismissed by {update.effective_user.mention_html()}",
                        parse_mode=ParseMode.HTML
                    )

            except Exception as e:
                logger.error(f"Error handling abuse report action: {e}")
                await query.edit_message_text(
                    f"❌ Error performing action: {str(e)}",
                    parse_mode=ParseMode.HTML
                )
            return

        # Handle regular reports
        elif data[0] == "report":
            target_id = data[2]

            # Answer callback query first
            await query.answer()

            try:
                if action == "reason":
                    # Get reason from bot_data
                    reason = context.bot_data.get('report_reasons', {}).get(target_id, "Reason not found")

                    reason_message = (
                        "📝 <b>REPORT DETAILS</b>\n"
                        "╔═════════════════╗\n"
                        f"║ <b>Reason:</b> <i>{reason}</i>\n"
                        "╚═════════════════╝\n\n"
                        "<i>Choose an action below:</i>"
                    )

                    # Create comprehensive action buttons
                    keyboard = [
                        [
                            InlineKeyboardButton("🗑️ Delete Message", callback_data=f"report_del_{target_id}"),
                            InlineKeyboardButton("⚠️ Warn User", callback_data=f"report_warn_{target_id}")
                        ],
                        [
                            InlineKeyboardButton("🚫 Ban User", callback_data=f"report_ban_{target_id}"),
                            InlineKeyboardButton("✖️ Dismiss", callback_data=f"report_ignore_{target_id}")
                        ],
                        [
                            InlineKeyboardButton("🔙 Back", callback_data=f"report_back_{target_id}")
                        ]
                    ]
                    reply_markup = InlineKeyboardMarkup(keyboard)

                    await query.edit_message_text(
                        text=reason_message,
                        reply_markup=reply_markup,
                        parse_mode=ParseMode.HTML
                    )

                elif action == "back":
                    # Get original message info
                    reporter_username = query.message.reply_to_message.from_user.username if query.message.reply_to_message else "Unknown"
                    reported_username = query.message.reply_to_message.reply_to_message.from_user.username if query.message.reply_to_message and query.message.reply_to_message.reply_to_message else "Unknown"

                    # Recreate original message with enhanced formatting
                    alert_message = (
                        "🚨 <code>@admin</code>\n\n"
                        "⚠️ <b>REPORT ALERT</b> ⚠️\n"
                        "╔═════════════════╗\n"
                        f"║ <b>Reporter:</b> @{reporter_username}\n"
                        f"║ <b>Reported:</b> @{reported_username}\n"
                        f"║ <b>Message ID:</b> {target_id}\n"
                        "╚═════════════════╝\n\n"
                        "<i>Use the buttons below to take action</i>"
                    )

                    # Recreate original keyboard
                    keyboard = [
                        [
                            InlineKeyboardButton("📝 View Reason", callback_data=f"report_reason_{target_id}"),
                            InlineKeyboardButton("🗑️ Delete Msg", callback_data=f"report_del_{target_id}")
                        ],
                        [
                            InlineKeyboardButton("⚠️ Warn User", callback_data=f"report_warn_{target_id}"),
                            InlineKeyboardButton("🚫 Ban User", callback_data=f"report_ban_{target_id}")
                        ],
                        [
                            InlineKeyboardButton("✖️ Dismiss", callback_data=f"report_ignore_{target_id}")
                        ]
                    ]
                    reply_markup = InlineKeyboardMarkup(keyboard)

                    await query.edit_message_text(
                        text=alert_message,
                        reply_markup=reply_markup,
                        parse_mode=ParseMode.HTML
                    )

                elif action == "del":
                    # Delete the reported message
                    await context.bot.delete_message(chat_id=update.effective_chat.id, message_id=int(target_id))
                    await query.edit_message_text(
                        f"✅ Reported message has been deleted by {update.effective_user.mention_html()}",
                        parse_mode=ParseMode.HTML
                    )

                elif action == "warn":
                    # Get the user to warn from the reported message
                    reported_msg = await context.bot.get_message(update.effective_chat.id, int(target_id))
                    if reported_msg and reported_msg.from_user:
                        user_to_warn = reported_msg.from_user
                        # Add warning
                        current_warns = await self.db.get_user_warn_count(update.effective_chat.id, user_to_warn.id)
                        await self.db.add_warning(
                            chat_id=update.effective_chat.id,
                            user_id=user_to_warn.id,
                            warned_by=update.effective_user.id,
                            reason="Warned via report"
                        )
                        await query.edit_message_text(
                            f"⚠️ {user_to_warn.mention_html()} has been warned by {update.effective_user.mention_html()}\n"
                            f"Current warnings: {current_warns + 1}/5",
                            parse_mode=ParseMode.HTML
                        )
                    else:
                        await query.edit_message_text("❌ Could not find the reported message or user.")

                elif action == "ban":
                    # Get the user to ban from the reported message
                    reported_msg = await context.bot.get_message(update.effective_chat.id, int(target_id))
                    if reported_msg and reported_msg.from_user:
                        user_to_ban = reported_msg.from_user
                        # Ban user
                        await context.bot.ban_chat_member(update.effective_chat.id, user_to_ban.id)
                        await query.edit_message_text(
                            f"🚫 {user_to_ban.mention_html()} has been banned by {update.effective_user.mention_html()}",
                            parse_mode=ParseMode.HTML
                        )
                    else:
                        await query.edit_message_text("❌ Could not find the reported message or user.")

                elif action == "ignore":
                    await query.edit_message_text(
                        f"✖️ Report dismissed by {update.effective_user.mention_html()}",
                        parse_mode=ParseMode.HTML
                    )

            except Exception as e:
                logger.error(f"Error handling report action: {e}")
                await query.edit_message_text(
                    f"❌ Error performing action: {str(e)}",
                    parse_mode=ParseMode.HTML
                )

    async def report_abuse(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Report a message for abuse and check with AI moderator"""
        if not update.message.reply_to_message:
            await update.message.reply_text(
                "❌ <b>How to Report Abuse:</b>\n\n"
                "1️⃣ Reply to the abusive message\n"
                "2️⃣ Type <code>/reportabuse [detailed reason]</code>\n\n"
                "<b>Example Categories:</b>\n"
                "• Hate Speech\n"
                "• Harassment\n"
                "• NSFW Content\n"
                "• Spam/Scam\n"
                "• Violence\n\n"
                "<i>The more details you provide, the better our AI can help! 🤖</i>",
                parse_mode=ParseMode.HTML
            )
            return

        # Check if reason is provided
        if not context.args:
            await update.message.reply_text(
                "❌ <b>Please Provide a Reason!</b>\n\n"
                "Format: <code>/reportabuse [detailed reason]</code>\n\n"
                "<b>Good Example:</b>\n"
                "<code>/reportabuse This user is spamming crypto scam links</code>\n\n"
                "<b>Bad Example:</b>\n"
                "<code>/reportabuse bad message</code>\n\n"
                "<i>Detailed reasons help us take better action! 🎯</i>",
                parse_mode=ParseMode.HTML
            )
            return

        chat_id = update.effective_chat.id
        reported_msg = update.message.reply_to_message
        reported_msg_id = reported_msg.message_id
        reported_user = reported_msg.from_user
        reported_text = reported_msg.text or reported_msg.caption or ""
        reporter_id = update.effective_user.id
        user_reason = " ".join(context.args)

        # Get usernames or mentions
        reporter = f"@{update.effective_user.username}" if update.effective_user.username else update.effective_user.mention_html()
        reported = f"@{reported_user.username}" if reported_user.username else reported_user.mention_html()

        # Send initial response with progress updates
        progress_msg = await update.message.reply_text(
            "🔍 <b>Processing Abuse Report</b>\n\n"
            "⏳ <i>Phase 1/3: Analyzing message content...</i>",
            parse_mode=ParseMode.HTML
        )

        # Combine reported message and context for AI
        analysis_text = (
            f"REPORTED MESSAGE: {reported_text}\n"
            f"REPORTER'S REASON: {user_reason}\n"
            f"REPORTER: {reporter}\n"
            f"REPORTED USER: {reported}"
        )

        # Update progress
        await progress_msg.edit_text(
            "🔍 <b>Processing Abuse Report</b>\n\n"
            "✅ Phase 1/3: Message analyzed\n"
            "⏳ <i>Phase 2/3: AI evaluation in progress...</i>",
            parse_mode=ParseMode.HTML
        )

        # Check content with AI moderator
        is_inappropriate, details = await self.ai_mod.analyze_content(analysis_text, str(reporter_id), str(reported_msg_id))

        # Update progress
        await progress_msg.edit_text(
            "🔍 <b>Processing Abuse Report</b>\n\n"
            "✅ Phase 1/3: Message analyzed\n"
            "✅ Phase 2/3: AI evaluation complete\n"
            "⏳ <i>Phase 3/3: Taking action...</i>",
            parse_mode=ParseMode.HTML
        )

        if is_inappropriate and details:
            # Get current warns count
            current_warns = await self.db.get_user_warn_count(chat_id, reported_user.id)
            max_warns = 5  # Changed from 3 to 5 warnings

            # Format AI analysis results
            severity = int(details.get("severity", "2"))
            category = details.get('category', '').lower().replace('nsfw_', '')
            confidence = details.get('confidence', 0)
            reason = details.get('reason', '')

            # Get reaction emoji based on severity
            reaction = self.ai_mod.get_reaction_for_severity(severity)

            # Create action keyboard for admins
            keyboard = [
                [
                    InlineKeyboardButton(f"🗑️ Delete & Warn ({current_warns + 1}/{max_warns})",
                                       callback_data=f"abuse_delwarn_{reported_msg_id}_{reported_user.id}"),
                    InlineKeyboardButton("🚫 Delete & Ban",
                                       callback_data=f"abuse_delban_{reported_msg_id}_{reported_user.id}")
                ],
                [
                    InlineKeyboardButton(f"⚠️ Only Warn ({current_warns + 1}/{max_warns})",
                                       callback_data=f"abuse_warn_{reported_user.id}_{reported_msg_id}"),
                    InlineKeyboardButton("✖️ Dismiss",
                                       callback_data=f"abuse_dismiss_{reported_msg_id}")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            # Format found words as spoiler if available
            found_words = details.get('found_words', [])
            found_words_text = ", ".join(found_words) if found_words else ""
            found_words_section = f"\n\n<b>Found Words:</b> <tg-spoiler>{found_words_text}</tg-spoiler>" if found_words_text else ""

            violation_msg = (
                f"{reaction} <b>VIOLATION DETECTED!</b>\n\n"
                f"<b>Reporter:</b> {reporter}\n"
                f"<b>Reported:</b> {reported}\n\n"
                f"<b>AI Analysis:</b>\n"
                f"• Category: {category.title()}\n"
                f"• Severity: {'🔴' * severity}{'⚪' * (5-severity)}\n"
                f"• Confidence: {confidence}%\n"
                f"• Reason: {reason}{found_words_section}\n\n"
                f"<b>Reporter's Note:</b>\n<i>{user_reason}</i>\n\n"
                f"<b>Current Status:</b>\n"
                f"• Warnings: {current_warns}/{max_warns}\n"
                f"<b>Recommended Action:</b>\n"
                f"{'🚫 Immediate Ban' if severity >= 4 or current_warns >= max_warns - 1 else '⚠️ Warning'}"
            )

            # Send violation message to admins
            admin_msg = await context.bot.send_message(
                chat_id=chat_id,
                text=violation_msg,
                reply_markup=reply_markup,
                reply_to_message_id=reported_msg_id,
                parse_mode=ParseMode.HTML
            )

            # Track warning message for potential cleanup
            self.ai_mod.track_warning_message(str(reported_msg_id), str(admin_msg.message_id), severity)

            # Thank the reporter
            thank_you = (
                f"✅ <b>Thanks {reporter}!</b>\n\n"
                "Our AI has confirmed this violation and admins have been notified.\n"
                f"Severity Level: {'🔴' * severity}{'⚪' * (5-severity)}\n"
                f"Category: {category.title()}\n\n"
                "<i>Keep helping us maintain a safe community! 💪</i>"
            )

            await progress_msg.edit_text(thank_you, parse_mode=ParseMode.HTML)

            # Log the moderation result
            self.ai_mod.log_moderation_result(reported_text, True, details)

            # Process AI response for future training
            await self.ai_mod.process_ai_response(reported_text, details.get('full_response', ''))

            # Schedule message cleanup
            async def cleanup_messages():
                await asyncio.sleep(30)
                try:
                    await progress_msg.delete()
                    await update.message.delete()
                except Exception:
                    pass

            asyncio.create_task(cleanup_messages())

        else:
            # No violation found
            no_violation_msg = (
                "✅ <b>Analysis Complete</b>\n\n"
                f"The reported message from {reported} was reviewed:\n\n"
                "🤖 <b>AI Verdict:</b> No violation detected\n"
                f"📝 <b>Report Reason:</b> <i>{user_reason}</i>\n\n"
                "<i>Thanks for staying vigilant! If you still believe this content violates our rules, please contact an admin directly.</i>"
            )

            await progress_msg.edit_text(no_violation_msg, parse_mode=ParseMode.HTML)

            # Log the false positive
            self.ai_mod.log_moderation_result(reported_text, False, None)

            # Schedule cleanup
            async def cleanup_messages():
                await asyncio.sleep(15)
                try:
                    await progress_msg.delete()
                    await update.message.delete()
                except Exception:
                    pass

            asyncio.create_task(cleanup_messages())

    @admin_only()
    @group_only()
    async def add_note(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Add a note about a user"""
        if not context.args or len(context.args) < 2:
            await update.message.reply_text(
                "Usage: /addnote [username/user_id] [note text]"
            )
            return

        target = context.args[0]
        note_text = " ".join(context.args[1:])

        try:
            if target.startswith("@"):
                user = await context.bot.get_chat(target)
                user_id = user.id
            else:
                user_id = int(target)
                user = await context.bot.get_chat_member(update.effective_chat.id, user_id)
                user = user.user
        except Exception:
            await update.message.reply_text("Unable to find user. Please provide a valid username or ID.")
            return

        note_data = {
            "user_id": user_id,
            "admin_id": update.effective_user.id,
            "note": note_text,
            "timestamp": datetime.now().isoformat()
        }
        await self.db.add_user_note(note_data)

        await update.message.reply_text(
            f"✅ Note added for user {user.mention_html()}",
            parse_mode=ParseMode.HTML
        )

    @admin_only()
    @group_only()
    async def get_notes(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Get all notes about a user"""
        if not context.args:
            await update.message.reply_text(
                "Usage: /getnotes [username/user_id]"
            )
            return

        target = context.args[0]

        try:
            if target.startswith("@"):
                user = await context.bot.get_chat(target)
                user_id = user.id
            else:
                user_id = int(target)
                user = await context.bot.get_chat_member(update.effective_chat.id, user_id)
                user = user.user
        except Exception:
            await update.message.reply_text("Unable to find user. Please provide a valid username or ID.")
            return

        notes = await self.db.get_user_notes(user_id)
        if not notes:
            await update.message.reply_text(f"No notes found for {user.mention_html()}")
            return

        response = f"📝 Notes for {user.mention_html()}:\n\n"
        for note in notes:
            admin = await context.bot.get_chat_member(update.effective_chat.id, note["admin_id"])
            timestamp = datetime.fromisoformat(note["timestamp"]).strftime("%Y-%m-%d %H:%M:%S")
            response += (
                f"• {note['note']}\n"
                f"  Added by {admin.user.mention_html()} on {timestamp}\n\n"
            )

        await update.message.reply_text(response, parse_mode=ParseMode.HTML)

    @admin_only()
    @group_only()
    async def clear_notes(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Clear all notes about a user"""
        if not context.args:
            await update.message.reply_text(
                "Usage: /clearnotes [username/user_id]"
            )
            return

        target = context.args[0]

        try:
            if target.startswith("@"):
                user = await context.bot.get_chat(target)
                user_id = user.id
            else:
                user_id = int(target)
                user = await context.bot.get_chat_member(update.effective_chat.id, user_id)
                user = user.user
        except Exception:
            await update.message.reply_text("Unable to find user. Please provide a valid username or ID.")
            return

        await self.db.clear_user_notes(user_id)
        await update.message.reply_text(
            f"✅ All notes cleared for user {user.mention_html()}",
            parse_mode=ParseMode.HTML
        )
