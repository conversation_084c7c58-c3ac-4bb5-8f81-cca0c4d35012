from telegram.constants import <PERSON><PERSON>Mode
import os
import html
import traceback
import asyncio
import nest_asyncio
import tracemalloc
from typing import List
from datetime import datetime
from telegram import Update, Message, BotCommand, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.constants import <PERSON><PERSON><PERSON><PERSON>ber<PERSON>tat<PERSON>, ChatType
from telegram.ext import (
    Application,
    CommandHandler,
    MessageHandler,
    CallbackQueryHandler,
    ChatMemberHandler,
    ContextTypes,
    filters,
    JobQueue
)
from src.features.broadcast_handler import PushHandler  # Add this import
from telegram.error import TelegramError
from dotenv import load_dotenv
import logging

from src.features.yt_command_handler import YouTubeCommandHandler
from telegram.ext import CallbackQueryHandler
from src.features.felo_handler import FeloHandler  # Add this import
from src.features.imagen_handler import setup as setup_imagen  # Add this import
from src.features.appeal_handler import AppealHandler  # Add this import
from src.features.auto_responder import AutoResponder
from src.features.admin_mention_handler import AdminMentionHandler

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

from src.database.db import Database
from src.database.tracker import db_tracker

# Features
from src.features.admin_tools import AdminTools
from src.features.group_manager import GroupManager
from src.features.group_settings import GroupSettings
from src.features.log_handler import LogHandler
from src.features.user_handler import UserHandler
from src.features.utility_handler import UtilityHandler
from src.features.admin_reference import AdminCommandReference
from src.features.translation_handler import TranslationHandler
from src.features.broadcast_handler import PushHandler
from src.features.report_handler import ReportHandler
from src.features.fun_handler import FunHandler
from src.features.appeal_handler import AppealHandler
from src.features.felo_handler import FeloHandler
from src.features.group_settings.protection import ProtectionHandler
from src.features.raid_protection import RaidProtection
from src.features.greeting_handler import GreetingHandler
from src.features.ai_chat import AIChat
from src.features.zombies import ZombieHandler
from src.features.vote_ban_handler import VoteBanHandler
from src.features.summary_handler import SummaryHandler
from src.features.transcription.transcription_handler import TranscriptionHandler
from src.features.captcha_handler import CaptchaHandler  # Add this import

# Import filter_lists functions
from src.config.filter_lists import load_nsfw_words, save_nsfw_words

# Handlers
from src.handlers.api_handlers import APICommandHandlers
from src.handlers.media_handlers import MediaCommandHandlers
from src.handlers.utility_handlers import UtilityCommandHandlers
from src.handlers.personal_handlers import PersonalCommandHandlers

# Utils
from src.utils.scheduler import TaskScheduler

# Config
from src.config import Config

# Load environment variables
load_dotenv()

# Initialize components
config = Config()
db = Database()
admin_tools = AdminTools(db)
user_handler = UserHandler(db)
utility_handler = UtilityHandler(db)
admin_reference = AdminCommandReference(db)
translation_handler = TranslationHandler()  # Initialize without passing application
group_settings = GroupSettings(db, admin_tools)
group_manager = GroupManager(db)
report_handler = ReportHandler(db)
log_handler = LogHandler(db)
personal_handlers = PersonalCommandHandlers()
appeal_handler = AppealHandler(db)
greeting_handler = GreetingHandler()
ai_chat = AIChat(db)
youtube_handler = YouTubeCommandHandler()
transcription_handler = TranscriptionHandler()
push_handler = PushHandler(db)
auto_responder = AutoResponder(db)
captcha_handler = CaptchaHandler(db)  # Add this line
admin_mention_handler = AdminMentionHandler()


def escape_markdown_v2(text: str) -> str:
    """Escape special characters for Markdown V2"""
    special_chars = ['_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!']
    for char in special_chars:
        text = text.replace(char, f'\\{char}')
    return text


async def setup_commands(application: Application, update: Update = None) -> None:
    """Set up bot commands."""

    commands = [
        # User Commands
        BotCommand("start", "Start the bot and get welcome message 👋"),
        BotCommand("help", "Get help about bot features and commands ℹ️"),
        BotCommand("id", "Get user/chat ID 🔢"),
        BotCommand("ping", "Check bot's response time 🏓"),
        BotCommand("arhelp", "Auto responder help 🤖"),

        # Admin Commands
        BotCommand("admin", "Show admin command list 👮"),
        BotCommand("mute", "Mute user (Reply + /mute [time]) 🤫"),
        BotCommand("unmute", "Unmute a user 🔊"),
        BotCommand("ban", "Ban user (Reply/Username/ID + reason) ⛔"),
        BotCommand("tban", "Temporary ban user (Reply + /tban [time]) ⏳"),
        BotCommand("sban", "Soft ban user (Reply/Username/ID + reason) 🌊"),
        BotCommand("unban", "Unban a previously banned user ✅"),
        BotCommand("warn", "Warn a user ⚠️"),
        BotCommand("unwarn", "Remove a warning from user 🔄"),
        BotCommand("purge", "Delete messages in bulk (Reply) 🗑️"),
        BotCommand("del", "Delete a message (Reply) 🗑️"),
        BotCommand("pin", "Pin message (Reply + flags: -q, -u) 📌"),
        BotCommand("unpin", "Unpin a message 📍"),
        BotCommand("unpinall", "Unpin all messages 📑"),


        # Group Settings
        BotCommand("setwelcome", "Set welcome message 👋"),
        BotCommand("togglewelcome", "Toggle welcome message on/off 🔄"),
        BotCommand("setrules", "Set group rules 📜"),
        BotCommand("rules", "View group rules 📖"),
        BotCommand("setflood", "Set anti-flood settings 🌊"),
        BotCommand("nsfw", "Master protection control 🛡️"),
        BotCommand("setlogchannel", "Set logging channel ��"),

        # Translation
        # BotCommand("tr", "Translate text to another language 🌐"),
        # BotCommand("tl", "Translate last message to another language 🌐"),
        # BotCommand("languages", "Show available languages 🗣️"),

        # AI Features
        BotCommand("webai", "Use web AI features 🤖"),
        BotCommand("transcribe", "Transcribe and analyze YouTube videos 🎥"),
        BotCommand("stt", "Convert voice/audio to text 🎙️"),
        # Utility Commands
        # BotCommand("weather", "Get weather info ⛅"),
        # BotCommand("crypto", "Get crypto prices 💰"),
        # BotCommand("news", "Get latest news ��"),
        # BotCommand("movie", "Get movie information 🎬"),
        # BotCommand("recipe", "Get food recipes 🍳"),
        # BotCommand("space", "Get space-related info 🚀"),
        # BotCommand("wiki", "Search Wikipedia 📚"),
        # BotCommand("define", "Get word definitions 📖"),
        # BotCommand("github", "Get GitHub user/repo info 💻"),
        # BotCommand("riddle", "Get a random riddle ���"),
        # BotCommand("technews", "Get tech news 📱"),
        # BotCommand("shorten", "Shorten URLs 🔗"),
        # BotCommand("imagen", "Generate AI images from text 🖼️"),

        # # API Commands
        # BotCommand("quote", "Get random quotes ��"),
        # BotCommand("fact", "Get random facts ℹ️"),
        # BotCommand("dog", "Get dog pictures 🐕"),
        # BotCommand("catfact", "Get cat facts 🐱"),
        # BotCommand("exchange", "Get currency exchange rates 💱"),
        # BotCommand("numberfact", "Get facts about numbers 🔢"),

        # Media Commands

        BotCommand("qr", "Generate QR codes 📱"),


        # Support Commands
        BotCommand("report", "Report issues to admins 🚨"),
        BotCommand("reportabuse", "Report abuse to admins 🚨"),
        BotCommand("appeal", "Appeal a ban/mute 🙏"),

        # New Admin Commands
        BotCommand("promote", "Promote user with custom title 🎖"),
        BotCommand("demote", "Remove admin rights 👎"),
        BotCommand("poll", "Create advanced polls ⏲️"),
        BotCommand("raidmode", "Toggle anti-raid protection 🛡️"),

        # Filter Management
        BotCommand("filter", "Show help for filter commands"),
        BotCommand("filter_add", "Add a word to the NSFW filter"),
        BotCommand("filter_remove", "Remove a word from the NSFW filter"),
        BotCommand("filter_list", "List all words in the NSFW filter"),

        # Fun Commands (Private Chat Only)
        BotCommand("dice", "Roll a dice (DM only) 🎲"),
        BotCommand("roll", "Roll a dice - alias (DM only) 🎲"),
        BotCommand("flip", "Flip a coin (DM only) 🪙"),
        BotCommand("coin", "Flip a coin - alias (DM only) 🪙"),
        BotCommand("hug", "Give someone a hug (DM only) 🤗"),
        BotCommand("joke", "Get a random joke (DM only) 😄"),
        BotCommand("fortune", "Get your fortune (DM only) 🔮"),
        BotCommand("roast", "Roast someone (DM only) 🔥"),
        BotCommand("tod", "Play Truth or Dare (DM only) 🎮"),
        BotCommand("truthordare", "Play Truth or Dare - alias (DM only) 🎮"),
        BotCommand("rps", "Play Rock Paper Scissors (DM only) ✊"),
        BotCommand("fun", "Show all fun commands (DM only) 🎮"),

        # Music Commands
        BotCommand("play", "Play a song in voice chat (e.g., /play song name)"),
        BotCommand("stop", "Stop current music playback"),

        # Transcription Commands
        BotCommand("stt", "Convert voice/audio to text 🎙️"),
        BotCommand("transcribe", "Convert voice/audio to text (alias) 🎙️"),
        BotCommand("transcribe_yt", "Transcribe YouTube videos 🎥"),

        # Owner Commands
        BotCommand("bc", "Broadcast message to all chats 📢"),
        BotCommand("syncchats", "Sync chat list for broadcast 🔄"),
        BotCommand("verifysync", "Verify synced chats and their status 🔍"),
        BotCommand("push", "Push replied content to groups 📤"),
        BotCommand("userstats", "Show bot usage statistics 📊"),
        BotCommand("ohelp", "Owner command list 👑"),

        # Add morning greeting commands
        BotCommand("schedule_morning", "Schedule daily morning greetings 📅"),
    ]

    try:
        # Set commands globally
        await application.bot.set_my_commands(commands)
        print("Bot commands have been set up successfully!")
    except Exception as e:
        print(f"Error setting up commands: {e}")

    if update:
        await update.message.reply_text("✅ Bot commands have been updated successfully! Use /help to see all commands.")


async def handle_new_chat(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle new members joining or leaving a chat."""
    chat = update.effective_chat

    if not chat:
        return

    # Initialize database
    db = Database()

    # Check if it's a new member event
    if update.message and update.message.new_chat_members:
        # Check if our bot is among the new members
        bot_joined = any(member.id == context.bot.id for member in update.message.new_chat_members)

        if bot_joined:
            # Bot was added to a new chat
            logger.info(f"Bot added to chat: {chat.title} ({chat.id})")

            try:
                # Get chat administrators to check bot's role
                chat_admins = await context.bot.get_chat_administrators(chat.id)
                bot_member = next((admin for admin in chat_admins if admin.user.id == context.bot.id), None)

                # Store chat in database with admin status
                await db.add_chat(
                    chat_id=chat.id,
                    title=chat.title,
                    chat_type=chat.type
                )

                # Send professional welcome message
                keyboard = [
                    [
                        InlineKeyboardButton("🚀 Get Started", callback_data="bot_get_started"),
                        InlineKeyboardButton("⚙️ Admin Panel", callback_data="bot_admin_panel")
                    ],
                    [
                        InlineKeyboardButton("📚 Help Guide", callback_data="bot_help"),
                        InlineKeyboardButton("🛡️ Protection Setup", callback_data="bot_protection")
                    ],
                    [
                        InlineKeyboardButton("🌐 Official Channel", url="https://t.me/HelpingAI_Official"),
                        InlineKeyboardButton("💬 Support", url="https://t.me/HelpingAI_Support")
                    ]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                welcome_text = (
                    f"╭─「 <b>🤖 HelpingAI BOT (AI ADMIN)</b> 」\n"
                    f"│\n" 
                    f"│ 🎉 <b>Successfully added to {chat.title}!</b>\n"
                    f"│\n"
                    f"├─「 <b>✨ What I Can Do</b> 」\n"
                    f"│ 🛡️ <b>Advanced Protection</b> - AI-powered moderation\n"
                    f"│ 👥 <b>User Management</b> - Professional admin tools\n"
                    f"│ 🤖 <b>Smart Automation</b> - Intelligent group assistance\n"
                    f"│ 📊 <b>Analytics & Logs</b> - Comprehensive monitoring\n"
                    f"│\n"
                    f"├─「 <b>🚀 Quick Setup</b> 」\n"
                    f"│ 🔹 Grant me admin rights for full functionality\n"
                    f"│ 🔹 Use /admin to access the control panel\n"
                    f"│ 🔹 Configure protection with /nsfw\n"
                    f"│ 🔹 Set welcome messages with /setwelcome\n"
                    f"│\n"
                    f"├─「 <b>📋 Essential Commands</b> 」\n"
                    f"│ • <code>/help</code> - Complete feature guide\n"
                    f"│ • <code>/admin</code> - Admin control panel\n"
                    f"│ • <code>/rules</code> - View group rules\n"
                    f"│ • <code>/settings</code> - Group configuration\n"
                    f"│\n"
                    f"╰─「 <i>Ready to protect and serve! 🌟</i> 」"
                )
                
                if bot_member and not bot_member.can_post_messages:
                    welcome_text += (
                        f"\n\n⚠️ <b>Permission Warning:</b>\n"
                        f"I need message posting permissions to function properly.\n"
                        f"Please grant admin rights for optimal performance."
                    )

                await context.bot.send_message(
                    chat.id, 
                    welcome_text,
                    parse_mode=ParseMode.HTML,
                    reply_markup=reply_markup,
                    disable_web_page_preview=True
                )

            except Exception as e:
                logger.error(f"Error handling new chat join: {str(e)}")
        else:
            # Regular users joined, now check if captcha is enabled
            captcha_enabled = await db.get_group_setting(chat.id, "captcha_enabled", False)

            if captcha_enabled:
                # If captcha is enabled, let the captcha handler manage the flow
                # It will show the welcome message after successful verification
                from src.features.captcha_handler import CaptchaHandler
                captcha_handler = CaptchaHandler(db)
                await captcha_handler.handle_new_member(update, context)
            else:
                # If captcha is disabled, directly show welcome message
                from src.features.group_settings.welcome import WelcomeHandler
                welcome_handler = WelcomeHandler(db)
                await welcome_handler.handle_new_member(update, context)

    # Check if it's a member left event
    elif update.message and update.message.left_chat_member:
        if update.message.left_chat_member.id == context.bot.id:
            # Bot was removed from chat
            logger.info(f"Bot removed from chat: {chat.title} ({chat.id})")

            # Remove chat from database
            await db.remove_chat(chat.id)
        else:
            # Regular user left, handle goodbye message
            try:
                # Import here to avoid circular imports
                from src.features.group_settings.welcome import WelcomeHandler, DEFAULT_GOODBYE

                # Get the leaving member
                left_member = update.message.left_chat_member
                if left_member.is_bot:
                    return

                # Get member count
                member_count = await chat.get_member_count()

                # Format goodbye message
                goodbye_msg = await db.get_goodbye_message(chat.id) or DEFAULT_GOODBYE
                formatted_msg = goodbye_msg.format(
                    mention=left_member.mention_html(),
                    chat_title=html.escape(chat.title),
                    count=member_count,
                    username=html.escape(left_member.username or "No username"),
                    first_name=html.escape(left_member.first_name),
                    last_name=html.escape(left_member.last_name or ""),
                    id=left_member.id
                )

                # Send goodbye message
                await context.bot.send_message(
                    chat_id=chat.id,
                    text=formatted_msg,
                    parse_mode=ParseMode.HTML
                )

            except Exception as e:
                logger.error(f"Error handling goodbye message for leaving member: {str(e)}")
                logger.error(traceback.format_exc())

async def error_handler(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle errors in the bot"""
    pass

    try:
        # Get the full traceback
        tb_list = traceback.format_exception(None, context.error, context.error.__traceback__)
        tb_string = ''.join(tb_list)

        # Create a user-friendly error message
        error_msg = (
            "❌ <b>An error occurred while processing your request.</b>\n\n"
            f"<i>Error details:</i>\n"
            f"<code>{escape_markdown_v2(str(context.error))}</code>"
        )

        if update and update.effective_message:
            # Send error message to the chat
            await update.effective_message.reply_text(
                error_msg,
                parse_mode=ParseMode.HTML
            )

            # If it's a group, also print the error details
            if update.effective_chat.type in ['group', 'supergroup']:
                chat_info = (
                    f"• Chat: {update.effective_chat.title} ({update.effective_chat.id})\n"
                    f"• User: {update.effective_user.name} ({update.effective_user.id})\n"
                    f"• Message: {update.effective_message.text}\n"
                    f"• Error: {context.error}\n"
                    f"• Traceback:\n{tb_string}"
                )
                pass

    except Exception as e:
        pass


async def handle_private_messages(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle messages in private chat"""
    if not update.effective_message:
        return



async def handle_message(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle regular text messages"""
    if not update.message or not update.effective_chat:
        return

    try:
        chat_type = update.effective_chat.type
        user = update.effective_user
        chat = update.effective_chat

        # Track user in database
        if user:
            db_tracker.track_user(
                user_id=user.id,
                username=user.username,
                first_name=user.first_name,
                last_name=user.last_name
            )
        
        # Track server/chat in database if it's a group
        if chat_type in [ChatType.GROUP, ChatType.SUPERGROUP]:
            db_tracker.track_server(
                server_id=chat.id,
                title=chat.title,
                description=chat.description if hasattr(chat, 'description') else None
            )
            
            # Track server member
            if user:
                is_admin = False
                try:
                    member = await chat.get_member(user.id)
                    # Check if member is admin or creator
                    is_admin = member.status in ["administrator", "creator"]
                except TelegramError:
                    pass
                
                db_tracker.track_server_member(
                    server_id=chat.id,
                    user_id=user.id,
                    is_admin=is_admin
                )

        # Skip processing for private chats (let AI chat handler process them first)
        if chat_type == ChatType.PRIVATE:
            # Don't call personal_handlers.handle_private_command here
            # Let the AI chat handler process DMs first
            pass
        else:
            # Continue with group message processing
            pass

        # Get message text for logging
        message_text = update.message.text or update.message.caption or ""
        
        # Track message in database
        has_media = bool(update.message.photo or update.message.video or 
                        update.message.audio or update.message.document or 
                        update.message.voice or update.message.video_note)
        
        media_type = None
        if update.message.photo:
            media_type = "photo"
        elif update.message.video:
            media_type = "video"
        elif update.message.audio:
            media_type = "audio"
        elif update.message.document:
            media_type = "document"
        elif update.message.voice:
            media_type = "voice"
        elif update.message.video_note:
            media_type = "video_note"
        
        if chat_type in [ChatType.GROUP, ChatType.SUPERGROUP] and user:
            db_tracker.track_message(
                chat_id=chat.id,
                user_id=user.id,
                message_text=message_text,
                message_type="text" if not has_media else "media",
                has_media=has_media,
                media_type=media_type
            )

        # Direct check for explicit NSFW words
        if message_text and chat_type in [ChatType.GROUP, ChatType.SUPERGROUP]:
            # Check for obvious NSFW words
            nsfw_words = ["fuck", "penis", "cock", "ass", "pussy", "sex", "dick", "vagina"]
            message_lower = message_text.lower()
            found_word = None

            for word in nsfw_words:
                if word in message_lower:
                    found_word = word
                    break

            if found_word:
                # Process with protection handler directly
                try:
                    if 'protection_handler' in context.bot_data:
                        await context.bot_data['protection_handler'].handle_message(update, context)
                except Exception as e:
                    logger.error(f"Error in protection handler: {e}")
                    logger.error(traceback.format_exc())

                # Return early, no need to process further
                return

        # Process with regular protection handler if no explicit NSFW words found
        try:
            if 'protection_handler' in context.bot_data:
                await context.bot_data['protection_handler'].handle_message(update, context)
        except Exception as e:
            logger.error(f"Error in protection handler: {e}")
            logger.error(traceback.format_exc())

        # Process group messages using group_settings handler
        if chat_type == ChatType.GROUP or chat_type == ChatType.SUPERGROUP:
            if hasattr(group_settings, 'handle_message') and callable(group_settings.handle_message):
                await group_settings.handle_message(update, context)

    except Exception as e:
        logger.error(f"Error in handle_message: {e}")
        logger.error(traceback.format_exc())

async def handle_media(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle media messages (photos, videos, documents)"""
    if not update.effective_message or not update.effective_chat:
        return

    try:
        await db.track_user_activity(
            update.effective_chat.id,
            update.effective_user.id,
            "media"
        )
    except Exception as e:
        logger.error(f"Error tracking media: {e}")

async def handle_sticker(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle sticker messages"""
    if not update.effective_message or not update.effective_chat:
        return

    try:
        await db.track_user_activity(
            update.effective_chat.id,
            update.effective_user.id,
            "sticker"
        )
    except Exception as e:
        logger.error(f"Error tracking sticker: {e}")

async def delete_messages_after_delay(messages: List[Message], delay: int = 60):
    """Delete messages after a specified delay in seconds."""
    await asyncio.sleep(delay)
    for message in messages:
        try:
            await message.delete()
        except TelegramError:
            pass

async def filter_add(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Add a word to the NSFW filter list. Admin only."""
    if not update.effective_message or not update.effective_user:
        return

    chat_id = update.effective_chat.id
    user_id = update.effective_user.id
    messages_to_delete = []

    # Add command message to deletion list
    if update.message:
        messages_to_delete.append(update.message)

    # Check if user is admin
    try:
        user = await context.bot.get_chat_member(chat_id, user_id)
        if user.status not in [ChatMemberStatus.ADMINISTRATOR, ChatMemberStatus.OWNER]:
            response = await update.message.reply_text("❌ This command is only available to administrators.")
            messages_to_delete.append(response)
            asyncio.create_task(delete_messages_after_delay(messages_to_delete))
            return
    except TelegramError:
        response = await update.message.reply_text("❌ Failed to verify admin status.")
        messages_to_delete.append(response)
        asyncio.create_task(delete_messages_after_delay(messages_to_delete))
        return

    # Get the word to add
    if not context.args:
        response = await update.message.reply_text("❌ Please specify a word to add to the filter.\nUsage: /filter_add <word>")
        messages_to_delete.append(response)
        asyncio.create_task(delete_messages_after_delay(messages_to_delete))
        return

    word = context.args[0].lower()

    # Load current words
    nsfw_words = load_nsfw_words()

    # Check if word already exists
    if word in nsfw_words:
        response = await update.message.reply_text(f"❌ The word '{word}' is already in the filter list.")
        messages_to_delete.append(response)
        asyncio.create_task(delete_messages_after_delay(messages_to_delete))
        return

    # Add word and save
    nsfw_words.add(word)
    if save_nsfw_words(nsfw_words):
        response = await update.message.reply_text(f"✅ Added '{word}' to the filter list.")
    else:
        response = await update.message.reply_text("❌ Failed to save the filter list.")

    messages_to_delete.append(response)
    asyncio.create_task(delete_messages_after_delay(messages_to_delete))

async def filter_remove(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Remove a word from the NSFW filter list. Admin only."""
    if not update.effective_message or not update.effective_user:
        return

    chat_id = update.effective_chat.id
    user_id = update.effective_user.id
    messages_to_delete = []

    # Add command message to deletion list
    if update.message:
        messages_to_delete.append(update.message)

    # Check if user is admin
    try:
        user = await context.bot.get_chat_member(chat_id, user_id)
        if user.status not in [ChatMemberStatus.ADMINISTRATOR, ChatMemberStatus.OWNER]:
            response = await update.message.reply_text("❌ This command is only available to administrators.")
            messages_to_delete.append(response)
            asyncio.create_task(delete_messages_after_delay(messages_to_delete))
            return
    except TelegramError:
        response = await update.message.reply_text("❌ Failed to verify admin status.")
        messages_to_delete.append(response)
        asyncio.create_task(delete_messages_after_delay(messages_to_delete))
        return

    # Get the word to remove
    if not context.args:
        response = await update.message.reply_text("❌ Please specify a word to remove from the filter.\nUsage: /filter_remove <word>")
        messages_to_delete.append(response)
        asyncio.create_task(delete_messages_after_delay(messages_to_delete))
        return

    word = context.args[0].lower()

    # Load current words
    nsfw_words = load_nsfw_words()

    # Check if word exists
    if word not in nsfw_words:
        response = await update.message.reply_text(f"❌ The word '{word}' is not in the filter list.")
        messages_to_delete.append(response)
        asyncio.create_task(delete_messages_after_delay(messages_to_delete))
        return

    # Remove word and save
    nsfw_words.remove(word)
    if save_nsfw_words(nsfw_words):
        response = await update.message.reply_text(f"✅ Removed '{word}' from the filter list.")
    else:
        response = await update.message.reply_text("❌ Failed to save the filter list.")

    messages_to_delete.append(response)
    asyncio.create_task(delete_messages_after_delay(messages_to_delete))

async def filter_list(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """List all words in the NSFW filter. Admin only."""
    if not update.effective_message or not update.effective_user:
        return

    chat_id = update.effective_chat.id
    user_id = update.effective_user.id
    messages_to_delete = []

    # Add command message to deletion list
    if update.message:
        messages_to_delete.append(update.message)

    # Check if user is admin
    try:
        user = await context.bot.get_chat_member(chat_id, user_id)
        if user.status not in [ChatMemberStatus.ADMINISTRATOR, ChatMemberStatus.OWNER]:
            response = await update.message.reply_text("❌ This command is only available to administrators.")
            messages_to_delete.append(response)
            asyncio.create_task(delete_messages_after_delay(messages_to_delete))
            return
    except TelegramError:
        response = await update.message.reply_text("❌ Failed to verify admin status.")
        messages_to_delete.append(response)
        asyncio.create_task(delete_messages_after_delay(messages_to_delete))
        return

    # Load and display words
    nsfw_words = load_nsfw_words()
    if not nsfw_words:
        response = await update.message.reply_text("📝 The filter list is empty.")
        messages_to_delete.append(response)
        asyncio.create_task(delete_messages_after_delay(messages_to_delete))
        return

    # Format the list with word count
    word_list = sorted(nsfw_words)
    response = f"📝 Filter List ({len(word_list)} words):\n\n"
    response += "\n".join(f"• {word}" for word in word_list)

    # Split message if too long
    if len(response) > 4000:
        chunks = [response[i:i+4000] for i in range(0, len(response), 4000)]
        for chunk in chunks:
            msg = await update.message.reply_text(chunk)
            messages_to_delete.append(msg)
    else:
        msg = await update.message.reply_text(response)
        messages_to_delete.append(msg)

    asyncio.create_task(delete_messages_after_delay(messages_to_delete))

async def filter_help(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Show help for filter commands."""
    if not update.effective_message:
        return

    help_text = (
        "<b>🛡️ Filter Management Commands</b>\n\n"
        "<b>Admin Commands:</b>\n"
        "• /filter_add <code>[word]</code> - Add a word to the filter\n"
        "• /filter_remove <code>[word]</code> - Remove a word from the filter\n"
        "• /filter_list - Show all filtered words\n\n"
        "<b>Examples:</b>\n"
        "• /filter_add badword\n"
        "• /filter_remove badword\n\n"
        "<i>Note: Only administrators can use these commands.\nAll responses will be deleted after 60 seconds.</i>"
    )

    messages_to_delete = []
    if update.message:
        messages_to_delete.append(update.message)
        response = await update.message.reply_text(
            help_text,
            parse_mode=ParseMode.HTML
        )
        messages_to_delete.append(response)
        asyncio.create_task(delete_messages_after_delay(messages_to_delete))

async def coming_soon_handler(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle commands that are coming soon."""
    if not update.effective_message:
        return

    command = update.message.text.split()[0][1:]  # Remove the '/' from command

    # Send the coming soon message
    response = await update.message.reply_text(f"🔜 The /{command} feature is coming soon! Stay tuned for updates!")

    # Delete both the command message and response after 5 seconds
    try:
        # Schedule deletion of the command message
        asyncio.create_task(
            _delete_message_later(context, update.effective_chat.id, update.message.message_id, 5)
        )
        # Schedule deletion of the response message
        asyncio.create_task(
            _delete_message_later(context, update.effective_chat.id, response.message_id, 5)
        )
    except Exception as e:
        logger.error(f"Error scheduling message deletion: {e}")

async def _delete_message_later(context: ContextTypes.DEFAULT_TYPE, chat_id: int,
                               message_id: int, delay: int = 60) -> None:
    """Delete a message after a specified delay in seconds."""
    await asyncio.sleep(delay)
    try:
        await context.bot.delete_message(chat_id, message_id)
    except Exception as e:
        logger.error(f"Error deleting message: {e}")

from telegram.request import HTTPXRequest  # add this import
import httpx  # new import

# Add custom HTTPXRequest subclass to remove proxy configuration
class CustomHTTPXRequest(HTTPXRequest):
    def _build_client(self):
        """Build the HTTPX AsyncClient with expanded pool limits."""
        # Remove proxy parameter if present
        self._client_kwargs.pop("proxy", None)

        # Increase connection pool limits to avoid pool timeout errors
        if "limits" not in self._client_kwargs:
            self._client_kwargs["limits"] = httpx.Limits(
                max_connections=40,
                max_keepalive_connections=20,
            )

        return httpx.AsyncClient(**self._client_kwargs)

async def main():
    """Start the bot."""
    try:
        # Initialize bot without proxy issues using the custom request
        application = Application.builder()\
            .token(config.TELEGRAM_BOT_TOKEN)\
            .request(CustomHTTPXRequest())\
            .build()

        # Store bot start time
        application.bot_data['start_time'] = datetime.now()

        # Initialize job queue
        job_queue = JobQueue()
        job_queue.set_application(application)

        # Initialize application
        await application.initialize()

        # Set up commands
        await setup_commands(application)

        # Initialize database
        db = Database()
        
        # Initialize database (SQLite tables will be created automatically)
        logger.info("Database initialization completed")

        # Initialize handlers
        api_handlers = APICommandHandlers()
        media_handlers = MediaCommandHandlers()
        utility_handlers = UtilityCommandHandlers()
        fun_handler = FunHandler(config)
        admin_tools = AdminTools(db)

        # Initialize GroupSettings with proper initialization
        group_settings = GroupSettings(db, admin_tools)
        # Initialize group settings synchronously
        await group_settings.initialize()

        log_handler = LogHandler(db)
        push_handler = PushHandler(db)
        user_handler = UserHandler(db)
        report_handler = ReportHandler(db)
        translation_handler = TranslationHandler()
        zombie_handler = ZombieHandler(db)
        felo_handler = FeloHandler()
        protection_handler = ProtectionHandler(db, admin_tools)
        # Initialize protection handler
        await protection_handler.initialize()
        raid_protection = RaidProtection()
        greeting_handler = GreetingHandler()
        vote_ban_handler = VoteBanHandler(db)
        summary_handler = SummaryHandler()
        youtube_handler = YouTubeCommandHandler()
        captcha_handler = CaptchaHandler(db)

        # Register message handler for content moderation (highest priority)
        application.add_handler(
            MessageHandler(
                (filters.TEXT | filters.CAPTION | filters.Document.ALL | filters.PHOTO | filters.VIDEO | filters.ANIMATION | filters.Sticker.ALL) & ~filters.COMMAND,
                protection_handler.handle_message
            ),
            group=0  # Highest priority
        )

        # Store protection_handler in bot_data for access elsewhere
        application.bot_data['protection_handler'] = protection_handler

        # Register moderation callback handler (critical for NSFW detection system)
        application.add_handler(CallbackQueryHandler(protection_handler.handle_moderation_callback, pattern="^mod_"))

        # Register regular message handler (lower priority)
        application.add_handler(
            MessageHandler(
                (filters.TEXT | filters.CAPTION | filters.Document.ALL | filters.PHOTO | filters.VIDEO | filters.ANIMATION | filters.Sticker.ALL) & ~filters.COMMAND,
                handle_message
            ),
            group=1  # Lower priority
        )

        # Register handlers
        # Register user commands
        user_handler.register_handlers(application)
        
        # Register utility and admin reference handlers
        utility_handler.register_handlers(application)
        admin_reference.register_handlers(application)

        # Register fun commands
        fun_handler.register_handlers(application)

        # Register utility commands
        from src.handlers.utility_handlers import setup as setup_utility_handlers
        setup_utility_handlers(application)

        # Register push commands
        push_handler.register_handlers(application)

        # Register media commands
        application.add_handler(CommandHandler("meme", media_handlers.meme))
        application.add_handler(CommandHandler("qr", media_handlers.qr))

        # Register imagen command
        setup_imagen(application)

        # Add YouTube transcription handlers
        application.add_handler(CommandHandler("transcribe_yt", youtube_handler.transcribe_yt_command))
        application.add_handler(CallbackQueryHandler(
            youtube_handler.handle_callback,
            pattern=r"^yt_"
        ))

        # Add Felo AI web search handler


        # Add optimized transcription handlers
        transcription_handler = TranscriptionHandler()
        application.add_handler(CommandHandler(
            ["stt", "transcribe"],
            transcription_handler.handle_transcription
        ))

        # Schedule periodic cleanup of temp files
        if application.job_queue:
            async def cleanup_job(context):
                await transcription_handler.cleanup_resources()
            application.job_queue.run_repeating(cleanup_job, interval=300)  # Run every 5 minutes

        # Initialize AntiFloodHandler with proper setup
        from src.features.group_settings.antiflood import AntiFloodHandler
        antiflood_handler = AntiFloodHandler(db)
        # Initialize antiflood handler asynchronously
        await antiflood_handler.initialize()

        # Register filter commands
        application.add_handler(CommandHandler("filter", filter_help))
        application.add_handler(CommandHandler("filter_add", filter_add))
        application.add_handler(CommandHandler("filter_remove", filter_remove))
        application.add_handler(CommandHandler("filter_list", filter_list))

        # Add handlers
        antiflood_handler.register_handlers(application)
        application.add_handler(CommandHandler("weather", api_handlers.weather, filters.ChatType.PRIVATE))
        application.add_handler(CommandHandler("webai", felo_handler.handle_webai))  # Register the webai command
        application.add_handler(CommandHandler("quote", api_handlers.quote))
        application.add_handler(CommandHandler("fact", api_handlers.fact))
        application.add_handler(CommandHandler("dog", api_handlers.dog))
        application.add_handler(CommandHandler("catfact", api_handlers.catfact))
        # Register fun commands
        fun_handler.register_handlers(application)
        admin_tools.register_handlers(application)

        # Register media commands
        application.add_handler(CommandHandler("qr", media_handlers.qr))

        # Register captcha handlers
        application.add_handler(CommandHandler("togglecaptcha", captcha_handler.toggle_captcha))
        application.add_handler(CallbackQueryHandler(captcha_handler.handle_captcha_callback, pattern="^captcha_"))

        # Register new member handlers with captcha check
        application.add_handler(ChatMemberHandler(captcha_handler.handle_new_member, ChatMemberHandler.CHAT_MEMBER))
        application.add_handler(MessageHandler(filters.StatusUpdate.NEW_CHAT_MEMBERS, captcha_handler.handle_new_member))
        application.add_handler(MessageHandler(filters.StatusUpdate.LEFT_CHAT_MEMBER, handle_new_chat))

        # Protection handlers (command only - message handlers already registered above)
        # toggleprotect removed - nsfw command now handles all protection

        # Zombie Management
        application.add_handler(CommandHandler("zombies", zombie_handler.zombies, filters.ChatType.GROUPS))

        # Vote Ban System
        application.add_handler(CommandHandler("voteban", vote_ban_handler.voteban, filters.ChatType.GROUPS))
        application.add_handler(CallbackQueryHandler(vote_ban_handler.handle_vote, pattern="^vote_"))

        # New Admin Features
        application.add_handler(CommandHandler("raidmode", raid_protection.manual_toggle))

        # Group management commands (Enhanced with Rose-bot style)
        application.add_handler(CommandHandler("settings", group_settings.settings_panel, filters.ChatType.GROUPS))
        application.add_handler(CommandHandler("setwelcome", group_settings.set_welcome, filters.ChatType.GROUPS))
        application.add_handler(CommandHandler("togglewelcome", group_settings.toggle_welcome, filters.ChatType.GROUPS))
        application.add_handler(CommandHandler("setrules", group_settings.set_rules, filters.ChatType.GROUPS))
        application.add_handler(CommandHandler("rules", group_settings.show_rules, filters.ChatType.GROUPS))
        application.add_handler(CommandHandler("setflood", group_settings.set_antiflood, filters.ChatType.GROUPS))
        # toggleprotect removed - nsfw command now handles all protection
        
        # Protection commands (new comprehensive system)
        if hasattr(group_settings, 'protection') and group_settings.protection:
            # Anti-Spam Protection Commands
            application.add_handler(CommandHandler("antiflood", group_settings.protection.antiflood_command, filters.ChatType.GROUPS))
            application.add_handler(CommandHandler("floodmode", group_settings.protection.floodmode_command, filters.ChatType.GROUPS))
            
            # Link Protection Commands
            application.add_handler(CommandHandler("antilink", group_settings.protection.antilink_command, filters.ChatType.GROUPS))
            application.add_handler(CommandHandler("antitelegram", group_settings.protection.antitelegram_command, filters.ChatType.GROUPS))
            application.add_handler(CommandHandler("antiforward", group_settings.protection.antiforward_command, filters.ChatType.GROUPS))
            application.add_handler(CommandHandler("linkwhitelist", group_settings.protection.linkwhitelist_command, filters.ChatType.GROUPS))
            
            # Anti-Raid Protection Commands
            application.add_handler(CommandHandler("raidmode", group_settings.protection.raidmode_command, filters.ChatType.GROUPS))
            application.add_handler(CommandHandler("raidtime", group_settings.protection.raidtime_command, filters.ChatType.GROUPS))
            application.add_handler(CommandHandler("raidlimit", group_settings.protection.raidlimit_command, filters.ChatType.GROUPS))
            application.add_handler(CommandHandler("raidaction", group_settings.protection.raidaction_command, filters.ChatType.GROUPS))
            
            # NSFW Protection Commands
            application.add_handler(CommandHandler("nsfw", group_settings.protection.nsfw_command, filters.ChatType.GROUPS))
            application.add_handler(CommandHandler("nsfwmode", group_settings.protection.nsfwmode_command, filters.ChatType.GROUPS))
            application.add_handler(CommandHandler("nsfwstrict", group_settings.protection.nsfwstrict_command, filters.ChatType.GROUPS))
            application.add_handler(CommandHandler("nsfwwhitelist", group_settings.protection.nsfwwhitelist_command, filters.ChatType.GROUPS))
            
            # New User Protection Commands
            application.add_handler(CommandHandler("captcha", group_settings.protection.captcha_command, filters.ChatType.GROUPS))
            application.add_handler(CommandHandler("captchatime", group_settings.protection.captchatime_command, filters.ChatType.GROUPS))
            application.add_handler(CommandHandler("welcomemute", group_settings.protection.welcomemute_command, filters.ChatType.GROUPS))
            application.add_handler(CommandHandler("restricttime", group_settings.protection.restricttime_command, filters.ChatType.GROUPS))
            
            # Media Protection Commands
            application.add_handler(CommandHandler("antimedia", group_settings.protection.antimedia_command, filters.ChatType.GROUPS))
            application.add_handler(CommandHandler("antisticker", group_settings.protection.antisticker_command, filters.ChatType.GROUPS))
            application.add_handler(CommandHandler("antivideo", group_settings.protection.antivideo_command, filters.ChatType.GROUPS))
            application.add_handler(CommandHandler("antiaudio", group_settings.protection.antiaudio_command, filters.ChatType.GROUPS))
            application.add_handler(CommandHandler("antidocument", group_settings.protection.antidocument_command, filters.ChatType.GROUPS))
            
            # Language Protection Commands
            application.add_handler(CommandHandler("antiarabic", group_settings.protection.antiarabic_command, filters.ChatType.GROUPS))
            application.add_handler(CommandHandler("anticyrillic", group_settings.protection.anticyrillic_command, filters.ChatType.GROUPS))
            application.add_handler(CommandHandler("antichinese", group_settings.protection.antichinese_command, filters.ChatType.GROUPS))
            application.add_handler(CommandHandler("langwhitelist", group_settings.protection.langwhitelist_command, filters.ChatType.GROUPS))
            
            # Protection Help Command
            application.add_handler(CommandHandler("protection", group_settings.protection.protection_help_command, filters.ChatType.GROUPS))
        
        # Settings panel callbacks  
        application.add_handler(CallbackQueryHandler(group_settings.handle_settings_callback, pattern="^settings_"))
        application.add_handler(CommandHandler("setlogchannel", log_handler.set_log_channel, filters.ChatType.GROUPS))
        application.add_handler(CommandHandler("togglelog", log_handler.toggle_log_events, filters.ChatType.GROUPS))

        # Add callback query handler for welcome message buttons
        application.add_handler(CallbackQueryHandler(group_settings.handle_welcome_callback, pattern="^(show_rules|back_to_welcome)$"))

        # Add message handler to check for restricted users
        application.add_handler(MessageHandler(
            filters.ChatType.GROUPS & ~filters.COMMAND,
            captcha_handler.check_restricted_user
        ))

        application.add_handler(CommandHandler("report", report_handler.report))
        application.add_handler(CommandHandler("reportabuse", report_handler.report_abuse))
        application.add_handler(CallbackQueryHandler(
            report_handler.handle_callback,
            pattern=r"^(report|abuse)_"
        ))
        application.add_handler(CommandHandler("languages", translation_handler.show_languages))

        # Add appeal command handler
        appeal_handler = AppealHandler(db)
        auto_responder.register_handlers(application)
        admin_mention_handler.register_handlers(application)
        
        # Register AI chat handlers for mentions and replies
        ai_chat.register_handlers(application)
        
        # Add fallback handler for DM commands that AI doesn't handle
        async def handle_private_fallback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
            """Handle private commands that weren't processed by AI chat"""
            if update.effective_chat.type == ChatType.PRIVATE:
                await personal_handlers.handle_private_command(update, context)
        
        application.add_handler(
            MessageHandler(
                filters.TEXT & filters.ChatType.PRIVATE & filters.COMMAND,
                handle_private_fallback
            ),
            group=4  # Lower priority than AI chat handler
        )
        
        application.add_handler(CommandHandler("appeal", appeal_handler.handle_appeal))
        application.add_handler(CallbackQueryHandler(
            appeal_handler.handle_appeal_callback,
            pattern=r"^appeal_(accept|reject)_\d+$"
        ))

        # Add start and model button handlers
        application.add_handler(CallbackQueryHandler(
            user_handler.start_button,
            pattern=r"^(start_help|fun_commands|ai_models|start_back)$"
        ))

        # Add handlers for /play and /stop commands
        application.add_handler(CommandHandler("play", coming_soon_handler))
        application.add_handler(CommandHandler("stop", coming_soon_handler))

        # Schedule morning greetings for each group when bot starts
        async def setup_morning_greetings(context: ContextTypes.DEFAULT_TYPE):
            try:
                # Initialize a set to track scheduled groups
                if 'scheduled_greeting_groups' not in context.bot_data:
                    context.bot_data['scheduled_greeting_groups'] = set()

                # Get all group chats (properly awaited)
                groups = await db.get_all_groups()
                if groups:
                    for group in groups:
                        try:
                            chat_id = group['chat_id']
                            # Check if this group is already scheduled
                            if chat_id in context.bot_data['scheduled_greeting_groups']:
                                logger.info(f"Skipping already scheduled morning greetings for chat {chat_id}")
                                continue

                            # Schedule the greeting and add to tracked groups
                            await greeting_handler.schedule_morning_greeting(context, chat_id)
                            context.bot_data['scheduled_greeting_groups'].add(chat_id)
                        except Exception as e:
                            logger.error(f"❌ Error scheduling for group {group['chat_id']}: {e}")
                else:
                    logger.info("No groups found for morning greetings scheduling")
            except Exception as e:
                logger.error(f"❌ Error in morning greetings setup: {e}")

        # Run the setup after bot starts and job queue is ready
        if application.job_queue:
            application.job_queue.run_once(setup_morning_greetings, when=1)
        else:
            logger.error("❌ Job queue not available! Morning greetings won't work!")

        application.add_handler(CallbackQueryHandler(
            protection_handler.handle_moderation_callback,
            pattern=r"^mod_"
        ))

        # Initialize the task scheduler without complex async
        scheduler = TaskScheduler(application, personal_handlers.db)

        # Set up bot commands
        await setup_commands(application)
        print("Bot commands have been set up successfully!")

        # Initialize database
        db = Database()
        db._create_tables()

        # Add morning greeting handlers
        async def schedule_morning_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
            if not update.effective_chat:
                return

            chat_id = update.effective_chat.id

            # Check if this chat is already in the scheduled groups
            if 'scheduled_greeting_groups' in context.bot_data and chat_id in context.bot_data['scheduled_greeting_groups']:
                await update.message.reply_text("Morning greetings are already scheduled for this chat.")
                return

            # Schedule the greeting
            await greeting_handler.schedule_morning_greeting(context, chat_id)

            # Add to tracked groups if successful
            if 'scheduled_greeting_groups' not in context.bot_data:
                context.bot_data['scheduled_greeting_groups'] = set()
            context.bot_data['scheduled_greeting_groups'].add(chat_id)

            await update.message.reply_text("✅ Morning greetings have been scheduled for this chat.")

        application.add_handler(CommandHandler("schedule_morning", schedule_morning_command))

        # Add birthday detection handler (with higher priority to ensure it runs)
        application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, greeting_handler.handle_birthday), group=2)

        # Import owner commands
        from src.handlers.owner_commands import user_stats_command, owner_help_command
        from src.handlers.server_stats_command import server_stats_command, user_activity_command
        from src.handlers.db_stats_command import db_stats_command

        # Register push handler (owner commands)
        push_handler.register_handlers(application)
        
        # Register owner commands
        application.add_handler(CommandHandler("userstats", user_stats_command))
        application.add_handler(CommandHandler("ohelp", owner_help_command))
        application.add_handler(CommandHandler("dbstats", db_stats_command))
        
        # Register server stats commands
        application.add_handler(CommandHandler("serverstats", server_stats_command))
        application.add_handler(CommandHandler("useractivity", user_activity_command))

        # Make sure the database is available in bot_data
        application.bot_data["db"] = db
        application.bot_data["db_tracker"] = db_tracker
        application.bot_data["owner_username"] = os.getenv("BOT_OWNER", "").lower()  # Store owner username in lowercase
        application.bot_data["owner_id"] = 6655019805  # Store owner ID for owner_only decorator
        
        # Add command tracker middleware
        from src.utils.command_tracker import CommandTrackerMiddleware
        command_tracker = CommandTrackerMiddleware(application)

        print("Starting bot...")
        # Configure polling with proper timeouts and updates
        await application.run_polling(
            allowed_updates=Update.ALL_TYPES,
            drop_pending_updates=True
        )

    except Exception as e:
        logger.error(f"Error during bot execution: {e}")
        raise e
    finally:
        try:
            if 'application' in locals() and application.running:
                await application.stop()
        except Exception as e:
            print(f"Error stopping application: {e}")

if __name__ == "__main__":
    # Apply nest_asyncio to handle event loops properly
    nest_asyncio.apply()

    # Start tracemalloc for debugging
    tracemalloc.start()

    # Run the bot with proper asyncio handling
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nBot stopped gracefully by user")
    except Exception as e:
        print(f"\nFatal error: {e}")
        if not isinstance(e, KeyboardInterrupt):
            traceback.print_exc()

