"""
Filter Lists Configuration 🛡️
----------------------------
Contains all the filter lists for chat protection
"""

import re
import json
import os

# Load NSFW words from JSON
def load_nsfw_words():
    json_path = os.path.join(os.path.dirname(__file__), 'nsfw_words.json')
    try:
        with open(json_path, 'r') as f:
            data = json.load(f)
            return {word.lower() for word in data.get('nsfw_words', [])}
    except Exception as e:
        print(f"Error loading NSFW words: {e}")
        return set()

def save_nsfw_words(words):
    json_path = os.path.join(os.path.dirname(__file__), 'nsfw_words.json')
    try:
        with open(json_path, 'w') as f:
            json.dump({'nsfw_words': list(words)}, f, indent=4)
        return True
    except Exception as e:
        print(f"Error saving NSFW words: {e}")
        return False

# # URL Regex Patterns 🔗
# URL_PATTERNS = [
#     # HTTP/HTTPS URLs
#     r'https?://(?:[\w-]|\.|/|\?|=|%|&)+',
    
#     # Common URL shorteners
#     r'(?:bit\.ly|goo\.gl|t\.co|tinyurl\.com|tiny\.cc|is\.gd|buff\.ly|ow\.ly|rebrand\.ly|cutt\.ly)/\S+',
    
#     # Direct IP addresses
#     r'(?:https?://)?(?:\d{1,3}\.){3}\d{1,3}(?:/\S*)?',
    
#     # Common domains without http
#     r'(?:www\.)?(?:[\w-]+\.)+(?:com|net|org|edu|gov|mil|biz|info|io|ai|app|dev|me|co|us|uk|ru|cn|jp|kr|in)(?:/\S*)?',
    
#     # Telegram links (except allowed ones)
#     r't(?:elegram)?\.me/(?!NexusAI_HelpingAI_bot|addstickers)(?:[\w-]+/?)*',
    
#     # Discord invites
#     r'discord(?:\.gg|\.com/invite|app\.com/invite)/[\w-]+',
    
#     # Common social media links
#     r'(?:facebook|fb|instagram|twitter|tiktok|snapchat|linkedin|pinterest|reddit|tumblr)\.com/[\w-]+',
    
#     # Video platforms
#     r'(?:youtube|youtu\.be|vimeo|dailymotion|twitch)\.(?:com|tv)/[\w-]+',
# ]

# # Compile regex patterns for better performance
# COMPILED_URL_PATTERNS = [re.compile(pattern, re.IGNORECASE) for pattern in URL_PATTERNS]

# # NSFW Words and Phrases 🔞
NSFW_WORDS = load_nsfw_words()

# # Blocked File Extensions 🚫
# BLOCKED_EXTENSIONS = {
#     # Executable
#     '.exe', '.msi', '.bat', '.cmd', '.ps1', '.vbs',
#     # System
#     '.sys', '.dll', '.bin',
#     # Packages
#     '.apk', '.app', '.deb', '.rpm'
# }
