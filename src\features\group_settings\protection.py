"""Protection handler for managing group safety 🛡️"""

import json
from pathlib import Path
import random
import asyncio
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
import logging
from typing import Dict, Optional, Set
import uuid
import tempfile
import os
import traceback
import re

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.error import TelegramError
from telegram.ext import ContextTypes
from telegram.constants import ChatType, ParseMode, ReactionEmoji

# Global variable to store NSFW words between handler calls
_found_nsfw_words = []

from ...database.db import Database
from ...utils.ai_moderator import AIModerator
from ...utils.decorators import admin_only, group_only
from ..admin_tools import AdminTools
from ...utils.typegpt_moderation import get_typegpt_moderator
from ...config.filter_lists import load_nsfw_words

logger = logging.getLogger(__name__)

def create_flexible_pattern(word):
    """Create a flexible regex pattern for matching obfuscated words"""
    # Simple pattern that just looks for the word with word boundaries
    return r'\b' + re.escape(word.lower()) + r'\b'

class ProtectionHandler:
    """Handles group protection and content moderation."""

    def __init__(self, db: Database, admin_tools: AdminTools):
        """Initialize protection handler."""
        self.db = db
        self.admin_tools = admin_tools
        self.ai_moderator = AIModerator()
        self.warned_users = {}
        self.user_messages = {}  # Track user message counts (used for other protection features)
        self._last_warning_time: Dict[int, datetime] = {}
        self._pending_moderation: Dict[str, Dict] = {}  # Store pending moderation decisions
        self.moderation_cases: Dict[str, Dict] = {}  # Initialize moderation_cases dictionary
        self.typegpt_moderator = None  # Will be initialized lazily
        self.moderation_tasks = set()  # Keep track of running moderation tasks
        self._temp_files = set()  # Track temporary files for cleanup
        self.executor = ThreadPoolExecutor(max_workers=2)  # Thread pool for concurrent operations - reduced to avoid overloading
        self.currently_moderating = set()  # Track messages currently being moderated
        self._processed_messages = set()  # Track message IDs that have been processed
        self.thread_locks = {}  # Locks for thread synchronization

        # This handler is registered in bot.py
        # application.add_handler(CallbackQueryHandler(self.handle_moderation_callback, pattern="^mod_"))

    async def initialize(self):
        """Initialize the protection handler"""
        # Log initialization
        logger.info("Protection handler initialized")
        logger.info("NSFW filter is disabled by default. Use /nsfw on to enable comprehensive protection.")

        # Start a task to clean up old processed messages
        asyncio.create_task(self._cleanup_processed_messages())

    async def _get_typegpt_moderator(self):
        """Get TypeGPT moderator with lazy initialization."""
        if self.typegpt_moderator is None:
            try:
                self.typegpt_moderator = await get_typegpt_moderator()
                logger.info("✅ TypeGPT moderator initialized")
            except Exception as e:
                logger.error(f"❌ Failed to initialize TypeGPT moderator: {e}")
                return None
        return self.typegpt_moderator

    async def _cleanup_processed_messages(self):
        """Clean up old processed messages to prevent memory leaks"""
        while True:
            try:
                # Sleep for 10 minutes
                await asyncio.sleep(600)

                # Clear the processed messages set
                old_count = len(self._processed_messages)
                self._processed_messages.clear()
                logger.info(f"Cleaned up {old_count} processed message records")

                # Clean up the currently_moderating set
                mod_count = len(self.currently_moderating)
                self.currently_moderating.clear()
                logger.info(f"Cleaned up {mod_count} currently moderating records")

                # Clean up completed moderation tasks
                completed_tasks = set()
                for task in self.moderation_tasks:
                    if task.done():
                        completed_tasks.add(task)

                for task in completed_tasks:
                    self.moderation_tasks.discard(task)

                if completed_tasks:
                    logger.info(f"Cleaned up {len(completed_tasks)} completed moderation tasks")
            except Exception as e:
                logger.error(f"Error cleaning up processed messages: {e}")

    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle incoming messages and check for violations 🛡️"""
        if not update.message or not update.effective_chat or update.effective_chat.type == ChatType.PRIVATE:
            return

        chat_id = update.effective_chat.id
        user_id = update.effective_user.id
        message = update.message
        message_id = message.message_id

        # Check if this message has already been processed
        message_key = f"{chat_id}:{message_id}"
        if message_key in self._processed_messages:
            logger.info(f"Skipping already processed message {message_id} in chat {chat_id}")
            return

        # Add this message to the processed set
        self._processed_messages.add(message_key)

        # Create a background task to process this message
        # This allows the main thread to continue processing other messages
        asyncio.create_task(self._process_message_async(update, context))

        # Don't return here - allow message to propagate to other handlers
        # The protection processing happens in the background

    async def _process_message_async(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Process a message asynchronously in a background task"""
        try:
            chat_id = update.effective_chat.id
            user_id = update.effective_user.id
            message = update.message

            message_id = message.message_id

            # Always ensure protection is enabled in the database
            await self.db.set_group_setting(chat_id, 'protection_enabled', True)

            # Check if admin protection is enabled (default is False, meaning admins are exempt by default)
            check_admins = await self.db.get_group_setting(chat_id, 'check_admins', False)

            # Skip checks for admins if check_admins is disabled (default behavior)
            if not check_admins:
                try:
                    user = await context.bot.get_chat_member(chat_id, user_id)
                    if user.status in ['administrator', 'creator']:
                        logger.info(f"Skipping protection checks for admin/creator: {user_id} in chat {chat_id} (admin checks disabled)")
                        return
                except TelegramError:
                    logger.error(f"Failed to get chat member info for user {user_id} in chat {chat_id}")
                    # Continue with protection checks if we can't determine admin status
                    pass

            # Check if user is in any whitelist
            nsfw_whitelist = await self.db.get_group_setting(chat_id, 'nsfw_whitelist', [])
            language_whitelist = await self.db.get_group_setting(chat_id, 'language_whitelist', [])
            
            # Run all protection checks
            violations = []

            # 1. Check forwarded messages
            if message.forward_origin:
                antiforward_enabled = await self.db.get_group_setting(chat_id, 'antiforward_enabled', False)
                if antiforward_enabled:
                    violations.append("forwarded_message")

            # 2. Check links in text
            if message.text or message.caption:
                text = message.text or message.caption
                await self._check_links(chat_id, text, violations)
                await self._check_language(chat_id, user_id, text, violations, language_whitelist)
                await self._process_text_content(update, context, text)

            # 3. Check media content
            if (message.photo or message.video or message.animation or message.sticker or 
                (message.document and (message.document.mime_type or '').startswith(('image/', 'video/')))):
                await self._check_media_restrictions(chat_id, message, violations)
                await self._process_media_content(update, context)

            # 4. Check audio/voice content
            if message.audio or message.voice or message.video_note:
                antiaudio_enabled = await self.db.get_group_setting(chat_id, 'antiaudio_enabled', False)
                if antiaudio_enabled:
                    violations.append("audio_content")

            # 5. Check documents
            if message.document and not (message.document.mime_type or '').startswith(('image/', 'video/')):
                antidocument_enabled = await self.db.get_group_setting(chat_id, 'antidocument_enabled', False)
                if antidocument_enabled:
                    violations.append("document_content")

            # Handle violations
            if violations:
                await self._handle_violations(update, context, violations)

        except Exception as e:
            logger.error(f"Error processing message: {e}")
            logger.error(traceback.format_exc())

    async def _check_links(self, chat_id: int, text: str, violations: list) -> None:
        """Check for various types of links"""
        import re
        
        # Check for general links
        antilink_enabled = await self.db.get_group_setting(chat_id, 'antilink_enabled', False)
        if antilink_enabled:
            link_patterns = [
                r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+',
                r'www\.(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+',
                r'[a-zA-Z0-9-]+\.[a-zA-Z]{2,}(?:/[^\s]*)?'
            ]
            
            for pattern in link_patterns:
                if re.search(pattern, text, re.IGNORECASE):
                    # Check whitelist
                    whitelist = await self.db.get_group_setting(chat_id, 'link_whitelist', [])
                    is_whitelisted = False
                    for domain in whitelist:
                        if domain.lower() in text.lower():
                            is_whitelisted = True
                            break
                    
                    if not is_whitelisted:
                        violations.append("general_link")
                        break

        # Check for Telegram links specifically
        antitelegram_enabled = await self.db.get_group_setting(chat_id, 'antitelegram_enabled', False)
        if antitelegram_enabled:
            telegram_patterns = [
                r't\.me/[a-zA-Z0-9_]+',
                r'telegram\.me/[a-zA-Z0-9_]+',
                r'telegram\.dog/[a-zA-Z0-9_]+'
            ]
            
            for pattern in telegram_patterns:
                if re.search(pattern, text, re.IGNORECASE):
                    violations.append("telegram_link")
                    break

    async def _check_language(self, chat_id: int, user_id: int, text: str, violations: list, language_whitelist: list) -> None:
        """Check for restricted languages"""
        if user_id in language_whitelist:
            return
            
        # Check Arabic
        antiarabic_enabled = await self.db.get_group_setting(chat_id, 'antiarabic_enabled', False)
        if antiarabic_enabled:
            # Arabic Unicode range: \u0600-\u06FF
            if re.search(r'[\u0600-\u06FF]', text):
                violations.append("arabic_text")

        # Check Cyrillic
        anticyrillic_enabled = await self.db.get_group_setting(chat_id, 'anticyrillic_enabled', False)
        if anticyrillic_enabled:
            # Cyrillic Unicode range: \u0400-\u04FF
            if re.search(r'[\u0400-\u04FF]', text):
                violations.append("cyrillic_text")

        # Check Chinese
        antichinese_enabled = await self.db.get_group_setting(chat_id, 'antichinese_enabled', False)
        if antichinese_enabled:
            # Chinese Unicode ranges: \u4e00-\u9fff (CJK Unified Ideographs)
            if re.search(r'[\u4e00-\u9fff]', text):
                violations.append("chinese_text")

    async def _check_media_restrictions(self, chat_id: int, message, violations: list) -> None:
        """Check various media restrictions"""
        # Check general media blocking
        antimedia_enabled = await self.db.get_group_setting(chat_id, 'antimedia_enabled', False)
        if antimedia_enabled:
            violations.append("media_content")
            return  # If all media is blocked, no need to check specific types

        # Check stickers
        if message.sticker:
            antisticker_enabled = await self.db.get_group_setting(chat_id, 'antisticker_enabled', False)
            if antisticker_enabled:
                violations.append("sticker_content")

        # Check videos
        if message.video or message.animation:
            antivideo_enabled = await self.db.get_group_setting(chat_id, 'antivideo_enabled', False)
            if antivideo_enabled:
                violations.append("video_content")

    async def _handle_violations(self, update: Update, context: ContextTypes.DEFAULT_TYPE, violations: list) -> None:
        """Handle detected violations"""
        try:
            chat_id = update.effective_chat.id
            user_id = update.effective_user.id
            message = update.message

            # Delete the message
            try:
                await message.delete()
                logger.info(f"Deleted message with violations: {violations}")
            except Exception as e:
                logger.error(f"Failed to delete violating message: {e}")

            # Send warning based on violation type
            violation_messages = {
                "forwarded_message": "🔄 Forwarded messages are not allowed in this group.",
                "general_link": "🔗 Links are not allowed in this group.",
                "telegram_link": "📱 Telegram links are not allowed in this group.",
                "arabic_text": "🌐 Arabic text is not allowed in this group.",
                "cyrillic_text": "🌐 Cyrillic text is not allowed in this group.",
                "chinese_text": "🌐 Chinese text is not allowed in this group.",
                "media_content": "📱 Media files are not allowed in this group.",
                "sticker_content": "😃 Stickers are not allowed in this group.",
                "video_content": "🎥 Videos are not allowed in this group.",
                "audio_content": "🎵 Audio files are not allowed in this group.",
                "document_content": "📄 Documents are not allowed in this group."
            }

            # Send a brief warning message
            if violations:
                primary_violation = violations[0]
                warning_text = violation_messages.get(primary_violation, "⚠️ Message violates group rules.")
                
                user_mention = update.effective_user.mention_html()
                warning_msg = await context.bot.send_message(
                    chat_id=chat_id,
                    text=f"{warning_text}\n\n👤 User: {user_mention}",
                    parse_mode=ParseMode.HTML
                )
                
                # Auto-delete warning after 10 seconds
                asyncio.create_task(self._auto_delete_message(context, chat_id, warning_msg.message_id, 10))

        except Exception as e:
            logger.error(f"Error handling violations: {e}")
            logger.error(traceback.format_exc())

    async def _process_text_content(self, update: Update, context: ContextTypes.DEFAULT_TYPE, text: str) -> None:
        """Process text content asynchronously"""
        try:
            chat_id = update.effective_chat.id
            user_id = update.effective_user.id
            message_id = update.effective_message.message_id

            # Check if NSFW filter is enabled
            nsfw_filter_enabled = await self.db.get_group_setting(chat_id, 'nsfw_filter_enabled', False)

            if nsfw_filter_enabled:
                logger.info(f"NSFW filter is enabled for chat {chat_id}, proceeding with NSFW word detection")

                # Run NSFW word detection in a separate thread to avoid blocking
                loop = asyncio.get_running_loop()
                found_words = await loop.run_in_executor(
                    self.executor,
                    lambda: self._check_nsfw_words(text)
                )

                if found_words:
                    # Create a unique key for this moderation request
                    moderation_key = f"mod:{chat_id}:{message_id}"

                    # Check if this message is already being moderated
                    if moderation_key not in self.currently_moderating:
                        # Mark this message as being moderated
                        self.currently_moderating.add(moderation_key)

                        async def run_moderation():
                            try:
                                await self._moderate_text(update, context, text)
                            finally:
                                self.currently_moderating.discard(moderation_key)

                        task = asyncio.create_task(run_moderation())
                        self.moderation_tasks.add(task)
                        task.add_done_callback(self.moderation_tasks.discard)

                        logger.info(f"Started background moderation task for message: {text[:50]}...")
                    else:
                        logger.info(f"Message {message_id} is already being moderated, skipping duplicate moderation")
                else:
                    logger.info(f"No NSFW words found in message, skipping AI moderation")
            else:
                logger.info(f"NSFW filter is disabled for chat {chat_id}, skipping NSFW word detection")
        except Exception as e:
            logger.error(f"Error processing text content: {e}")
            logger.error(traceback.format_exc())

    def _check_nsfw_words(self, text: str) -> list:
        """Check text for NSFW words - 100x better detection algorithm"""
        try:
            if not text or len(text.strip()) == 0:
                return []
                
            # Convert to lowercase for case-insensitive matching
            text_lower = text.lower()
            found_words = []

            # Load NSFW words from the JSON file (this is the comprehensive list)
            nsfw_words = load_nsfw_words()
            logger.info(f"🔍 Scanning message against {len(nsfw_words)} NSFW words from database")
            
            # PHASE 1: Lightning-fast exact word matching (O(1) set operations)
            # Extract all words from text using word boundaries
            words_in_text = set(re.findall(r'\b\w+\b', text_lower))
            
            # Create optimized NSFW word set for instant lookup
            nsfw_word_set = set(word.lower().strip() for word in nsfw_words if len(word.strip()) > 2)
            
            # Check for direct word matches (super fast)
            direct_matches = words_in_text.intersection(nsfw_word_set)
            if direct_matches:
                found_words.extend(list(direct_matches))
                logger.info(f"⚡ Phase 1 - Found {len(direct_matches)} exact NSFW word matches: {list(direct_matches)}")
                return found_words
            
            # PHASE 2: Advanced pattern matching for obfuscated words
            logger.info("🔬 Phase 2 - Checking for obfuscated NSFW words...")
            
            # Remove all non-alphanumeric characters to detect obfuscated words
            clean_text = re.sub(r'[^a-zA-Z0-9\s]', '', text_lower)
            clean_words = set(re.findall(r'\b\w+\b', clean_text))
            
            # Check clean words against NSFW list
            clean_matches = clean_words.intersection(nsfw_word_set)
            if clean_matches:
                found_words.extend(list(clean_matches))
                logger.info(f"🧹 Phase 2a - Found {len(clean_matches)} obfuscated matches: {list(clean_matches)}")
                return found_words
            
            # PHASE 3: Leetspeak and character substitution detection
            logger.info("🤖 Phase 3 - Checking for leetspeak and character substitutions...")
            
            # Common character substitutions in leetspeak
            leetspeak_map = {
                '4': 'a', '@': 'a', '3': 'e', '1': 'i', '!': 'i', 
                '0': 'o', '5': 's', '$': 's', '7': 't', '+': 't',
                '8': 'b', '6': 'g', '9': 'g', 'ph': 'f', 'x': 'ks'
            }
            
            # Normalize text by replacing leetspeak characters
            normalized_text = text_lower
            for leet, normal in leetspeak_map.items():
                normalized_text = normalized_text.replace(leet, normal)
            
            # Extract words from normalized text
            normalized_words = set(re.findall(r'\b\w+\b', normalized_text))
            
            # Check normalized words against NSFW list
            leet_matches = normalized_words.intersection(nsfw_word_set)
            if leet_matches:
                found_words.extend(list(leet_matches))
                logger.info(f"🔢 Phase 3 - Found {len(leet_matches)} leetspeak matches: {list(leet_matches)}")
                return found_words
            
            # PHASE 4: Spaced character detection (f u c k, p.o.r.n, etc.)
            logger.info("📝 Phase 4 - Checking for spaced/separated characters...")
            
            # Remove spaces, dots, dashes, underscores between characters
            compressed_text = re.sub(r'[.\s\-_*]+', '', text_lower)
            
            # Check if any NSFW word appears in the compressed text
            for nsfw_word in nsfw_word_set:
                if len(nsfw_word) >= 3 and nsfw_word in compressed_text:
                    # Verify it's not part of a larger innocent word
                    if self._is_standalone_word_match(nsfw_word, compressed_text, text_lower):
                        found_words.append(nsfw_word)
                        logger.info(f"📊 Phase 4 - Found spaced word: {nsfw_word}")
            
            if found_words:
                return found_words
            
            # PHASE 5: Advanced regex patterns for heavily obfuscated content
            logger.info("🎯 Phase 5 - Advanced pattern matching for heavily obfuscated content...")
            
            # Only check the most commonly obfuscated words to avoid performance issues
            # Skip very common substrings that cause false positives
            critical_patterns = {
                'fuck': [
                    r'\bf+[\W\d_]*u+[\W\d_]*c+[\W\d_]*k+\b',
                    r'\bph+[\W\d_]*u+[\W\d_]*[ck]+\b',
                    r'\bf[\W\d_]*4[\W\d_]*[ck]+\b',
                ],
                'shit': [
                    r'\bs+[\W\d_]*h+[\W\d_]*i+[\W\d_]*t+\b',
                    r'\b5+[\W\d_]*h+[\W\d_]*1+[\W\d_]*7+\b',
                ],
                'bitch': [
                    r'\bb+[\W\d_]*i+[\W\d_]*t+[\W\d_]*c+[\W\d_]*h+\b',
                    r'\b8+[\W\d_]*1+[\W\d_]*7+[\W\d_]*[ck]+[\W\d_]*h+\b',
                ],
                'pussy': [
                    r'\bp+[\W\d_]*u+[\W\d_]*s+[\W\d_]*s+[\W\d_]*y+\b',
                    r'\bp+[\W\d_]*4+[\W\d_]*5+[\W\d_]*5+[\W\d_]*y+\b',
                ],
                'porn': [
                    r'\bp+[\W\d_]*o+[\W\d_]*r+[\W\d_]*n+\b',
                    r'\bp+[\W\d_]*0+[\W\d_]*r+[\W\d_]*n+\b',
                ],
                'cunt': [
                    r'\bc+[\W\d_]*u+[\W\d_]*n+[\W\d_]*t+\b',
                    r'\b[ck]+[\W\d_]*4+[\W\d_]*n+[\W\d_]*7+\b',
                ]
                # Note: Removed 'ass', 'dick', 'sex', 'nude' patterns that cause too many false positives
                # These will be caught in earlier phases if they're genuine violations
            }
            
            for base_word, patterns in critical_patterns.items():
                for pattern in patterns:
                    try:
                        matches = re.finditer(pattern, text_lower, re.IGNORECASE)
                        for match in matches:
                            matched_text = match.group(0)
                            # Only consider matches that are reasonably close to the original word length
                            if len(matched_text) <= len(base_word) * 3:  # Allow up to 3x length due to obfuscation
                                found_words.append(base_word)
                                logger.info(f"🎯 Phase 5 - Found heavily obfuscated word: {base_word} (matched: {matched_text})")
                                break
                    except re.error as e:
                        logger.error(f"Regex error for pattern '{pattern}': {e}")
                        continue
                
                # Stop after first detection for efficiency
                if found_words:
                    break
            
            # PHASE 6: Context-aware detection (final fallback)
            if not found_words:
                logger.info("🧠 Phase 6 - Context-aware detection...")
                
                # Check for suspicious patterns that might indicate NSFW content
                suspicious_indicators = [
                    r'\b(?:want|need|looking\s+for|find)\s+(?:some|hot|sexy)\b',
                    r'\b(?:send|show|give)\s+(?:nudes|pics|photos)\b',
                    r'\b(?:hook\s+up|one\s+night|casual)\b',
                    r'\b(?:adult|mature|18\+|nsfw)\s+(?:content|material|stuff)\b',
                    r'\b(?:xxx|porn|sex)\s+(?:site|video|content)\b'
                ]
                
                for pattern in suspicious_indicators:
                    if re.search(pattern, text_lower, re.IGNORECASE):
                        found_words.append('contextual_nsfw')
                        logger.info(f"🧠 Phase 6 - Found suspicious context pattern: {pattern}")
                        break

            # Log final results
            if found_words:
                logger.info(f"🚨 NSFW DETECTION COMPLETE: Found {len(found_words)} violations: {found_words}")
            else:
                logger.info(f"✅ NSFW DETECTION COMPLETE: Message is clean")

            return found_words
            
        except Exception as e:
            logger.error(f"Error in enhanced NSFW detection: {e}")
            logger.error(traceback.format_exc())
            return []

    def _is_standalone_word_match(self, nsfw_word: str, compressed_text: str, original_text: str) -> bool:
        """Check if NSFW word match is standalone and not part of innocent word"""
        try:
            # Find position of NSFW word in compressed text
            pos = compressed_text.find(nsfw_word)
            if pos == -1:
                return False
            
            # Check characters before and after to ensure it's not part of larger word
            before_char = compressed_text[pos - 1] if pos > 0 else ' '
            after_char = compressed_text[pos + len(nsfw_word)] if pos + len(nsfw_word) < len(compressed_text) else ' '
            
            # If surrounded by letters, it might be part of innocent word
            if before_char.isalpha() and after_char.isalpha():
                return False
                
            # Enhanced check: common innocent words that contain NSFW substrings
            innocent_words = {
                # Words containing "ass"
                'class', 'glass', 'grass', 'mass', 'pass', 'classic', 'assistant', 'assess', 'assignment',
                'assurance', 'assumption', 'assassin', 'asshole', 'assemble', 'assertion', 'associate',
                'passage', 'message', 'massage', 'cassette', 'bassist', 'compass', 'harass', 'embassy',
                'trespass', 'surpass', 'bypass', 'amass', 'crevasse', 'molasses', 'vasssal', 'morass',
                
                # Words containing "dick"
                'dickens', 'dickinson', 'dickhead', 'predict', 'addict', 'verdict', 'benedick', 'riddick',
                'frederick', 'roderick', 'broderick', 'kendrick', 'patrick', 'maverick', 'limerick',
                
                # Words containing "hell"
                'hello', 'shell', 'sheller', 'dwell', 'swell', 'smell', 'spell', 'belly', 'jelly', 'kelly',
                'michelle', 'isabella', 'umbrella', 'nutella', 'mozzarella', 'cinderella', 'cappella',
                'rebellion', 'hellish', 'hellenic', 'hellenistic', 'hellcat', 'hellhound', 'shelter',
                
                # Words containing "sex"
                'sextet', 'sextant', 'sexism', 'sexual', 'sexuality', 'bisexual', 'asexual', 'unisex',
                'sussex', 'middlesex', 'essex', 'wessex', 'sexagenarian', 'intersex', 'transsex',
                
                # Words containing "anal"
                'anal', 'analysis', 'analyst', 'analyze', 'analytical', 'analyze', 'canal', 'banal',
                'venal', 'penal', 'renal', 'journal', 'national', 'international', 'rational',
                'arsenal', 'cardinal', 'marginal', 'original', 'criminal', 'terminal', 'seminal',
                
                # Words containing "cum"
                'cumulative', 'cucumber', 'circumference', 'circumstance', 'circumnavigate', 'succumb',
                'document', 'vacuum', 'spectrum', 'fulcrum', 'decorum', 'locum', 'scum', 'acumen',
                
                # Words containing other common substrings
                'pushy', 'pussycat', 'pussy-willow', 'discussion', 'percussion', 'concussion', 'repercussion'
            }
            
            # Get all words from the original text
            words_in_original = re.findall(r'\b\w+\b', original_text.lower())
            
            # Check if any word in the original text is innocent but contains the NSFW substring
            for word in words_in_original:
                if nsfw_word in word and word in innocent_words:
                    logger.info(f"🛡️ Protecting innocent word '{word}' that contains '{nsfw_word}'")
                    return False
                    
                # Additional protection for common patterns
                if nsfw_word == "ass" and word in ['class', 'glass', 'grass', 'mass', 'pass', 'classic', 'assistant']:
                    logger.info(f"🛡️ Protecting common word '{word}' containing 'ass'")
                    return False
                    
                if nsfw_word == "dick" and word in ['predict', 'addict', 'verdict', 'frederick', 'patrick']:
                    logger.info(f"🛡️ Protecting name/word '{word}' containing 'dick'")
                    return False
                    
                if nsfw_word == "hell" and word in ['hello', 'shell', 'dwell', 'swell', 'smell', 'spell']:
                    logger.info(f"🛡️ Protecting common word '{word}' containing 'hell'")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking standalone word match: {e}")
            return True  # Default to flagging if unsure

    async def _process_media_content(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Process media content asynchronously"""
        try:
            chat_id = update.effective_chat.id
            user_id = update.effective_user.id
            message = update.message

            # React with eyes emoji to indicate scanning of media
            try:
                await context.bot.set_message_reaction(
                    chat_id=chat_id,
                    message_id=message.message_id,
                    reaction=[ReactionEmoji.EYES]
                )
            except Exception as e:
                logger.error(f"Failed to add eyes reaction: {e}")

            # Check if media filter is enabled
            media_filter_enabled = await self.db.get_group_setting(chat_id, 'media_filter_enabled', False)

            if media_filter_enabled:
                logger.info(f"Media filter is enabled for chat {chat_id}, proceeding with NSFW media detection")

                # Create a unique key for this moderation request
                moderation_key = f"media:{chat_id}:{message.message_id}"

                # Check if this message is already being moderated
                if moderation_key not in self.currently_moderating:
                    # Mark this message as being moderated
                    self.currently_moderating.add(moderation_key)

                    logger.info(f"Starting AI media moderation for message {message.message_id}")

                    async def run_media_moderation():
                        try:
                            await self._moderate_media(update, context)
                        finally:
                            self.currently_moderating.discard(moderation_key)

                    task = asyncio.create_task(run_media_moderation())
                    self.moderation_tasks.add(task)
                    task.add_done_callback(self.moderation_tasks.discard)

                    logger.info(f"Started background media moderation task for message {message.message_id}")
                else:
                    logger.info(f"Message {message.message_id} is already being moderated, skipping duplicate moderation")
            else:
                logger.info(f"Media filter is disabled for chat {chat_id}, skipping NSFW media detection")
        except Exception as e:
            logger.error(f"Error processing media content: {e}")
            logger.error(traceback.format_exc())

    async def _moderate_text(self, update: Update, context: ContextTypes.DEFAULT_TYPE, text: str) -> None:
        """Enhanced 3-layer text moderation system with TypeGPT integration"""
        try:
            chat_id = update.effective_chat.id
            user_id = update.effective_user.id
            message_id = update.effective_message.message_id

            # Create a unique key for this moderation request
            moderation_key = f"mod:{chat_id}:{message_id}"

            # Check if we've already moderated this message
            if moderation_key in self._processed_messages:
                logger.info(f"Skipping already moderated message {message_id} in chat {chat_id}")
                return

            # Add this message to the processed set
            self._processed_messages.add(moderation_key)

            logger.info(f"🤖 Starting ENHANCED 3-LAYER moderation for message ID {message_id} from user {user_id} in chat {chat_id}")
            print(f"\n🔍 Analyzing message: {text[:100]}{'...' if len(text) > 100 else ''}")

            # LAYER 1: Enhanced NSFW word detection (our existing 100x better algorithm)
            logger.info("📋 LAYER 1: Running enhanced NSFW word detection from JSON database...")
            found_words = self._check_nsfw_words(text)
            
            if not found_words:
                logger.info(f"✅ LAYER 1 PASSED: No NSFW words found in message {message_id}")
                print("✅ Layer 1 (JSON Database): CLEAN - No further analysis needed")
                return

            logger.info(f"🚨 LAYER 1 FLAGGED: Found {len(found_words)} NSFW indicators: {found_words}")
            print(f"🚨 Layer 1 (JSON Database): FLAGGED - Found {len(found_words)} words: {found_words}")

            # LAYER 2: TypeGPT API moderation (new requirement)
            logger.info("🔍 LAYER 2: Sending to TypeGPT moderation API...")
            print(f"🔍 Layer 2 (TypeGPT API): Analyzing content...")
            
            moderator = await self._get_typegpt_moderator()
            typegpt_result = None
            layer2_flagged = False

            if moderator:
                try:
                    layer2_flagged, typegpt_result = await moderator.moderate_text(text)
                    
                    if typegpt_result:
                        logger.info(f"🎯 LAYER 2 RESULT: Flagged = {layer2_flagged}")
                        if layer2_flagged:
                            logger.info(f"🚨 LAYER 2 FLAGGED: {typegpt_result}")
                            print(f"🚨 Layer 2 (TypeGPT API): FLAGGED - {typegpt_result}")
                        else:
                            logger.info(f"✅ LAYER 2 PASSED: TypeGPT classified as safe despite JSON words")
                            print(f"✅ Layer 2 (TypeGPT API): SAFE - Despite JSON detection")
                    else:
                        logger.warning("⚠️ LAYER 2 FAILED: TypeGPT API returned no result")
                        print("⚠️ Layer 2 (TypeGPT API): API ERROR - Continuing to Layer 3")
                except Exception as e:
                    logger.error(f"❌ LAYER 2 ERROR: TypeGPT API failed: {e}")
                    print(f"❌ Layer 2 (TypeGPT API): ERROR - {e}")
                    layer2_flagged = False
                    typegpt_result = None
            else:
                logger.warning("⚠️ LAYER 2 UNAVAILABLE: TypeGPT client not initialized")
                print("⚠️ Layer 2 (TypeGPT API): UNAVAILABLE - Continuing to Layer 3")

            # Decision logic: If TypeGPT says it's safe, trust it and skip Layer 3
            if typegpt_result and not layer2_flagged:
                logger.info(f"✅ LAYER 2 OVERRIDE: TypeGPT classified as safe, skipping AI moderator")
                print(f"✅ Layer 2 Override: Content deemed safe by TypeGPT API")
                print(f"📝 Note: JSON detected words but TypeGPT API found content appropriate")
                return

            # LAYER 3: AI Moderator Analysis (enhanced with previous layer context)
            logger.info("🤖 LAYER 3: Sending to AI moderator with enhanced context...")
            print(f"🤖 Layer 3 (AI Moderator): Final analysis with full context...")
            
            # Build comprehensive context for the AI
            context_messages = []
            
            # Add Layer 1 results
            layer1_context = (
                f"LAYER 1 DETECTION (JSON Database):\n"
                f"- Found NSFW words: {', '.join(found_words)}\n"
                f"- Detection confidence: HIGH (multi-phase algorithm)\n"
                f"- Message length: {len(text)} characters"
            )
            context_messages.append(layer1_context)
            
            # Add Layer 2 results if available
            if typegpt_result:
                layer2_context = (
                    f"LAYER 2 DETECTION (TypeGPT API):\n"
                    f"- API flagged: {layer2_flagged}\n"
                    f"- Model: text-moderation-007\n"
                )
                if layer2_flagged and typegpt_result:
                    categories = list(typegpt_result.get('categories', {}).keys())
                    layer2_context += (
                        f"- Categories: {categories}\n"
                        f"- Reason: {typegpt_result.get('reason', 'No reason provided')}\n"
                        f"- Category Scores: {typegpt_result.get('category_scores', {})}"
                    )
                else:
                    layer2_context += "- Result: Content classified as safe"
                context_messages.append(layer2_context)
            
            enhanced_context = (
                f"ENHANCED 3-LAYER MODERATION REQUEST:\n"
                f"- User ID: {user_id}\n"
                f"- Message ID: {message_id}\n"
                f"- Detection layers triggered: {1 + (1 if typegpt_result else 0)}\n"
                f"Please provide final decision considering all layer results above."
            )
            context_messages.append(enhanced_context)
            
            logger.info(f"🧠 Enhanced context prepared with {len(context_messages)} layers of analysis")

            try:
                is_nsfw, details = await self.ai_moderator.analyze_content(
                    text=text,
                    user_id=str(user_id),
                    message_id=str(message_id),
                    context=context_messages
                )

                logger.info(f"🎯 LAYER 3 RESULT: AI moderator NSFW = {is_nsfw}")
                print(f"🎯 Layer 3 (AI Moderator): {'🚨 NSFW CONFIRMED' if is_nsfw else '✅ CONTENT IS SAFE'}")
                
                if details:
                    category = details.get('category', 'UNKNOWN')
                    confidence = details.get('confidence', 0)
                    reason = details.get('reason', 'No reason provided')
                    severity = details.get('severity', 0)
                    
                    print(f"📊 Final Analysis:")
                    print(f"   Category: {category}")
                    print(f"   Confidence: {confidence}%")
                    print(f"   Severity: {severity}/4")
                    print(f"   Reason: {reason}")

                # FINAL DECISION PROCESSING
                if not is_nsfw:
                    # AI determined the message is safe despite other layers flagging it
                    logger.info(f"✅ FINAL DECISION: AI classified as SAFE despite previous layer detections")
                    print(f"✅ FINAL DECISION: Message is SAFE")
                    print(f"🧠 AI Override: Context analysis deemed content appropriate")
                    
                    # Log the comprehensive override decision
                    override_details = {
                        "layer1_found_words": found_words,
                        "layer2_flagged": layer2_flagged,
                        "layer3_override": True,
                        "ai_reasoning": details.get('reason', 'Context analysis deemed content appropriate'),
                        "final_decision": "SAFE"
                    }
                    
                    logger.info(f"📋 3-LAYER ANALYSIS COMPLETE: {override_details}")
                    return

                # CONTENT CONFIRMED AS NSFW - SEND ENHANCED MODERATION UI
                logger.info("🚨 FINAL DECISION: Content CONFIRMED as NSFW by 3-layer analysis")
                print("🚨 FINAL DECISION: Content is UNSAFE - Sending to admins")
                
                # Enhance details with all layer information
                if not details:
                    details = {}
                
                details.update({
                    'layer1_words': found_words,
                    'layer2_result': typegpt_result,
                    'layer2_flagged': layer2_flagged,
                    'layer3_confirmed': True,
                    'detection_method': '3-Layer Enhanced Detection (JSON + TypeGPT + AI)',
                    'detection_layers': 3,
                    'confidence_sources': ['JSON Database', 'TypeGPT API', 'AI Moderator']
                })
                
                # Add TypeGPT specific details if available
                if typegpt_result and layer2_flagged:
                    details.update({
                        'typegpt_categories': typegpt_result.get('categories', {}),
                        'typegpt_category_scores': typegpt_result.get('category_scores', {}),
                        'typegpt_reason': typegpt_result.get('reason', 'No reason provided')
                    })

                await self._send_enhanced_moderation_confirmation(update, context, details)

            except Exception as ai_error:
                logger.error(f"🚨 LAYER 3 FAILED: AI moderation error: {ai_error}")
                print(f"❌ Layer 3 (AI Moderator): ERROR - {ai_error}")
                
                # Enhanced fallback with TypeGPT and JSON data
                logger.info("🔄 ENHANCED FALLBACK: Using multi-layer fallback logic...")
                
                # Use TypeGPT result if available and flagged
                if typegpt_result and layer2_flagged:
                    logger.info("🚨 FALLBACK DECISION: Using TypeGPT API result (UNSAFE)")
                    print("🚨 Fallback: TypeGPT API confirmed content is unsafe")
                    
                    details = {
                        "category": "NSFW_CONTENT",
                        "confidence": 85,  # Default confidence for TypeGPT fallback
                        "reason": f"AI failed but TypeGPT API detected: {list(typegpt_result.get('categories', {}).keys())}",
                        "severity": 2,  # Default severity for TypeGPT fallback
                        "action_recommended": "Remove message and warn user",
                        "layer1_words": found_words,
                        "layer2_result": typegpt_result,
                        "layer2_flagged": layer2_flagged,
                        "ai_fallback": True,
                        "detection_method": "Enhanced Fallback (JSON + TypeGPT)",
                        "fallback_reason": "AI moderator failed but TypeGPT confirmed violation",
                        "typegpt_categories": typegpt_result.get('categories', {}),
                        "typegpt_reason": typegpt_result.get('reason', 'No reason provided')
                    }
                    
                    await self._send_enhanced_moderation_confirmation(update, context, details)
                    
                else:
                    # Use word severity fallback (existing logic)
                    critical_words = {'fuck', 'shit', 'cunt', 'bitch', 'porn', 'sex', 'nude', 'pussy', 'dick'}
                    detected_critical = [w for w in found_words if w.lower() in critical_words]
                    
                    if detected_critical:
                        logger.warning(f"🚨 FALLBACK DECISION: Critical words detected (UNSAFE): {detected_critical}")
                        print(f"🚨 Fallback: Critical words require action: {detected_critical}")
                        
                        details = {
                            "category": "NSFW_LANGUAGE",
                            "confidence": 80,
                            "reason": f"AI and TypeGPT failed but critical NSFW words detected: {', '.join(detected_critical)}",
                            "severity": 3,
                            "action_recommended": "Remove message and warn user",
                            "layer1_words": found_words,
                            "critical_words": detected_critical,
                            "ai_fallback": True,
                            "detection_method": "Enhanced Fallback (Critical Words)",
                            "fallback_reason": "Both AI and TypeGPT failed but critical violations detected"
                        }
                        
                        await self._send_enhanced_moderation_confirmation(update, context, details)
                    else:
                        logger.info(f"✅ FALLBACK DECISION: Only minor words detected (SAFE): {found_words}")
                        print(f"✅ Fallback: Minor words only, message deemed safe")

        except Exception as e:
            logger.error(f"Error in enhanced 3-layer text moderation: {e}")
            logger.error(traceback.format_exc())
            print(f"❌ Critical error in 3-layer moderation: {e}")

    async def _send_enhanced_moderation_confirmation(self, update: Update, context: ContextTypes.DEFAULT_TYPE, details: Dict) -> None:
        """Send savage moderation confirmation with 30-second auto-delete"""
        if not update.effective_message or not update.effective_chat:
            return

        chat_id = update.effective_chat.id
        message_id = update.effective_message.message_id

        # Check if we've already sent a moderation message for this message
        moderation_key = f"modmsg:{chat_id}:{message_id}"
        if moderation_key in self._processed_messages:
            logger.info(f"Skipping duplicate moderation message for message {message_id} in chat {chat_id}")
            return

        # Add this message to the processed set to prevent duplicates
        self._processed_messages.add(moderation_key)

        case_id = str(uuid.uuid4())[:8]  # Shorter case ID for better UX

        # Enhanced logging with more details
        logger.info(f"🚨 ENHANCED NSFW DETECTION ALERT 🚨")
        logger.info(f"📍 Message ID: {message_id} | Chat ID: {chat_id}")
        logger.info(f"👤 User: {update.effective_user.id} ({update.effective_user.full_name})")
        logger.info(f"📊 Category: {details.get('category', 'Unknown')}")
        logger.info(f"📊 Confidence: {details.get('confidence', 0)}%")
        logger.info(f"📊 Severity: {details.get('severity', 0)}/4")
        logger.info(f"🔍 Detection Method: {details.get('detection_method', 'Standard')}")
        logger.info(f"🎯 Found Words: {details.get('found_words', [])}")
        logger.info(f"🤖 AI Confirmed: {details.get('ai_confirmed', False)}")
        
        # Create professional moderation keyboard with comprehensive actions  
        keyboard = [
            [
                InlineKeyboardButton("🚫 Delete & Ban", callback_data=f"mod_ban_{case_id}"),
                InlineKeyboardButton("⚠️ Delete & Warn", callback_data=f"mod_confirm_{case_id}")
            ],
            [
                InlineKeyboardButton("🔇 Mute User", callback_data=f"mod_mute_{case_id}"),
                InlineKeyboardButton("⏰ Temp Ban", callback_data=f"mod_tban_{case_id}")
            ],
            [
                InlineKeyboardButton("✅ False Positive", callback_data=f"mod_reject_{case_id}"),
                InlineKeyboardButton("🔍 View Analysis", callback_data=f"mod_details_{case_id}")
            ]
        ]

        reply_markup = InlineKeyboardMarkup(keyboard)

        # Save enhanced case details
        self.moderation_cases[case_id] = {
            "message_id": message_id,
            "chat_id": chat_id,
            "user_id": update.effective_user.id,
            "details": details,
            "status": "pending",
            "created_at": datetime.now().isoformat(),
            "original_message": update.effective_message.text or update.effective_message.caption or "",
            "detection_method": details.get('detection_method', 'Standard'),
            "ai_confirmed": details.get('ai_confirmed', False)
        }

        # Professional moderation message
        severity = int(details.get('severity', 2))
        severity_levels = ["Low", "Medium", "High", "Critical", "Extreme"]
        severity_emojis = ["🟡", "🟠", "🔴", "🚨", "💀"]
        
        severity_level = severity_levels[min(severity, 4)]
        severity_emoji = severity_emojis[min(severity, 4)]
        
        # Format found words as spoilers for admin review
        found_words = details.get('found_words', [])
        if found_words:
            display_words = [w for w in found_words if w != 'contextual_nsfw']
            if display_words:
                found_words_spoiler = f"<tg-spoiler>{', '.join(display_words[:5])}</tg-spoiler>"
                if len(display_words) > 5:
                    found_words_spoiler += f" <i>+{len(display_words)-5} more</i>"
            else:
                found_words_spoiler = "<i>Context-based detection</i>"
        else:
            found_words_spoiler = "<i>AI pattern recognition</i>"

        # Professional admin message
        admin_message = (
            f"╭─「 <b>🛡️ Content Moderation Alert</b> 」\n"
            f"│\n"
            f"├─「 <b>👤 User Information</b> 」\n"
            f"│ • <b>User:</b> {update.effective_user.mention_html()}\n"
            f"│ • <b>ID:</b> <code>{update.effective_user.id}</code>\n"
            f"│ • <b>Message ID:</b> <code>{message_id}</code>\n"
            f"│\n"
            f"├─「 <b>📊 Detection Analysis</b> 」\n"
            f"│ • <b>Category:</b> {details.get('category', 'UNKNOWN').replace('_', ' ').title()}\n"
            f"│ • <b>Severity:</b> {severity_emoji} {severity_level} ({details.get('confidence', 0)}%)\n"
            f"│ • <b>Method:</b> {details.get('detection_method', 'Standard Detection')}\n"
            f"│ • <b>Detection:</b> Content flagged by system\n"
            f"│\n"
            f"├─「 <b>⚡ Actions Available</b> 」\n"
            f"│ Choose appropriate action based on severity\n"
            f"│ All actions are logged and reversible\n"
            f"│\n"
            f"╰─「 <b>Case ID:</b> <code>{case_id}</code> 」"
        )

        try:
            mod_msg = await context.bot.send_message(
                chat_id=chat_id,
                text=admin_message,
                parse_mode=ParseMode.HTML,
                reply_markup=reply_markup,
                reply_to_message_id=message_id
            )
            
            # Content Moderation Alert stays persistent for admin review
            
            logger.info(f"✅ Savage moderation confirmation sent for case {case_id}")
            print(f"✅ Savage moderation UI sent to admins for case {case_id}")
            
        except Exception as e:
            logger.error(f"Error sending savage moderation confirmation: {e}")
            print(f"❌ Failed to send moderation confirmation: {e}")

    async def _moderate_media(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Enhanced media moderation with OCR and 3-layer analysis"""
        try:
            message = update.message
            chat_id = update.effective_chat.id
            user_id = update.effective_user.id
            file_id = None
            temp_file = None

            # Get file ID based on media type
            if message.photo:
                file_id = message.photo[-1].file_id
            elif message.animation:
                file_id = message.animation.file_id
            elif message.video:
                file_id = message.video.file_id
            elif message.sticker:
                file_id = message.sticker.file_id
            elif message.document and (message.document.mime_type or '').startswith(('image/', 'video/')):
                file_id = message.document.file_id

            if not file_id:
                return

            logger.info(f"🖼️ Starting enhanced media moderation with OCR for message {message.message_id}")
            print(f"🖼️ Processing media with OCR analysis...")

            # Download and process file
            file = await context.bot.get_file(file_id)
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=self._get_file_extension(message))
            self._temp_files.add(temp_file.name)

            try:
                await file.download_to_memory(temp_file)
                temp_file.close()  # Close the file handle so it can be read by other processes

                # STEP 1: OCR Text Extraction (for images)
                ocr_text = ""
                if message.photo or (message.document and (message.document.mime_type or '').startswith('image/')):
                    logger.info("📝 STEP 1: Extracting text from image using OCR...")
                    print("📝 Step 1: Extracting text from image...")
                    
                    # Note: OCR functionality removed as TypeGPT moderation API handles text extraction internally
                    logger.info("📝 OCR functionality integrated into TypeGPT moderation API")
                    print("📝 OCR: Integrated with TypeGPT moderation")

                # STEP 2: Text Analysis (if OCR found text)
                text_violation_detected = False
                if ocr_text and len(ocr_text.strip()) > 2:
                    logger.info(f"📋 STEP 2: Analyzing extracted text through 3-layer system...")
                    print(f"📋 Step 2: Analyzing extracted text: '{ocr_text[:50]}{'...' if len(ocr_text) > 50 else ''}'")
                    
                    # Use our 3-layer text detection system
                    text_details = await self._analyze_text_through_layers(ocr_text, user_id, message.message_id)
                    
                    if text_details and text_details.get('is_violation', False):
                        text_violation_detected = True
                        logger.info("🚨 STEP 2 RESULT: Text violation detected in image")
                        print("🚨 Step 2 Result: NSFW text found in image!")
                        
                        # Immediately delete the message for text violations
                        try:
                            await context.bot.delete_message(chat_id, message.message_id)
                            logger.info(f"✅ Deleted image with NSFW text: {message.message_id}")
                            print("✅ Image deleted due to NSFW text content")
                        except Exception as e:
                            logger.error(f"Failed to delete image with NSFW text: {e}")

                        # Add warning for text violation in image
                        await self.db.add_warning_sqlite(
                            chat_id=chat_id,
                            user_id=user_id,
                            admin_id=context.bot.id,
                            reason=f"NSFW text in image: {text_details.get('reason', 'Text violation detected')}"
                        )

                        # Send warning message
                        warning_msg = (
                            f"🚨 <b>NSFW Text Detected in Image!</b>\n\n"
                            f"👤 <b>User:</b> {update.effective_user.mention_html()}\n"
                            f"📝 <b>Detected Text:</b> <tg-spoiler>{ocr_text[:100]}{'...' if len(ocr_text) > 100 else ''}</tg-spoiler>\n"
                            f"🔍 <b>Analysis:</b> {text_details.get('reason', 'Multiple detection layers flagged content')}\n"
                            f"⚡ <b>Action:</b> Image deleted and warning added\n\n"
                            f"<i>💡 Images with NSFW text are automatically removed by HelpingAI BOT (AI ADMIN)</i>"
                        )
                        
                        warning_message = await context.bot.send_message(
                            chat_id=chat_id,
                            text=warning_msg,
                            parse_mode=ParseMode.HTML
                        )
                        
                        # Auto-delete warning after 60 seconds
                        asyncio.create_task(self._auto_delete_message(context, chat_id, warning_message.message_id, 60))
                        
                        return  # Exit early since we've handled the violation
                    else:
                        logger.info("✅ STEP 2 RESULT: Extracted text is clean")
                        print("✅ Step 2 Result: Extracted text is appropriate")

                # STEP 3: Visual Content Analysis (if no text violation)
                if not text_violation_detected:
                    logger.info("🖼️ STEP 3: Analyzing visual content for NSFW material...")
                    print("🖼️ Step 3: Analyzing visual content...")

                    # Process based on media type with callback
                    async def handle_visual_result(is_nsfw: bool, scores: Dict):
                        if is_nsfw:
                            logger.info("🚨 STEP 3 RESULT: Visual NSFW content detected")
                            print("🚨 Step 3 Result: NSFW visual content detected!")
                            
                            details = {
                                'category': 'NSFW_VISUAL',
                                'confidence': int(scores.get('nsfw', 0) * 100),
                                'reason': 'NSFW visual content detected in image/video',
                                'severity': 3,
                                'action_recommended': 'Delete image and warn user',
                                'scores': scores,
                                'message_id': str(message.message_id),
                                'ocr_text': ocr_text if ocr_text else 'No text detected',
                                'detection_method': 'Enhanced Media Analysis (OCR + Visual)'
                            }
                            
                            # Auto-delete offending message
                            try:
                                await context.bot.delete_message(chat_id, message.message_id)
                                logger.info(f"✅ Deleted NSFW visual content: {message.message_id}")
                                print("✅ Image deleted due to NSFW visual content")
                            except Exception as e:
                                logger.error(f"Failed to delete NSFW visual message: {e}")

                            # Add warning
                            await self.db.add_warning_sqlite(
                                chat_id=chat_id,
                                user_id=user_id,
                                admin_id=context.bot.id,
                                reason=details['reason']
                            )

                            # Send enhanced warning
                            await self._send_media_warning(update, context, details)
                        else:
                            logger.info("✅ STEP 3 RESULT: Visual content is appropriate")
                            print("✅ Step 3 Result: Visual content is clean")

                    # Get TypeGPT moderator
                    moderator = await self._get_typegpt_moderator()
                    if not moderator:
                        logger.error("❌ TypeGPT moderator not available")
                        print("❌ Visual analysis unavailable: TypeGPT moderator not initialized")
                        return

                    # Process media with TypeGPT moderator
                    try:
                        is_flagged = False
                        moderation_details = {}
                        
                        if message.photo or (message.document and (message.document.mime_type or '').startswith('image/') and not (message.document.mime_type or '').startswith('image/gif')):
                            is_flagged, moderation_details = await moderator.moderate_image(temp_file.name)
                        elif message.animation or (message.document and (message.document.mime_type or '').startswith('image/gif')):
                            is_flagged, moderation_details = await moderator.moderate_video(temp_file.name, frame_count=4)
                        elif message.video or (message.document and (message.document.mime_type or '').startswith('video/')):
                            is_flagged, moderation_details = await moderator.moderate_video(temp_file.name)
                        elif message.sticker:
                            is_flagged, moderation_details = await moderator.moderate_sticker(temp_file.name)
                        
                        # Convert TypeGPT response to expected format for callback
                        if is_flagged:
                            # Create scores dict from category_scores
                            scores = moderation_details.get('category_scores', {})
                            # Add nsfw score for backward compatibility
                            scores['nsfw'] = max(scores.values()) if scores else 0.8
                        else:
                            scores = {'nsfw': 0.0}
                        
                        # Call the callback with results
                        await handle_visual_result(is_flagged, scores)
                        
                    except Exception as e:
                        logger.error(f"Error during TypeGPT visual moderation: {e}")
                        print(f"❌ Visual analysis error: {e}")

            finally:
                # Cleanup temporary file
                try:
                    if temp_file and temp_file.name in self._temp_files:
                        self._temp_files.remove(temp_file.name)
                        os.unlink(temp_file.name)
                        logger.info("🗑️ Cleaned up temporary media file")
                except Exception as e:
                    logger.error(f"Error cleaning up temporary file: {e}")

        except Exception as e:
            logger.error(f"Error in enhanced media moderation: {e}")
            logger.error(traceback.format_exc())
            print(f"❌ Media moderation error: {e}")

    async def _analyze_text_through_layers(self, text: str, user_id: int, message_id: int) -> Optional[Dict]:
        """Analyze text through the 3-layer system and return violation details"""
        try:
            # Layer 1: JSON word detection
            found_words = self._check_nsfw_words(text)
            if not found_words:
                return {'is_violation': False, 'reason': 'No NSFW words detected'}

            # Layer 2: TypeGPT moderation
            moderator = await self._get_typegpt_moderator()
            layer2_flagged = False
            typegpt_categories = []
            moderation_details = {}
            
            if moderator:
                try:
                    layer2_flagged, moderation_details = await moderator.moderate_text(text)
                    if moderation_details:
                        typegpt_categories = list(moderation_details.get('categories', {}).keys())
                except Exception as e:
                    logger.error(f"Layer 2 analysis failed: {e}")

            # If TypeGPT says it's safe, trust it
            if moderation_details and not layer2_flagged:
                return {'is_violation': False, 'reason': 'TypeGPT API classified as safe'}

            # Layer 3: AI Moderator (simplified for OCR text)
            try:
                context_messages = [
                    f"Analyzing text extracted from image: {', '.join(found_words)} words detected",
                    f"TypeGPT result: {'Flagged' if layer2_flagged else 'Safe'} with categories: {typegpt_categories}"
                ]
                
                is_nsfw, details = await self.ai_moderator.analyze_content(
                    text=text,
                    user_id=str(user_id),
                    message_id=str(message_id),
                    context=context_messages
                )
                
                if is_nsfw:
                    return {
                        'is_violation': True,
                        'reason': f"3-layer analysis confirmed NSFW text in image: {details.get('reason', 'Multiple violations')}",
                        'found_words': found_words,
                        'typegpt_flagged': layer2_flagged,
                        'ai_confirmed': True
                    }
                else:
                    return {'is_violation': False, 'reason': 'AI classified as safe despite word detection'}
                    
            except Exception as e:
                # Fallback: Use critical word detection
                critical_words = {'fuck', 'shit', 'cunt', 'bitch', 'porn', 'sex', 'nude', 'pussy', 'dick'}
                detected_critical = [w for w in found_words if w.lower() in critical_words]
                
                if detected_critical:
                    return {
                        'is_violation': True,
                        'reason': f"Critical NSFW words in image text: {', '.join(detected_critical)}",
                        'found_words': found_words,
                        'critical_words': detected_critical,
                        'fallback': True
                    }
                else:
                    return {'is_violation': False, 'reason': 'Only minor words detected in image'}
                    
        except Exception as e:
            logger.error(f"Error in text layer analysis: {e}")
            return {'is_violation': False, 'reason': f'Analysis error: {e}'}

    async def _send_media_warning(self, update: Update, context: ContextTypes.DEFAULT_TYPE, details: Dict) -> None:
        """Send savage warning for media violations"""
        try:
            chat_id = update.effective_chat.id
            user_mention = update.effective_user.mention_html()
            
            # Get current warnings
            current_warns = await self.db.get_user_warn_count(chat_id, update.effective_user.id)
            new_warns = current_warns + 1
            max_warns = 5
            
            ocr_text = details.get('ocr_text', '')
            scores = details.get('scores', {})
            
            warning_msg = (
                f"🔥 <b>CAUGHT YOU RED-HANDED!</b> 🔥\n\n"
                f"👤 <b>Victim:</b> {user_mention}\n"
                f"💀 <b>Crime:</b> {details.get('category', 'NSFW_VISUAL').replace('_', ' ').replace('NSFW', 'Inappropriate')}\n"
                f"🎯 <b>How Sure:</b> {details.get('confidence', 0)}% busted!\n"
                f"⚠️ <b>Strike:</b> #{new_warns}/{max_warns}\n\n"
            )
            
            if ocr_text and len(ocr_text.strip()) > 0:
                warning_msg += f"📝 <b>Hidden Text Found:</b> <tg-spoiler>{ocr_text[:50]}{'...' if len(ocr_text) > 50 else ''}</tg-spoiler>\n"
            
            warning_msg += f"\n💥 <b>Action:</b> Content deleted & strike added\n"
            
            if new_warns >= max_warns:
                warning_msg += f"🚫 <b>YOU'RE OUTTA HERE!</b> Too many strikes! 🚫\n"
            else:
                warning_msg += f"⚠️ <b>{max_warns - new_warns} more strikes and you're BANNED!</b>\n"
                
            warning_msg += f"\n<i>💀 HelpingAI BOT (AI ADMIN) doesn't mess around</i>\n"
            warning_msg += f"<i>💥 This message will vanish in 30 seconds</i>"
            
            warning_message = await context.bot.send_message(
                chat_id=chat_id,
                text=warning_msg,
                parse_mode=ParseMode.HTML
            )
            
            # Auto-delete after 30 seconds
            asyncio.create_task(self._auto_delete_message(context, chat_id, warning_message.message_id, 30))
            
            logger.info(f"✅ Savage media warning sent for user {update.effective_user.id}")
            
        except Exception as e:
            logger.error(f"Error sending media warning: {e}")

    def _is_supported_mime_type(self, mime_type: str) -> bool:
        """Check if the mime type is supported for NSFW detection"""
        if not mime_type:
            return False
        
        supported_types = [
            'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
            'video/mp4', 'video/avi', 'video/mov', 'video/webm'
        ]
        
        return mime_type.lower() in supported_types or \
               mime_type.lower().startswith(('image/', 'video/'))

    def _get_file_extension(self, message) -> str:
        """Get appropriate file extension based on message type"""
        if message.photo:
            return '.jpg'
        elif message.animation:
            return '.gif'
        elif message.video:
            return '.mp4'
        elif message.document and message.document.mime_type:
            if message.document.mime_type.startswith('image/gif'):
                return '.gif'
            elif message.document.mime_type.startswith('image/'):
                return '.jpg'
            elif message.document.mime_type.startswith('video/'):
                return '.mp4'
        elif message.sticker:
            if message.sticker.is_animated:
                return '.tgs'
            elif message.sticker.is_video:
                return '.webm'
            return '.webp'
        return ''

    async def _check_message_content(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Check message content for NSFW words and other violations"""
        if not update.effective_message:
            return

        # Get message text
        text = update.effective_message.text or update.effective_message.caption or ""
        if not text:
            return

        chat_id = update.effective_chat.id

        # Check for NSFW content
        nsfw_filter_enabled = await self.db.get_group_setting(chat_id, 'nsfw_filter_enabled', False)
        if nsfw_filter_enabled:
            try:
                # First check with AI moderator
                is_nsfw, details = await self.ai_moderator.analyze_content(text, str(update.effective_user.id), str(update.effective_message.message_id))

                # If AI fails, mark as safe
                if details is None:
                    logger.warning("AI moderation failed, marking message as safe")
                    details = {
                        "category": "SAFE",
                        "confidence": 100,
                        "reason": "AI moderation unavailable - marked as safe",
                        "severity": 0,
                        "action_recommended": "No action required",
                        "message_id": str(update.effective_message.message_id)
                    }
                    is_nsfw = False

                if is_nsfw:
                    # Send a confirmation message with buttons to verify AI moderation
                    await self._send_moderation_confirmation(update, context, details)
                    return
            except Exception as e:
                logger.error(f"Error in AI moderation: {e}")
                # Mark as safe in case of error
                details = {
                    "category": "SAFE",
                    "confidence": 100,
                    "reason": "AI moderation error - marked as safe",
                    "severity": 0,
                    "action_recommended": "No action required",
                    "message_id": str(update.effective_message.message_id)
                }

    async def _send_moderation_confirmation(self, update: Update, context: ContextTypes.DEFAULT_TYPE, details: Dict) -> None:
        """Send confirmation for moderators to review detected content"""
        if not update.effective_message or not update.effective_chat:
            return

        chat_id = update.effective_chat.id
        message_id = update.effective_message.message_id

        # Check if we've already sent a moderation message for this message
        moderation_key = f"modmsg:{chat_id}:{message_id}"
        if moderation_key in self._processed_messages:
            logger.info(f"Skipping duplicate moderation message for message {message_id} in chat {chat_id}")
            return

        # Add this message to the processed set to prevent duplicates
        self._processed_messages.add(moderation_key)

        case_id = str(uuid.uuid4())

        # Log details of the NSFW detection for debugging
        logger.info(f"DETECTED NSFW CONTENT in message {message_id} from chat {chat_id}")
        logger.info(f"Content type: {details.get('category', 'Unknown')}")
        logger.info(f"Confidence: {details.get('confidence', 0)}%")
        logger.info(f"Found words: {details.get('found_words', [])}")
        logger.info(f"User: {update.effective_user.id} ({update.effective_user.full_name})")

        # Create keyboard for admin confirmation
        keyboard = [
            [
                InlineKeyboardButton("🚫 Remove & Warn", callback_data=f"mod_confirm_{case_id}"),
                InlineKeyboardButton("✅ False Alarm", callback_data=f"mod_reject_{case_id}")
            ]
        ]

        reply_markup = InlineKeyboardMarkup(keyboard)

        # Save case details
        self.moderation_cases[case_id] = {
            "message_id": message_id,
            "chat_id": chat_id,
            "user_id": update.effective_user.id,
            "details": details,
            "status": "pending",
            "created_at": datetime.now().isoformat()
        }

        # Create sassy moderation message
        severity_emojis = ["😬", "😨", "🤯", "💀"]
        severity = int(details.get('severity', 2))
        emoji = severity_emojis[min(severity - 1, 3)]

        # Check if this contains NSFW words
        has_nsfw_words = 'found_words' in details and len(details.get('found_words', [])) > 0

        if has_nsfw_words:
            # For messages with detected NSFW words
            # Format the found words as a spoiler
            found_words_text = ', '.join(details.get('found_words', []))
            found_words_spoiler = f"<tg-spoiler>{found_words_text}</tg-spoiler>"

            admin_message = (
                f"🚨 <b>NSFW CONTENT DETECTED!</b> {emoji}\n\n"
                f"<b>User:</b> {update.effective_user.mention_html()}\n"
                f"<b>Violation:</b> {details['category'].replace('_', ' ').title()}\n"
                f"<b>Detection:</b> Content flagged by system\n"
                f"<b>Severity:</b> {severity}/4 {emoji}\n\n"
                f"<i>Please review this message and take appropriate action.</i>"
            )
        else:
            # For AI detection without specific words
            admin_message = (
                f"🚨 <b>Caught Someone Acting Sus!</b> {emoji}\n\n"
                f"<b>Suspect:</b> {update.effective_user.mention_html()}\n"
                f"<b>Crime:</b> {details['category'].replace('_', ' ').title()}\n"
                f"<b>AI Confidence:</b> {details['confidence']}% sure this ain't it chief!\n"
                f"<b>Detection:</b> AI pattern analysis flagged content\n"
                f"<b>Spicy Level:</b> {severity}/4 {emoji}\n\n"
                f"<b>Recommended Action:</b> {details['action_recommended']}\n\n"
                f"Admins, what's the verdict? 🤔"
            )

        try:
            await context.bot.send_message(
                chat_id=chat_id,
                text=admin_message,
                parse_mode=ParseMode.HTML,
                reply_markup=reply_markup,
                reply_to_message_id=message_id
            )
        except Exception as e:
            logger.error(f"Error sending moderation confirmation: {e}")

    def _save_moderation_case(self, case_id: str) -> None:
        """Save moderation case to JSON file"""
        try:
            case_data = self.moderation_cases.get(case_id)
            if not case_data:
                return

            # Create directory if it doesn't exist
            logs_dir = Path("logs/moderation_cases")
            logs_dir.mkdir(parents=True, exist_ok=True)

            # Create filename with date
            filename = f"cases_{datetime.now().strftime('%Y%m%d')}.json"
            filepath = logs_dir / filename

            # Append to existing file or create new one
            existing_data = {}
            if filepath.exists():
                with open(filepath, "r", encoding="utf-8") as f:
                    existing_data = json.load(f)

            existing_data[case_id] = case_data

            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(existing_data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            print(f"Error saving moderation case: {e}")

    async def handle_moderation_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle enhanced moderation confirmation callbacks with new actions"""
        query = update.callback_query
        if not query or not query.message:
            return

        action_parts = query.data.split('_')
        if len(action_parts) < 3:
            await query.answer("🤷‍♂️ Invalid action format!")
            return
            
        action = action_parts[1]
        case_id = action_parts[2]
        case = self.moderation_cases.get(case_id)

        if not case:
            await query.answer("🤷‍♂️ This case is older than my jokes!")
            return

        chat_id = case['chat_id']
        message_id = case['message_id']
        user_id = case['user_id']
        details = case['details']

        # Enhanced action handling
        try:
            if action == "confirm":
                await self._handle_delete_and_warn(query, context, case, details)
            elif action == "reject":
                await self._handle_false_alarm(query, context, case, details)
            elif action == "warn":
                await self._handle_warn_only(query, context, case, details)
            elif action == "details":
                await self._handle_show_details(query, context, case, details)
            else:
                await query.answer("❌ Unknown action!")
                return
                
        except Exception as e:
            logger.error(f"Error handling moderation callback: {e}")
            await query.answer("❌ An error occurred while processing your request.")

        await query.answer()

    async def _handle_delete_and_warn(self, query, context, case, details):
        """Handle delete message and warn user action with savage style"""
        chat_id = case['chat_id']
        message_id = case['message_id']
        user_id = case['user_id']
        
        try:
            # Get current warns count
            current_warns = await self.db.get_user_warn_count(chat_id, user_id)
            new_warns = current_warns + 1
            max_warns = 5

            try:
                user = await context.bot.get_chat_member(chat_id, user_id)
                user_mention = user.user.mention_html()
            except:
                user_mention = f"User {user_id}"

            admin_mention = query.from_user.mention_html()

            # Add warning
            await self.db.add_warning_sqlite(
                chat_id=chat_id,
                user_id=user_id,
                admin_id=query.from_user.id,
                reason=f"Inappropriate content: {details.get('reason', 'Content violation detected')}"
            )

            # Savage warning message
            found_words = details.get('found_words', [])
            
            group_warning = (
                f"💀 <b>BUSTED!</b> {user_mention}\n\n"
                f"👮 <b>Enforcer:</b> {admin_mention}\n"
                f"🏷️ <b>Crime:</b> {details.get('category', '').replace('_', ' ').replace('NSFW', 'Inappropriate')}\n"
                f"🎯 <b>Evidence Level:</b> {details.get('confidence')}% busted\n\n"
                f"⚡ <b>Punishment:</b>\n"
                f"• ❌ Message deleted (GONE!)\n"
                f"• ⚠️ Strike #{new_warns} on your record\n"
            )

            if new_warns >= max_warns:
                # Handle ban logic with savage messaging
                try:
                    await context.bot.ban_chat_member(chat_id, user_id)
                    await self.db.log_admin_action(
                        chat_id=chat_id,
                        admin_id=context.bot.id,
                        action="ban",
                        target_user_id=user_id,
                        reason=f"Reached {max_warns} strikes"
                    )

                    try:
                        await self.db.reset_warnings(chat_id, user_id)
                        group_warning += (
                            f"• 🚫 <b>BANNED!</b> Too many strikes - GET OUT! 🚫\n"
                        )
                    except Exception as e:
                        logger.error(f"Failed to reset warnings: {e}")
                        group_warning += (
                            f"• 🚫 <b>BANNED!</b> Too many strikes - GET OUT! 🚫\n"
                        )

                    # Savage ban DM
                    try:
                        ban_msg = await context.bot.send_message(
                            user_id,
                            f"🚫 <b>YOU'VE BEEN KICKED OUT!</b>\n\n"
                            f"Banned from the group for too many violations!\n"
                            f"Final strike: {details.get('category', 'Inappropriate').replace('_', ' ')}\n\n"
                            f"💬 <b>Want Back In?</b>\n"
                            f"Use: <code>/appeal {chat_id} [Your appeal]</code>\n\n"
                            f"<i>💀 Don't mess with HelpingAI BOT (AI ADMIN)</i>\n"
                            f"<i>💥 This message disappears in 5 minutes</i>",
                            parse_mode=ParseMode.HTML
                        )
                        asyncio.create_task(self._auto_delete_message(context, user_id, ban_msg.message_id, 300))
                    except Exception as e:
                        logger.error(f"Failed to send savage ban DM: {e}")

                except Exception as e:
                    logger.error(f"Failed to ban user: {e}")
                    group_warning += "\n❌ <i>Couldn't ban - Missing powers</i>"
            else:
                group_warning += f"\n⚠️ <i>{max_warns - new_warns} more strikes and YOU'RE OUT!</i> 👀"

            group_warning += f"\n\n<i>💥 This message vanishes in 30 seconds</i>"

            # Delete original message and update moderation message
            try:
                await context.bot.delete_message(chat_id=chat_id, message_id=message_id)
                logger.info(f"✅ Deleted message {message_id} via savage moderation")
            except Exception as e:
                logger.error(f"Failed to delete original message: {e}")

            try:
                await query.message.edit_text(
                    text=group_warning,
                    parse_mode=ParseMode.HTML
                )
                
                # Auto-delete after 30 seconds
                asyncio.create_task(self._auto_delete_message(context, chat_id, query.message.message_id, 30))
                
                logger.info(f"✅ Savage warning message sent for case {case.get('case_id', 'unknown')}")
            except Exception as e:
                logger.error(f"Failed to edit warning message: {e}")
                warning_msg = await context.bot.send_message(
                    chat_id=chat_id,
                    text=group_warning,
                    parse_mode=ParseMode.HTML
                )
                asyncio.create_task(self._auto_delete_message(context, chat_id, warning_msg.message_id, 30))

        except Exception as e:
            logger.error(f"Error in savage delete and warn action: {e}")
            await query.message.edit_text(
                f"❌ <b>Error:</b> {str(e)}\n\n<i>💥 Disappearing in 30 seconds</i>",
                parse_mode=ParseMode.HTML
            )
            asyncio.create_task(self._auto_delete_message(context, case['chat_id'], query.message.message_id, 30))

    async def _handle_false_alarm(self, query, context, case, details):
        """Handle false alarm action with savage feedback"""
        case_id = case.get('case_id', 'unknown')
        
        # Update case status
        case["status"] = "rejected"
        case["resolved_by"] = query.from_user.id
        case["resolved_at"] = datetime.now().isoformat()
        case["admin_action"] = "false_alarm"
        
        # Save updated case
        self._save_moderation_case(case_id)

        # Savage false alarm message
        admin_mention = query.from_user.mention_html()
        false_alarm_msg = (
            f"😴 <b>False Alarm - My Bad!</b>\n\n"
            f"👮 <b>Admin Says:</b> {admin_mention}\n"
            f"🤖 <b>AI Confidence:</b> {details.get('confidence', 0)}% (oops!)\n\n"
            f"📝 <b>Verdict:</b> Content is totally fine\n"
            f"🧠 <b>Learning:</b> AI gets better from mistakes\n\n"
            f"<i>💡 Message stays untouched - user is innocent</i>\n"
            f"<i>💥 Disappearing in 30 seconds</i>"
        )

        try:
            await query.message.edit_text(
                text=false_alarm_msg,
                parse_mode=ParseMode.HTML
            )
            
            # Auto-delete false alarm message after 30 seconds
            asyncio.create_task(self._auto_delete_message(
                context, 
                case['chat_id'], 
                query.message.message_id, 
                30
            ))
            
            logger.info(f"✅ False alarm handled for case {case_id} by admin {query.from_user.id}")
            
        except Exception as e:
            logger.error(f"Failed to handle false alarm: {e}")

    async def _handle_warn_only(self, query, context, case, details):
        """Handle warn only action with savage tone"""
        chat_id = case['chat_id']
        user_id = case['user_id']
        
        try:
            # Get current warns
            current_warns = await self.db.get_user_warn_count(chat_id, user_id)
            new_warns = current_warns + 1

            try:
                user = await context.bot.get_chat_member(chat_id, user_id)
                user_mention = user.user.mention_html()
            except:
                user_mention = f"User {user_id}"

            admin_mention = query.from_user.mention_html()

            # Add warning
            await self.db.add_warning_sqlite(
                chat_id=chat_id,
                user_id=user_id,
                admin_id=query.from_user.id,
                reason=f"Warning Only: {details.get('reason', 'Borderline content detected')}"
            )

            warn_only_msg = (
                f"⚠️ <b>You Got Lucky This Time!</b>\n\n"
                f"👤 <b>User:</b> {user_mention}\n"
                f"👮 <b>Merciful Admin:</b> {admin_mention}\n"
                f"🏷️ <b>Why:</b> {details.get('category', '').replace('_', ' ').replace('NSFW', 'Inappropriate')}\n\n"
                f"📊 <b>How Bad:</b> {details.get('confidence', 0)}% sus\n\n"
                f"⚡ <b>What Happened:</b>\n"
                f"• ⚠️ Strike #{new_warns} added to your record\n"
                f"• 📄 Message stays (this time!)\n\n"
                f"<i>💡 Be more careful or you're toast next time</i>\n"
                f"<i>💥 This warning vanishes in 30 seconds</i>"
            )

            await query.message.edit_text(
                text=warn_only_msg,
                parse_mode=ParseMode.HTML
            )
            
            # Auto-delete after 30 seconds
            asyncio.create_task(self._auto_delete_message(
                context, 
                chat_id, 
                query.message.message_id, 
                30
            ))
            
            logger.info(f"✅ Warning-only action completed for user {user_id}")
            
        except Exception as e:
            logger.error(f"Error in warn only action: {e}")

    async def _handle_show_details(self, query, context, case, details):
        """Show detailed analysis information"""
        try:
            detection_method = details.get('detection_method', 'Standard Detection')
            ai_confirmed = details.get('ai_confirmed', False)
            found_words = details.get('found_words', [])
            original_message = case.get('original_message', 'N/A')
            
            # Create detailed analysis message
            details_msg = (
                f"🔍 <b>Detailed Analysis Report</b>\n\n"
                f"📄 <b>Original Message:</b>\n"
                f"<code>{original_message[:200]}{'...' if len(original_message) > 200 else ''}</code>\n\n"
                f"🔬 <b>Detection Details:</b>\n"
                f"• Method: {detection_method}\n"
                f"• AI Verified: {'✅ Yes' if ai_confirmed else '⚠️ Fallback'}\n"
                f"• Category: {details.get('category', 'UNKNOWN')}\n"
                f"• Confidence: {details.get('confidence', 0)}%\n"
                f"• Severity: {details.get('severity', 0)}/4\n\n"
                f"🎯 <b>Found Indicators:</b>\n"
            )
            
            if found_words:
                # Filter and display words appropriately
                display_words = [w for w in found_words if w != 'contextual_nsfw']
                if display_words:
                    details_msg += f"• Words: <tg-spoiler>{', '.join(display_words)}</tg-spoiler>\n"
                else:
                    details_msg += f"• Type: Context-based detection\n"
            else:
                details_msg += f"• Type: Pattern-based detection\n"
                
            details_msg += (
                f"\n📝 <b>AI Analysis:</b>\n"
                f"{details.get('reason', 'No detailed reason provided')}\n\n"
                f"💡 <b>Recommended Action:</b>\n"
                f"{details.get('action_recommended', 'Review required')}\n\n"
                f"<i>This detailed view helps admins make informed decisions</i>"
            )

            # Send as new message to avoid editing the original
            await context.bot.send_message(
                chat_id=query.message.chat_id,
                text=details_msg,
                parse_mode=ParseMode.HTML,
                reply_to_message_id=query.message.message_id
            )
            
            await query.answer("📊 Detailed analysis sent!")
            
        except Exception as e:
            logger.error(f"Error showing details: {e}")
            await query.answer("❌ Failed to show details")

    async def _send_warning(self, update: Update, context: ContextTypes.DEFAULT_TYPE, warning_msg: str, details: Optional[Dict] = None) -> None:
        """Send a savage warning message and auto-delete after 30 seconds"""
        if not update.effective_chat or not update.effective_user:
            return

        chat_id = update.effective_chat.id
        current_time = datetime.now()

        # Rate limit: one warning every 5 seconds per group
        if chat_id in self._last_warning_time:
            time_diff = (current_time - self._last_warning_time[chat_id]).total_seconds()
            if time_diff < 5:
                return

        # Format savage warning with HTML
        full_warning = (
            f"💀 <b>Yo {update.effective_user.mention_html()}!</b>\n\n"
            f"<b>{warning_msg}</b>\n"
        )

        # Add AI details if present
        if details:
            category = details.get('category', '').lower().replace('nsfw_', '').replace('nsfw', 'inappropriate')
            confidence = details.get('confidence', 0)
            reason = details.get('reason', '')

            if category and confidence and reason:
                full_warning += (
                    f"<i>AI caught you! ({category} content, {confidence}% sure) 🤖</i>\n"
                    f"<i>Why: {reason}</i>\n"
                )

        full_warning += (
            f"\n<b>Action:</b> Message yeeted ❌"
            f"\n\n<i>💥 This warning vanishes in 30 seconds</i>"
        )

        # Update last warning time
        self._last_warning_time[chat_id] = current_time

        try:
            # Send warning
            msg = await context.bot.send_message(
                chat_id=chat_id,
                text=full_warning,
                parse_mode=ParseMode.HTML
            )

            # Auto-delete after 30 seconds
            asyncio.create_task(self._auto_delete_message(context, chat_id, msg.message_id, 30))

        except TelegramError as e:
            logging.error(f"Error sending warning: {str(e)}")

    @admin_only()
    @group_only()
    async def setup_protection(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Quick setup for protection features with savage style 🛡️"""
        if not update.effective_chat:
            return

        chat_id = update.effective_chat.id
        
        # Enable all protection features
        await self.db.set_group_setting(chat_id, 'protection_enabled', True)
        await self.db.set_group_setting(chat_id, 'nsfw_filter_enabled', True)
        await self.db.set_group_setting(chat_id, 'media_filter_enabled', True)
        await self.db.set_group_setting(chat_id, 'ai_moderation_enabled', True)
        
        setup_msg = await update.message.reply_text(
            "<b>🔥 PROTECTION IS NOW LIVE!</b>\n\n"
            "✅ Text Filter - ON (catching bad words)\n"
            "✅ Media Filter - ON (scanning images/videos)\n"
            "✅ AI Guardian - ON (smart detection)\n"
            "❌ Admin Immunity - ON (admins are safe)\n\n"
            "<i>💀 HelpingAI BOT (AI ADMIN) is now watching...</i>\n"
            "<i>💥 This message disappears in 30 seconds</i>",
            parse_mode=ParseMode.HTML
        )
        
        # Auto-delete after 30 seconds
        asyncio.create_task(self._auto_delete_message(context, chat_id, setup_msg.message_id, 30))
        
        logger.info(f"Protection setup completed for chat {chat_id}")

    # toggle_protection method removed - functionality moved to /nsfw command

    async def _auto_delete_message(self, context: ContextTypes.DEFAULT_TYPE, chat_id: int, message_id: int, delay: int) -> None:
        """Auto-delete a message after specified delay in seconds"""
        await asyncio.sleep(delay)
        try:
            await context.bot.delete_message(chat_id=chat_id, message_id=message_id)
        except TelegramError:
            pass  # Message might be already deleted

    # ═══════════════════════════════════════════════════════════════════════════════════
    # 🛡️ COMPREHENSIVE PROTECTION COMMAND SYSTEM
    # ═══════════════════════════════════════════════════════════════════════════════════

    async def _send_protection_response(self, update: Update, context: ContextTypes.DEFAULT_TYPE,
                                      title: str, message: str, success: bool = True) -> None:
        """Send a savage protection response message"""
        emoji = "⚡" if success else "💀"
        response_msg = await update.message.reply_text(
            f"<b>{emoji} {title}</b>\n\n{message}\n\n"
            f"<i>This battlefield report self-destructs in 30 seconds</i>",
            parse_mode=ParseMode.HTML
        )
        asyncio.create_task(self._auto_delete_message(context, update.effective_chat.id, response_msg.message_id, 30))

    # ═══════════════════════════════════════════════════════════════════════════════════
    # 🚫 ANTI-SPAM PROTECTION COMMANDS
    # ═══════════════════════════════════════════════════════════════════════════════════

    @admin_only()
    @group_only()
    async def antiflood_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Set flood protection: /antiflood [limit] [action]"""
        if not context.args:
            await self._send_protection_response(
                update, context, "AntiFlood Help",
                "Usage:\n"
                "• /antiflood [limit] [action] - Set flood protection\n"
                "• /antiflood off - Disable flood protection\n\n"
                "Examples:\n"
                "• /antiflood 10 mute - Mute after 10 messages\n"
                "• /antiflood 5 ban - Ban after 5 messages\n"
                "• /antiflood 15 kick - Kick after 15 messages"
            )
            return

        chat_id = update.effective_chat.id
        
        if context.args[0].lower() == "off":
            await self.db.set_group_setting(chat_id, 'antiflood_enabled', False)
            await self._send_protection_response(
                update, context, "AntiFlood Disabled",
                "🔴 Flood protection has been disabled for this group."
            )
            return

        try:
            limit = int(context.args[0])
            action = context.args[1].lower() if len(context.args) > 1 else "mute"
            
            if action not in ["mute", "kick", "ban"]:
                action = "mute"
            
            if limit < 1 or limit > 100:
                await self._send_protection_response(
                    update, context, "Invalid Limit",
                    "❌ Limit must be between 1 and 100 messages.", False
                )
                return

            await self.db.set_group_setting(chat_id, 'antiflood_enabled', True)
            await self.db.set_group_setting(chat_id, 'antiflood_limit', limit)
            await self.db.set_group_setting(chat_id, 'antiflood_action', action)
            
            await self._send_protection_response(
                update, context, "AntiFlood Configured",
                f"🛡️ Flood protection enabled!\n\n"
                f"• Limit: {limit} messages\n"
                f"• Action: {action.upper()}\n"
                f"• Users will be {action}ed for sending more than {limit} messages quickly"
            )
        except (ValueError, IndexError):
            await self._send_protection_response(
                update, context, "Invalid Arguments",
                "❌ Please provide valid numbers and action.\n"
                "Usage: /antiflood [limit] [action]", False
            )

    @admin_only()
    @group_only()
    async def setflood_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Advanced flood setting: /setflood [messages] [time]"""
        if not context.args or len(context.args) < 2:
            await self._send_protection_response(
                update, context, "SetFlood Help",
                "Usage: /setflood [messages] [time]\n\n"
                "Set custom flood detection parameters:\n"
                "• messages: Number of messages (1-50)\n"
                "• time: Time window in seconds (5-300)\n\n"
                "Example: /setflood 15 30"
            )
            return

        try:
            limit = int(context.args[0])
            time_window = int(context.args[1])
            
            if limit < 1 or limit > 50:
                await self._send_protection_response(
                    update, context, "Invalid Message Count",
                    "❌ Message count must be between 1 and 50.", False
                )
                return
            
            if time_window < 5 or time_window > 300:
                await self._send_protection_response(
                    update, context, "Invalid Time Window",
                    "❌ Time window must be between 5 and 300 seconds.", False
                )
                return

            chat_id = update.effective_chat.id
            await self.db.set_group_setting(chat_id, 'antiflood_enabled', True)
            await self.db.set_group_setting(chat_id, 'antiflood_limit', limit)
            await self.db.set_group_setting(chat_id, 'antiflood_time', time_window)
            
            await self._send_protection_response(
                update, context, "Advanced Flood Settings",
                f"🛡️ Advanced flood protection configured!\n\n"
                f"• Messages: {limit}\n"
                f"• Time Window: {time_window} seconds\n"
                f"• Detection: {limit} messages in {time_window} seconds"
            )
        except ValueError:
            await self._send_protection_response(
                update, context, "Invalid Arguments",
                "❌ Please provide valid numbers.\n"
                "Usage: /setflood [messages] [time]", False
            )

    @admin_only()
    @group_only()
    async def floodmode_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Set flood punishment mode: /floodmode [ban/mute/kick]"""
        if not context.args:
            chat_id = update.effective_chat.id
            current_action = await self.db.get_group_setting(chat_id, 'antiflood_action', 'mute')
            await self._send_protection_response(
                update, context, "Current Flood Mode",
                f"Current flood punishment: {current_action.upper()}\n\n"
                "Available modes:\n"
                "• /floodmode ban - Permanently ban users\n"
                "• /floodmode mute - Temporarily mute users\n"
                "• /floodmode kick - Kick users (they can rejoin)"
            )
            return

        action = context.args[0].lower()
        if action not in ["ban", "mute", "kick"]:
            await self._send_protection_response(
                update, context, "Invalid Mode",
                "❌ Valid modes: ban, mute, kick", False
            )
            return

        chat_id = update.effective_chat.id
        await self.db.set_group_setting(chat_id, 'antiflood_action', action)
        
        action_descriptions = {
            "ban": "Users will be permanently banned",
            "mute": "Users will be temporarily muted", 
            "kick": "Users will be kicked (can rejoin)"
        }
        
        await self._send_protection_response(
            update, context, "Flood Punishment Updated",
            f"🛡️ Flood punishment set to: {action.upper()}\n\n"
            f"• {action_descriptions[action]}\n"
            f"• Change anytime with /floodmode [mode]"
        )

    # ═══════════════════════════════════════════════════════════════════════════════════
    # 🔗 LINK PROTECTION COMMANDS
    # ═══════════════════════════════════════════════════════════════════════════════════

    @admin_only()
    @group_only()
    async def antilink_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Block all links: /antilink on/off"""
        if not context.args:
            chat_id = update.effective_chat.id
            current_state = await self.db.get_group_setting(chat_id, 'antilink_enabled', False)
            await self._send_protection_response(
                update, context, "AntiLink Status",
                f"Link protection: {'🟢 ENABLED' if current_state else '🔴 DISABLED'}\n\n"
                "Usage:\n"
                "• /antilink on - Enable link blocking\n"
                "• /antilink off - Disable link blocking"
            )
            return

        state = context.args[0].lower()
        if state not in ["on", "off"]:
            await self._send_protection_response(
                update, context, "Invalid State",
                "❌ Use 'on' or 'off'", False
            )
            return

        chat_id = update.effective_chat.id
        new_state = state == "on"
        await self.db.set_group_setting(chat_id, 'antilink_enabled', new_state)
        
        await self._send_protection_response(
            update, context, f"AntiLink {'Enabled' if new_state else 'Disabled'}",
            f"{'🟢' if new_state else '🔴'} Link protection has been {'enabled' if new_state else 'disabled'}!\n\n"
            f"{'• All links will be automatically deleted' if new_state else '• Users can now send links freely'}"
        )

    @admin_only()
    @group_only()
    async def antitelegram_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Block Telegram links: /antitelegram on/off"""
        if not context.args:
            chat_id = update.effective_chat.id
            current_state = await self.db.get_group_setting(chat_id, 'antitelegram_enabled', False)
            await self._send_protection_response(
                update, context, "AntiTelegram Status",
                f"Telegram link protection: {'🟢 ENABLED' if current_state else '🔴 DISABLED'}\n\n"
                "Usage:\n"
                "• /antitelegram on - Block Telegram links\n"
                "• /antitelegram off - Allow Telegram links"
            )
            return

        state = context.args[0].lower()
        if state not in ["on", "off"]:
            await self._send_protection_response(
                update, context, "Invalid State",
                "❌ Use 'on' or 'off'", False
            )
            return

        chat_id = update.effective_chat.id
        new_state = state == "on"
        await self.db.set_group_setting(chat_id, 'antitelegram_enabled', new_state)
        
        await self._send_protection_response(
            update, context, f"AntiTelegram {'Enabled' if new_state else 'Disabled'}",
            f"{'🟢' if new_state else '🔴'} Telegram link protection {'enabled' if new_state else 'disabled'}!\n\n"
            f"{'• t.me, telegram.me, telegram.dog links will be deleted' if new_state else '• Telegram links are now allowed'}"
        )

    @admin_only()
    @group_only()
    async def antiforward_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Block forwarded messages: /antiforward on/off"""
        if not context.args:
            chat_id = update.effective_chat.id
            current_state = await self.db.get_group_setting(chat_id, 'antiforward_enabled', False)
            await self._send_protection_response(
                update, context, "AntiForward Status",
                f"Forward protection: {'🟢 ENABLED' if current_state else '🔴 DISABLED'}\n\n"
                "Usage:\n"
                "• /antiforward on - Block forwarded messages\n"
                "• /antiforward off - Allow forwarded messages"
            )
            return

        state = context.args[0].lower()
        if state not in ["on", "off"]:
            await self._send_protection_response(
                update, context, "Invalid State",
                "❌ Use 'on' or 'off'", False
            )
            return

        chat_id = update.effective_chat.id
        new_state = state == "on"
        await self.db.set_group_setting(chat_id, 'antiforward_enabled', new_state)
        
        await self._send_protection_response(
            update, context, f"AntiForward {'Enabled' if new_state else 'Disabled'}",
            f"{'🟢' if new_state else '🔴'} Forward protection {'enabled' if new_state else 'disabled'}!\n\n"
            f"{'• All forwarded messages will be deleted' if new_state else '• Users can forward messages freely'}"
        )

    @admin_only()
    @group_only()
    async def linkwhitelist_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Allow specific domains: /linkwhitelist [domain]"""
        if not context.args:
            chat_id = update.effective_chat.id
            whitelist = await self.db.get_group_setting(chat_id, 'link_whitelist', [])
            if not whitelist:
                await self._send_protection_response(
                    update, context, "Link Whitelist Empty",
                    "No domains are whitelisted.\n\n"
                    "Usage: /linkwhitelist [domain]\n"
                    "Example: /linkwhitelist youtube.com"
                )
            else:
                domains_text = "\n".join([f"• {domain}" for domain in whitelist])
                await self._send_protection_response(
                    update, context, "Whitelisted Domains",
                    f"Allowed domains:\n\n{domains_text}\n\n"
                    "Add more: /linkwhitelist [domain]"
                )
            return

        domain = context.args[0].lower().strip()
        # Remove protocol if present
        domain = domain.replace('http://', '').replace('https://', '').replace('www.', '')
        
        chat_id = update.effective_chat.id
        whitelist = await self.db.get_group_setting(chat_id, 'link_whitelist', [])
        
        if domain in whitelist:
            await self._send_protection_response(
                update, context, "Domain Already Whitelisted",
                f"❌ {domain} is already in the whitelist!", False
            )
            return

        whitelist.append(domain)
        await self.db.set_group_setting(chat_id, 'link_whitelist', whitelist)
        
        await self._send_protection_response(
            update, context, "Domain Whitelisted",
            f"✅ {domain} has been added to the whitelist!\n\n"
            f"• Links from this domain will not be deleted\n"
            f"• Current whitelist has {len(whitelist)} domains"
        )

    # ═══════════════════════════════════════════════════════════════════════════════════
    # 🤖 ANTI-RAID PROTECTION COMMANDS
    # ═══════════════════════════════════════════════════════════════════════════════════

    @admin_only()
    @group_only()
    async def raidmode_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Toggle raid mode: /raidmode on/off"""
        if not context.args:
            chat_id = update.effective_chat.id
            current_state = await self.db.get_group_setting(chat_id, 'raidmode_enabled', False)
            await self._send_protection_response(
                update, context, "Raid Mode Status", 
                f"Raid protection: {'🟢 ENABLED' if current_state else '🔴 DISABLED'}\n\n"
                "Usage:\n"
                "• /raidmode on - Enable raid protection\n"
                "• /raidmode off - Disable raid protection"
            )
            return

        state = context.args[0].lower()
        if state not in ["on", "off"]:
            await self._send_protection_response(
                update, context, "Invalid State",
                "❌ Use 'on' or 'off'", False
            )
            return

        chat_id = update.effective_chat.id
        new_state = state == "on"
        await self.db.set_group_setting(chat_id, 'raidmode_enabled', new_state)
        
        await self._send_protection_response(
            update, context, f"Raid Mode {'Enabled' if new_state else 'Disabled'}",
            f"{'🟢' if new_state else '🔴'} Raid protection {'enabled' if new_state else 'disabled'}!\n\n"
            f"{'• Mass joins will trigger automatic protection' if new_state else '• Normal join monitoring resumed'}"
        )

    @admin_only()
    @group_only()
    async def raidtime_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Set raid detection time: /raidtime [minutes]"""
        if not context.args:
            chat_id = update.effective_chat.id
            current_time = await self.db.get_group_setting(chat_id, 'raid_time_window', 5)
            await self._send_protection_response(
                update, context, "Raid Time Window",
                f"Current detection window: {current_time} minutes\n\n"
                "Usage: /raidtime [minutes]\n"
                "Example: /raidtime 10"
            )
            return

        try:
            minutes = int(context.args[0])
            if minutes < 1 or minutes > 60:
                await self._send_protection_response(
                    update, context, "Invalid Time",
                    "❌ Time must be between 1 and 60 minutes.", False
                )
                return

            chat_id = update.effective_chat.id
            await self.db.set_group_setting(chat_id, 'raid_time_window', minutes)
            
            await self._send_protection_response(
                update, context, "Raid Time Updated",
                f"🛡️ Raid detection window set to {minutes} minutes!\n\n"
                f"• Raids will be detected if threshold is exceeded within {minutes} minutes"
            )
        except ValueError:
            await self._send_protection_response(
                update, context, "Invalid Number",
                "❌ Please provide a valid number of minutes.", False
            )

    @admin_only()
    @group_only()
    async def raidlimit_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Set user joining threshold: /raidlimit [users]"""
        if not context.args:
            chat_id = update.effective_chat.id
            current_limit = await self.db.get_group_setting(chat_id, 'raid_user_limit', 10)
            await self._send_protection_response(
                update, context, "Raid User Limit",
                f"Current user threshold: {current_limit} users\n\n"
                "Usage: /raidlimit [users]\n"
                "Example: /raidlimit 15"
            )
            return

        try:
            limit = int(context.args[0])
            if limit < 3 or limit > 100:
                await self._send_protection_response(
                    update, context, "Invalid Limit",
                    "❌ Limit must be between 3 and 100 users.", False
                )
                return

            chat_id = update.effective_chat.id
            await self.db.set_group_setting(chat_id, 'raid_user_limit', limit)
            
            await self._send_protection_response(
                update, context, "Raid Limit Updated",
                f"🛡️ Raid user threshold set to {limit} users!\n\n"
                f"• Raid detected when {limit}+ users join quickly"
            )
        except ValueError:
            await self._send_protection_response(
                update, context, "Invalid Number",
                "❌ Please provide a valid number of users.", False
            )

    @admin_only()
    @group_only()
    async def raidaction_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Set action on raid: /raidaction [ban/mute]"""
        if not context.args:
            chat_id = update.effective_chat.id
            current_action = await self.db.get_group_setting(chat_id, 'raid_action', 'mute')
            await self._send_protection_response(
                update, context, "Current Raid Action",
                f"Current raid action: {current_action.upper()}\n\n"
                "Available actions:\n"
                "• /raidaction ban - Ban suspected raiders\n"
                "• /raidaction mute - Mute suspected raiders"
            )
            return

        action = context.args[0].lower()
        if action not in ["ban", "mute"]:
            await self._send_protection_response(
                update, context, "Invalid Action",
                "❌ Valid actions: ban, mute", False
            )
            return

        chat_id = update.effective_chat.id
        await self.db.set_group_setting(chat_id, 'raid_action', action)
        
        action_descriptions = {
            "ban": "Suspected raiders will be banned",
            "mute": "Suspected raiders will be muted"
        }
        
        await self._send_protection_response(
            update, context, "Raid Action Updated",
            f"🛡️ Raid action set to: {action.upper()}\n\n"
            f"• {action_descriptions[action]}\n"
            f"• Change anytime with /raidaction [action]"
        )

    # ═══════════════════════════════════════════════════════════════════════════════════
    # 🔞 NSFW PROTECTION COMMANDS
    # ═══════════════════════════════════════════════════════════════════════════════════

    @admin_only()
    @group_only()
    async def nsfw_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Master protection control - handles all protection features"""
        if not update.effective_chat:
            return

        chat_id = update.effective_chat.id

        # Always ensure the main protection is enabled
        await self.db.set_group_setting(chat_id, 'protection_enabled', True)

        if not context.args:
            # Get current settings for all protection features
            nsfw_filter_enabled = await self.db.get_group_setting(chat_id, 'nsfw_filter_enabled', False)
            media_filter_enabled = await self.db.get_group_setting(chat_id, 'media_filter_enabled', False)
            link_filter_enabled = await self.db.get_group_setting(chat_id, 'link_filter_enabled', False)
            ai_moderation_enabled = await self.db.get_group_setting(chat_id, 'ai_moderation_enabled', False)
            check_admins = await self.db.get_group_setting(chat_id, 'check_admins', False)

            # Check comprehensive NSFW status
            comprehensive_nsfw = nsfw_filter_enabled and media_filter_enabled

            # Show current settings with savage protection status
            settings_msg = await update.message.reply_text(
                "<b>⚡ PROTECTION STATUS - NO MERCY MODE</b>\n\n"
                f"{'🔥 NSFW ANNIHILATION: ACTIVE' if comprehensive_nsfw else '💀 NSFW PROTECTION: DEAD'}\n"
                f"  ├─ Text Scanner: {'HUNTING' if nsfw_filter_enabled else 'SLEEPING'}\n"
                f"  └─ Media Hunter: {'LOCKED & LOADED' if media_filter_enabled else 'OFFLINE'}\n"
                f"{'🔥 Link Destroyer: OPERATIONAL' if link_filter_enabled else '💀 Link Filter: DISABLED'}\n"
                f"{'🔥 AI Executioner: WATCHING' if ai_moderation_enabled else '💀 AI Guardian: BLIND'}\n"
                f"{'🔥 Admin Immunity: GRANTED' if check_admins else '💀 Admins Get Wrecked Too'}\n\n"
                "<b>COMMAND YOUR ARMY:</b>\n"
                "• <code>/nsfw on/off</code> - TOTAL WARFARE MODE\n"
                "• <code>/nsfw nsfw on/off</code> - NSFW EXTERMINATION\n"
                "• <code>/nsfw text on/off</code> - TEXT ASSASSINATION\n"
                "• <code>/nsfw media on/off</code> - MEDIA OBLITERATION\n"
                "• <code>/nsfw links on/off</code> - LINK ANNIHILATION\n"
                "• <code>/nsfw ai on/off</code> - AI DOMINATION\n"
                "• <code>/nsfw admins on/off</code> - ADMIN PRIVILEGES\n\n"
                "<i>This intel self-destructs in 30 seconds</i>",
                parse_mode=ParseMode.HTML
            )
            
            # Auto-delete after 30 seconds
            asyncio.create_task(self._auto_delete_message(context, chat_id, settings_msg.message_id, 30))
            return

        setting = context.args[0].lower()
        
        # Check if there's a second argument for explicit on/off
        explicit_value = None
        if len(context.args) > 1:
            if context.args[1].lower() in ['on', 'true', 'yes', 'enable', 'enabled']:
                explicit_value = True
            elif context.args[1].lower() in ['off', 'false', 'no', 'disable', 'disabled']:
                explicit_value = False

        # Handle master control - "on" or "off" without specific setting enables/disables ALL protection
        if setting in ['on', 'off', 'enable', 'disable', 'enabled', 'disabled']:
            master_value = setting in ['on', 'enable', 'enabled']
            
            # Set all protection features to the master value
            await self.db.set_group_setting(chat_id, 'nsfw_filter_enabled', master_value)
            await self.db.set_group_setting(chat_id, 'media_filter_enabled', master_value)
            await self.db.set_group_setting(chat_id, 'link_filter_enabled', master_value)
            await self.db.set_group_setting(chat_id, 'ai_moderation_enabled', master_value)
            await self.db.set_group_setting(chat_id, 'check_admins', master_value)
            
            status = "ACTIVATED" if master_value else "DEACTIVATED"
            
            toggle_msg = await update.message.reply_text(
                f"<b>{'⚡ TOTAL DOMINATION ACTIVATED' if master_value else '💀 PROTECTION SYSTEMS OFFLINE'}</b>\n\n"
                f"{'🔥 ALL WEAPONS ARMED AND READY' if master_value else '💀 Defense grid completely destroyed'}\n"
                f"{'Every single threat will be eliminated without mercy' if master_value else 'Your group is now completely defenseless'}\n\n"
                f"<b>Commander:</b> {update.effective_user.mention_html()}\n"
                f"<i>This battlefield report self-destructs in 30 seconds</i>",
                parse_mode=ParseMode.HTML
            )
            
            asyncio.create_task(self._auto_delete_message(context, chat_id, toggle_msg.message_id, 30))
            logger.info(f"All protection features {status} in chat {chat_id} by admin {update.effective_user.full_name}")
            return

        # Individual setting controls
        settings_map = {
            'nsfw': 'nsfw_filter_enabled',
            'text': 'nsfw_filter_enabled',
            'media': 'media_filter_enabled',
            'links': 'link_filter_enabled',
            'ai': 'ai_moderation_enabled',
            'admins': 'check_admins'
        }

        if setting not in settings_map:
            error_msg = await update.message.reply_text(
                "<b>💀 COMMAND REJECTED - INVALID TARGET</b>\n\n"
                "<b>Available weapons systems:</b>\n"
                "• <code>on/off</code> - NUCLEAR OPTION (everything dies)\n"
                "• <code>nsfw</code> - NSFW EXTERMINATION PROTOCOL\n"
                "• <code>text</code> - TEXT ASSASSINATION MODE\n"
                "• <code>media</code> - MEDIA OBLITERATION SYSTEM\n"
                "• <code>links</code> - LINK DESTRUCTION UNIT\n"
                "• <code>ai</code> - AI OVERLORD ACTIVATION\n"
                "• <code>admins</code> - ADMIN PRIVILEGE CONTROL\n\n"
                "<i>Learn to aim properly or get out of my battlefield</i>",
                parse_mode=ParseMode.HTML
            )
            asyncio.create_task(self._auto_delete_message(context, chat_id, error_msg.message_id, 30))
            return

        try:
            db_setting = settings_map[setting]
            current_value = await self.db.get_group_setting(chat_id, db_setting, False)

            # Use explicit value if provided, otherwise toggle
            if explicit_value is not None:
                new_value = explicit_value
            else:
                new_value = not current_value

            # Special handling for comprehensive NSFW (both text and media)
            if setting == 'nsfw':
                logger.info(f"Setting comprehensive NSFW filter to {new_value} for chat {chat_id}")
                
                # Set both nsfw_filter_enabled and media_filter_enabled for comprehensive protection
                await self.db.set_group_setting(chat_id, 'nsfw_filter_enabled', new_value)
                await self.db.set_group_setting(chat_id, 'media_filter_enabled', new_value)
                
                logger.info(f"Set both text and media NSFW filters to {new_value} for chat {chat_id}")
                
                # Verify both settings were saved correctly
                text_value = await self.db.get_group_setting(chat_id, 'nsfw_filter_enabled', False)
                media_value = await self.db.get_group_setting(chat_id, 'media_filter_enabled', False)
                
                logger.info(f"Verification - Text NSFW: {text_value}, Media NSFW: {media_value}")
                
                # Update new_value to reflect the actual comprehensive state
                new_value = text_value and media_value
            else:
                # Standard handling for individual settings
                success = await self.db.set_group_setting(chat_id, db_setting, new_value)
                if success:
                    logger.info(f"Successfully updated {db_setting} for chat {chat_id} from {current_value} to {new_value}")
                    
                    # Double-check that the setting was saved correctly
                    updated_value = await self.db.get_group_setting(chat_id, db_setting, False)
                    if updated_value != new_value:
                        logger.error(f"Setting {db_setting} was not saved correctly! Expected {new_value}, got {updated_value}")
                else:
                    logger.error(f"Failed to update {db_setting} for chat {chat_id}")

            status = "ACTIVATED" if new_value else "DEACTIVATED"
            protection_type = setting.replace('_', ' ').upper()

            toggle_msg = await update.message.reply_text(
                f"<b>{'⚡ TARGET ACQUIRED' if new_value else '💀 WEAPON DISABLED'} - {protection_type.upper()}</b>\n\n"
                f"{'🔥 System armed and hunting for targets' if new_value else '💀 Defense system completely neutralized'}\n"
                f"{'No mercy will be shown to violators' if new_value else 'Your group is now vulnerable to attacks'}\n\n"
                f"<b>Operation executed by:</b> {update.effective_user.mention_html()}\n"
                f"<i>Mission report self-destructs in 30 seconds</i>",
                parse_mode=ParseMode.HTML
            )
            
            asyncio.create_task(self._auto_delete_message(context, chat_id, toggle_msg.message_id, 30))
            
            # Log the change with admin info
            admin_name = update.effective_user.full_name or update.effective_user.username or str(update.effective_user.id)
            logger.info(f"{protection_type} filter {status} in chat {chat_id} by admin {admin_name} (ID: {update.effective_user.id})")
            
        except Exception as e:
            logger.error(f"Error toggling protection setting: {str(e)}")
            error_msg = await update.message.reply_text(
                "💀 <b>SYSTEM MALFUNCTION!</b>\n\n"
                "<i>Weapon systems are experiencing technical difficulties</i>\n"
                "<i>Retry your command or face the consequences of a defenseless group</i>\n\n"
                "<i>Error report self-destructs in 30 seconds</i>",
                parse_mode=ParseMode.HTML
            )
            asyncio.create_task(self._auto_delete_message(context, chat_id, error_msg.message_id, 30))

    @admin_only()
    @group_only()
    async def nsfwmode_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Set NSFW action: /nsfwmode [delete/warn/ban]"""
        if not context.args:
            chat_id = update.effective_chat.id
            current_mode = await self.db.get_group_setting(chat_id, 'nsfw_action', 'delete')
            await self._send_protection_response(
                update, context, "Current NSFW Mode",
                f"Current NSFW action: {current_mode.upper()}\n\n"
                "Available modes:\n"
                "• /nsfwmode delete - Delete NSFW content\n"
                "• /nsfwmode warn - Warn users and delete\n"
                "• /nsfwmode ban - Ban users immediately"
            )
            return

        mode = context.args[0].lower()
        if mode not in ["delete", "warn", "ban"]:
            await self._send_protection_response(
                update, context, "Invalid Mode",
                "❌ Valid modes: delete, warn, ban", False
            )
            return

        chat_id = update.effective_chat.id
        await self.db.set_group_setting(chat_id, 'nsfw_action', mode)
        
        mode_descriptions = {
            "delete": "NSFW content will be silently deleted",
            "warn": "Users will be warned and content deleted",
            "ban": "Users will be immediately banned"
        }
        
        await self._send_protection_response(
            update, context, "NSFW Mode Updated",
            f"🛡️ NSFW action set to: {mode.upper()}\n\n"
            f"• {mode_descriptions[mode]}\n"
            f"• Change anytime with /nsfwmode [mode]"
        )

    @admin_only()
    @group_only()
    async def nsfwstrict_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Set detection sensitivity: /nsfwstrict [1-3]"""
        if not context.args:
            chat_id = update.effective_chat.id
            current_level = await self.db.get_group_setting(chat_id, 'nsfw_strictness', 2)
            levels = {1: "Low", 2: "Medium", 3: "High"}
            await self._send_protection_response(
                update, context, "NSFW Strictness Level",
                f"Current sensitivity: Level {current_level} ({levels[current_level]})\n\n"
                "Available levels:\n"
                "• /nsfwstrict 1 - Low sensitivity\n"
                "• /nsfwstrict 2 - Medium sensitivity (recommended)\n"
                "• /nsfwstrict 3 - High sensitivity"
            )
            return

        try:
            level = int(context.args[0])
            if level not in [1, 2, 3]:
                await self._send_protection_response(
                    update, context, "Invalid Level",
                    "❌ Valid levels: 1 (Low), 2 (Medium), 3 (High)", False
                )
                return

            chat_id = update.effective_chat.id
            await self.db.set_group_setting(chat_id, 'nsfw_strictness', level)
            
            levels = {1: "Low", 2: "Medium", 3: "High"}
            descriptions = {
                1: "Only obvious NSFW content detected",
                2: "Balanced detection (recommended)",
                3: "Aggressive detection, may have false positives"
            }
            
            await self._send_protection_response(
                update, context, "NSFW Strictness Updated",
                f"🛡️ NSFW sensitivity set to Level {level} ({levels[level]})\n\n"
                f"• {descriptions[level]}\n"
                f"• Adjust anytime with /nsfwstrict [1-3]"
            )
        except ValueError:
            await self._send_protection_response(
                update, context, "Invalid Level",
                "❌ Please provide a valid level (1, 2, or 3).", False
            )

    @admin_only()
    @group_only()
    async def nsfwwhitelist_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Exempt user from NSFW: /nsfwwhitelist [user]"""
        if not context.args:
            await self._send_protection_response(
                update, context, "NSFW Whitelist Help",
                "Usage: /nsfwwhitelist [user]\n\n"
                "Whitelist a user to exempt them from NSFW detection.\n"
                "Reply to a message or mention the user.\n\n"
                "Example: /nsfwwhitelist @username"
            )
            return

        chat_id = update.effective_chat.id
        target_user = None
        
        # Check if replying to a message
        if update.message.reply_to_message:
            target_user = update.message.reply_to_message.from_user
        else:
            # Try to parse username or user ID
            user_input = context.args[0]
            if user_input.startswith('@'):
                # Handle username (we can't resolve usernames directly, so we'll store the input)
                pass
            else:
                try:
                    user_id = int(user_input)
                    # We could try to get user info, but for now just store the ID
                    target_user = type('User', (), {'id': user_id, 'first_name': f'User {user_id}'})()
                except ValueError:
                    await self._send_protection_response(
                        update, context, "Invalid User",
                        "❌ Please reply to a message or provide a valid user ID.", False
                    )
                    return

        if target_user:
            whitelist = await self.db.get_group_setting(chat_id, 'nsfw_whitelist', [])
            if target_user.id not in whitelist:
                whitelist.append(target_user.id)
                await self.db.set_group_setting(chat_id, 'nsfw_whitelist', whitelist)
                
                user_name = target_user.first_name or f"User {target_user.id}"
                await self._send_protection_response(
                    update, context, "User Whitelisted",
                    f"✅ {user_name} has been exempted from NSFW detection!\n\n"
                    f"• This user can send any content without NSFW filtering\n"
                    f"• Current whitelist has {len(whitelist)} users"
                )
            else:
                await self._send_protection_response(
                    update, context, "Already Whitelisted",
                    f"❌ User is already exempted from NSFW detection!", False
                )

    # ═══════════════════════════════════════════════════════════════════════════════════
    # 👥 NEW USER PROTECTION COMMANDS
    # ═══════════════════════════════════════════════════════════════════════════════════

    @admin_only()
    @group_only()
    async def captcha_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Toggle new user verification: /captcha on/off"""
        if not context.args:
            chat_id = update.effective_chat.id
            current_state = await self.db.get_group_setting(chat_id, 'captcha_enabled', False)
            await self._send_protection_response(
                update, context, "Captcha Status",
                f"New user verification: {'🟢 ENABLED' if current_state else '🔴 DISABLED'}\n\n"
                "Usage:\n"
                "• /captcha on - Enable captcha for new users\n"
                "• /captcha off - Disable captcha verification"
            )
            return

        state = context.args[0].lower()
        if state not in ["on", "off"]:
            await self._send_protection_response(
                update, context, "Invalid State",
                "❌ Use 'on' or 'off'", False
            )
            return

        chat_id = update.effective_chat.id
        new_state = state == "on"
        await self.db.set_group_setting(chat_id, 'captcha_enabled', new_state)
        
        await self._send_protection_response(
            update, context, f"Captcha {'Enabled' if new_state else 'Disabled'}",
            f"{'🟢' if new_state else '🔴'} New user verification {'enabled' if new_state else 'disabled'}!\n\n"
            f"{'• New users must solve a captcha to speak' if new_state else '• New users can speak immediately'}"
        )

    @admin_only()
    @group_only()
    async def captchatime_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Set captcha time limit: /captchatime [minutes]"""
        if not context.args:
            chat_id = update.effective_chat.id
            current_time = await self.db.get_group_setting(chat_id, 'captcha_time_limit', 5)
            await self._send_protection_response(
                update, context, "Captcha Time Limit",
                f"Current time limit: {current_time} minutes\n\n"
                "Usage: /captchatime [minutes]\n"
                "Example: /captchatime 10"
            )
            return

        try:
            minutes = int(context.args[0])
            if minutes < 1 or minutes > 60:
                await self._send_protection_response(
                    update, context, "Invalid Time",
                    "❌ Time must be between 1 and 60 minutes.", False
                )
                return

            chat_id = update.effective_chat.id
            await self.db.set_group_setting(chat_id, 'captcha_time_limit', minutes)
            
            await self._send_protection_response(
                update, context, "Captcha Time Updated",
                f"🛡️ Captcha time limit set to {minutes} minutes!\n\n"
                f"• New users have {minutes} minutes to solve the captcha\n"
                f"• Users will be kicked if they don't solve it in time"
            )
        except ValueError:
            await self._send_protection_response(
                update, context, "Invalid Number",
                "❌ Please provide a valid number of minutes.", False
            )

    @admin_only()
    @group_only()
    async def welcomemute_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Mute new users: /welcomemute on/off"""
        if not context.args:
            chat_id = update.effective_chat.id
            current_state = await self.db.get_group_setting(chat_id, 'welcomemute_enabled', False)
            await self._send_protection_response(
                update, context, "Welcome Mute Status",
                f"New user muting: {'🟢 ENABLED' if current_state else '🔴 DISABLED'}\n\n"
                "Usage:\n"
                "• /welcomemute on - Mute new users temporarily\n"
                "• /welcomemute off - Let new users speak immediately"
            )
            return

        state = context.args[0].lower()
        if state not in ["on", "off"]:
            await self._send_protection_response(
                update, context, "Invalid State",
                "❌ Use 'on' or 'off'", False
            )
            return

        chat_id = update.effective_chat.id
        new_state = state == "on"
        await self.db.set_group_setting(chat_id, 'welcomemute_enabled', new_state)
        
        await self._send_protection_response(
            update, context, f"Welcome Mute {'Enabled' if new_state else 'Disabled'}",
            f"{'🟢' if new_state else '🔴'} New user muting {'enabled' if new_state else 'disabled'}!\n\n"
            f"{'• New users will be muted for a set duration' if new_state else '• New users can speak immediately upon joining'}"
        )

    @admin_only()
    @group_only()
    async def restricttime_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Set restriction duration: /restricttime [minutes]"""
        if not context.args:
            chat_id = update.effective_chat.id
            current_time = await self.db.get_group_setting(chat_id, 'restrict_duration', 60)
            await self._send_protection_response(
                update, context, "Restriction Duration",
                f"Current restriction time: {current_time} minutes\n\n"
                "Usage: /restricttime [minutes]\n"
                "Example: /restricttime 30"
            )
            return

        try:
            minutes = int(context.args[0])
            if minutes < 1 or minutes > 1440:  # Max 24 hours
                await self._send_protection_response(
                    update, context, "Invalid Time",
                    "❌ Time must be between 1 and 1440 minutes (24 hours).", False
                )
                return

            chat_id = update.effective_chat.id
            await self.db.set_group_setting(chat_id, 'restrict_duration', minutes)
            
            hours = minutes // 60
            remaining_minutes = minutes % 60
            time_str = f"{hours}h {remaining_minutes}m" if hours > 0 else f"{minutes}m"
            
            await self._send_protection_response(
                update, context, "Restriction Time Updated",
                f"🛡️ Restriction duration set to {time_str}!\n\n"
                f"• New users will be restricted for {time_str}\n"
                f"• They'll be automatically unrestricted after this time"
            )
        except ValueError:
            await self._send_protection_response(
                update, context, "Invalid Number",
                "❌ Please provide a valid number of minutes.", False
            )

    # ═══════════════════════════════════════════════════════════════════════════════════
    # 📱 MEDIA PROTECTION COMMANDS
    # ═══════════════════════════════════════════════════════════════════════════════════

    @admin_only()
    @group_only()
    async def antimedia_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Block all media: /antimedia on/off"""
        if not context.args:
            chat_id = update.effective_chat.id
            current_state = await self.db.get_group_setting(chat_id, 'antimedia_enabled', False)
            await self._send_protection_response(
                update, context, "AntiMedia Status",
                f"Media blocking: {'🟢 ENABLED' if current_state else '🔴 DISABLED'}\n\n"
                "Usage:\n"
                "• /antimedia on - Block all media files\n"
                "• /antimedia off - Allow media files"
            )
            return

        state = context.args[0].lower()
        if state not in ["on", "off"]:
            await self._send_protection_response(
                update, context, "Invalid State",
                "❌ Use 'on' or 'off'", False
            )
            return

        chat_id = update.effective_chat.id
        new_state = state == "on"
        await self.db.set_group_setting(chat_id, 'antimedia_enabled', new_state)
        
        await self._send_protection_response(
            update, context, f"AntiMedia {'Enabled' if new_state else 'Disabled'}",
            f"{'🟢' if new_state else '🔴'} Media blocking {'enabled' if new_state else 'disabled'}!\n\n"
            f"{'• All media files will be deleted' if new_state else '• Users can send media freely'}"
        )

    @admin_only()
    @group_only()
    async def antisticker_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Block stickers: /antisticker on/off"""
        if not context.args:
            chat_id = update.effective_chat.id
            current_state = await self.db.get_group_setting(chat_id, 'antisticker_enabled', False)
            await self._send_protection_response(
                update, context, "AntiSticker Status",
                f"Sticker blocking: {'🟢 ENABLED' if current_state else '🔴 DISABLED'}\n\n"
                "Usage:\n"
                "• /antisticker on - Block stickers\n"
                "• /antisticker off - Allow stickers"
            )
            return

        state = context.args[0].lower()
        if state not in ["on", "off"]:
            await self._send_protection_response(
                update, context, "Invalid State",
                "❌ Use 'on' or 'off'", False
            )
            return

        chat_id = update.effective_chat.id
        new_state = state == "on"
        await self.db.set_group_setting(chat_id, 'antisticker_enabled', new_state)
        
        await self._send_protection_response(
            update, context, f"AntiSticker {'Enabled' if new_state else 'Disabled'}",
            f"{'🟢' if new_state else '🔴'} Sticker blocking {'enabled' if new_state else 'disabled'}!\n\n"
            f"{'• All stickers will be deleted' if new_state else '• Users can send stickers freely'}"
        )

    @admin_only()
    @group_only()
    async def antivideo_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Block videos: /antivideo on/off"""
        if not context.args:
            chat_id = update.effective_chat.id
            current_state = await self.db.get_group_setting(chat_id, 'antivideo_enabled', False)
            await self._send_protection_response(
                update, context, "AntiVideo Status",
                f"Video blocking: {'🟢 ENABLED' if current_state else '🔴 DISABLED'}\n\n"
                "Usage:\n"
                "• /antivideo on - Block videos\n"
                "• /antivideo off - Allow videos"
            )
            return

        state = context.args[0].lower()
        if state not in ["on", "off"]:
            await self._send_protection_response(
                update, context, "Invalid State",
                "❌ Use 'on' or 'off'", False
            )
            return

        chat_id = update.effective_chat.id
        new_state = state == "on"
        await self.db.set_group_setting(chat_id, 'antivideo_enabled', new_state)
        
        await self._send_protection_response(
            update, context, f"AntiVideo {'Enabled' if new_state else 'Disabled'}",
            f"{'🟢' if new_state else '🔴'} Video blocking {'enabled' if new_state else 'disabled'}!\n\n"
            f"{'• All videos will be deleted' if new_state else '• Users can send videos freely'}"
        )

    @admin_only()
    @group_only()
    async def antiaudio_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Block audio files: /antiaudio on/off"""
        if not context.args:
            chat_id = update.effective_chat.id
            current_state = await self.db.get_group_setting(chat_id, 'antiaudio_enabled', False)
            await self._send_protection_response(
                update, context, "AntiAudio Status",
                f"Audio blocking: {'🟢 ENABLED' if current_state else '🔴 DISABLED'}\n\n"
                "Usage:\n"
                "• /antiaudio on - Block audio files\n"
                "• /antiaudio off - Allow audio files"
            )
            return

        state = context.args[0].lower()
        if state not in ["on", "off"]:
            await self._send_protection_response(
                update, context, "Invalid State",
                "❌ Use 'on' or 'off'", False
            )
            return

        chat_id = update.effective_chat.id
        new_state = state == "on"
        await self.db.set_group_setting(chat_id, 'antiaudio_enabled', new_state)
        
        await self._send_protection_response(
            update, context, f"AntiAudio {'Enabled' if new_state else 'Disabled'}",
            f"{'🟢' if new_state else '🔴'} Audio blocking {'enabled' if new_state else 'disabled'}!\n\n"
            f"{'• All audio files will be deleted' if new_state else '• Users can send audio freely'}"
        )

    @admin_only()
    @group_only()
    async def antidocument_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Block documents: /antidocument on/off"""
        if not context.args:
            chat_id = update.effective_chat.id
            current_state = await self.db.get_group_setting(chat_id, 'antidocument_enabled', False)
            await self._send_protection_response(
                update, context, "AntiDocument Status",
                f"Document blocking: {'🟢 ENABLED' if current_state else '🔴 DISABLED'}\n\n"
                "Usage:\n"
                "• /antidocument on - Block documents\n"
                "• /antidocument off - Allow documents"
            )
            return

        state = context.args[0].lower()
        if state not in ["on", "off"]:
            await self._send_protection_response(
                update, context, "Invalid State",
                "❌ Use 'on' or 'off'", False
            )
            return

        chat_id = update.effective_chat.id
        new_state = state == "on"
        await self.db.set_group_setting(chat_id, 'antidocument_enabled', new_state)
        
        await self._send_protection_response(
            update, context, f"AntiDocument {'Enabled' if new_state else 'Disabled'}",
            f"{'🟢' if new_state else '🔴'} Document blocking {'enabled' if new_state else 'disabled'}!\n\n"
            f"{'• All documents will be deleted' if new_state else '• Users can send documents freely'}"
        )

    # ═══════════════════════════════════════════════════════════════════════════════════
    # 🌐 LANGUAGE PROTECTION COMMANDS
    # ═══════════════════════════════════════════════════════════════════════════════════

    @admin_only()
    @group_only()
    async def antiarabic_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Block Arabic text: /antiarabic on/off"""
        if not context.args:
            chat_id = update.effective_chat.id
            current_state = await self.db.get_group_setting(chat_id, 'antiarabic_enabled', False)
            await self._send_protection_response(
                update, context, "AntiArabic Status",
                f"Arabic text blocking: {'🟢 ENABLED' if current_state else '🔴 DISABLED'}\n\n"
                "Usage:\n"
                "• /antiarabic on - Block Arabic text\n"
                "• /antiarabic off - Allow Arabic text"
            )
            return

        state = context.args[0].lower()
        if state not in ["on", "off"]:
            await self._send_protection_response(
                update, context, "Invalid State",
                "❌ Use 'on' or 'off'", False
            )
            return

        chat_id = update.effective_chat.id
        new_state = state == "on"
        await self.db.set_group_setting(chat_id, 'antiarabic_enabled', new_state)
        
        await self._send_protection_response(
            update, context, f"AntiArabic {'Enabled' if new_state else 'Disabled'}",
            f"{'🟢' if new_state else '🔴'} Arabic text blocking {'enabled' if new_state else 'disabled'}!\n\n"
            f"{'• Messages with Arabic text will be deleted' if new_state else '• Arabic text is now allowed'}"
        )

    @admin_only()
    @group_only()
    async def anticyrillic_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Block Cyrillic text: /anticyrillic on/off"""
        if not context.args:
            chat_id = update.effective_chat.id
            current_state = await self.db.get_group_setting(chat_id, 'anticyrillic_enabled', False)
            await self._send_protection_response(
                update, context, "AntiCyrillic Status",
                f"Cyrillic text blocking: {'🟢 ENABLED' if current_state else '🔴 DISABLED'}\n\n"
                "Usage:\n"
                "• /anticyrillic on - Block Cyrillic text\n"
                "• /anticyrillic off - Allow Cyrillic text"
            )
            return

        state = context.args[0].lower()
        if state not in ["on", "off"]:
            await self._send_protection_response(
                update, context, "Invalid State",
                "❌ Use 'on' or 'off'", False
            )
            return

        chat_id = update.effective_chat.id
        new_state = state == "on"
        await self.db.set_group_setting(chat_id, 'anticyrillic_enabled', new_state)
        
        await self._send_protection_response(
            update, context, f"AntiCyrillic {'Enabled' if new_state else 'Disabled'}",
            f"{'🟢' if new_state else '🔴'} Cyrillic text blocking {'enabled' if new_state else 'disabled'}!\n\n"
            f"{'• Messages with Cyrillic text will be deleted' if new_state else '• Cyrillic text is now allowed'}"
        )

    @admin_only()
    @group_only()
    async def antichinese_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Block Chinese text: /antichinese on/off"""
        if not context.args:
            chat_id = update.effective_chat.id
            current_state = await self.db.get_group_setting(chat_id, 'antichinese_enabled', False)
            await self._send_protection_response(
                update, context, "AntiChinese Status",
                f"Chinese text blocking: {'🟢 ENABLED' if current_state else '🔴 DISABLED'}\n\n"
                "Usage:\n"
                "• /antichinese on - Block Chinese text\n"
                "• /antichinese off - Allow Chinese text"
            )
            return

        state = context.args[0].lower()
        if state not in ["on", "off"]:
            await self._send_protection_response(
                update, context, "Invalid State",
                "❌ Use 'on' or 'off'", False
            )
            return

        chat_id = update.effective_chat.id
        new_state = state == "on"
        await self.db.set_group_setting(chat_id, 'antichinese_enabled', new_state)
        
        await self._send_protection_response(
            update, context, f"AntiChinese {'Enabled' if new_state else 'Disabled'}",
            f"{'🟢' if new_state else '🔴'} Chinese text blocking {'enabled' if new_state else 'disabled'}!\n\n"
            f"{'• Messages with Chinese text will be deleted' if new_state else '• Chinese text is now allowed'}"
        )

    @admin_only()
    @group_only()
    async def langwhitelist_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Exempt user from language filtering: /langwhitelist [user]"""
        if not context.args:
            await self._send_protection_response(
                update, context, "Language Whitelist Help",
                "Usage: /langwhitelist [user]\n\n"
                "Whitelist a user to exempt them from language filtering.\n"
                "Reply to a message or mention the user.\n\n"
                "Example: /langwhitelist @username"
            )
            return

        chat_id = update.effective_chat.id
        target_user = None
        
        # Check if replying to a message
        if update.message.reply_to_message:
            target_user = update.message.reply_to_message.from_user
        else:
            # Try to parse username or user ID
            user_input = context.args[0]
            if user_input.startswith('@'):
                # Handle username (we can't resolve usernames directly, so we'll store the input)
                pass
            else:
                try:
                    user_id = int(user_input)
                    # We could try to get user info, but for now just store the ID
                    target_user = type('User', (), {'id': user_id, 'first_name': f'User {user_id}'})()
                except ValueError:
                    await self._send_protection_response(
                        update, context, "Invalid User",
                        "❌ Please reply to a message or provide a valid user ID.", False
                    )
                    return

        if target_user:
            whitelist = await self.db.get_group_setting(chat_id, 'language_whitelist', [])
            if target_user.id not in whitelist:
                whitelist.append(target_user.id)
                await self.db.set_group_setting(chat_id, 'language_whitelist', whitelist)
                
                user_name = target_user.first_name or f"User {target_user.id}"
                await self._send_protection_response(
                    update, context, "User Whitelisted",
                    f"✅ {user_name} has been exempted from language filtering!\n\n"
                    f"• This user can send text in any language\n"
                    f"• Current whitelist has {len(whitelist)} users"
                )
            else:
                await self._send_protection_response(
                    update, context, "Already Whitelisted",
                    f"❌ User is already exempted from language filtering!", False
                )

    # ═══════════════════════════════════════════════════════════════════════════════════
    # 📋 PROTECTION HELP COMMAND
    # ═══════════════════════════════════════════════════════════════════════════════════

    @admin_only()
    @group_only()
    async def protection_help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show all protection commands in formatted style"""
        help_text = """╭─「 🛡️ Protection Commands 」
│
├─「 🚫 Anti-Spam Protection 」
│ • /antiflood [limit] [action] - Set flood protection
│ • /setflood [messages] [time] - Advanced flood setting
│ • /floodmode [ban/mute/kick] - Flood punishment
│ • /antiflood off - Disable flood protection
│
├─「 🔗 Link Protection 」
│ • /antilink on/off - Block all links
│ • /antitelegram on/off - Block Telegram links
│ • /antiforward on/off - Block forwarded messages
│ • /linkwhitelist [domain] - Allow specific domains
│
├─「 🤖 Anti-Raid Protection 」
│ • /raidmode on/off - Toggle raid mode
│ • /raidtime [minutes] - Set raid detection time
│ • /raidlimit [users] - Users joining threshold
│ • /raidaction [ban/mute] - Action on raid
│
├─「 🔞 NSFW Protection 」
│ • /nsfw on/off - Toggle NSFW detection
│ • /nsfwmode [delete/warn/ban] - NSFW action
│ • /nsfwstrict [1-3] - Detection sensitivity
│ • /nsfwwhitelist [user] - Exempt user from NSFW
│
├─「 👥 New User Protection 」
│ • /captcha on/off - New user verification
│ • /captchatime [minutes] - Time to solve
│ • /welcomemute on/off - Mute new users
│ • /restricttime [minutes] - Restriction duration
│
├─「 📱 Media Protection 」
│ • /antimedia on/off - Block all media
│ • /antisticker on/off - Block stickers
│ • /antivideo on/off - Block videos
│ • /antiaudio on/off - Block audio files
│ • /antidocument on/off - Block documents
│
├─「 🌐 Language Protection 」
│ • /antiarabic on/off - Block Arabic text
│ • /anticyrillic on/off - Block Cyrillic text
│ • /antichinese on/off - Block Chinese text
│ • /langwhitelist [user] - Exempt user
│
╰─「 Maximum security protection! 」"""

        await self._send_protection_response(
            update, context, "Protection Commands",
            f"<pre>{help_text}</pre>\n\n"
            f"<i>💡 All commands require admin privileges</i>"
        )

    async def cleanup_moderation_tasks(self) -> None:
        """Cancel any running moderation tasks and cleanup resources"""
        try:
            # Cancel all running tasks
            for task in self.moderation_tasks:
                if not task.done():
                    task.cancel()

            # Wait for all tasks to complete
            if self.moderation_tasks:
                await asyncio.gather(*self.moderation_tasks, return_exceptions=True)
                self.moderation_tasks.clear()

            # NSFW detector cleanup no longer needed (replaced with TypeGPT API)

            # Cleanup TypeGPT moderator
            if hasattr(self, 'typegpt_moderator') and self.typegpt_moderator:
                await self.typegpt_moderator.cleanup()

            # Cleanup any remaining temporary files
            for temp_file in self._temp_files:
                try:
                    if os.path.exists(temp_file):
                        os.unlink(temp_file)
                except Exception as e:
                    logger.error(f"Error cleaning up temporary file {temp_file}: {e}")
            self._temp_files.clear()

        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

    def __del__(self):
        """Cleanup on object destruction."""
        try:
            # Cleanup will be handled by the cleanup_moderation_tasks method
            pass
        except Exception as e:
            logger.error(f"Error during final cleanup: {e}")

    async def _thread_safe_moderate_text(self, moderation_data):
        """Thread-safe version of _moderate_text that doesn't rely on Update or Context objects"""
        chat_id = moderation_data['chat_id']
        user_id = moderation_data['user_id']
        text = moderation_data['text']

        try:
            # Create a bot instance for this thread
            from telegram import Bot
            import os

            # Get the bot token from config
            from src.config import config
            token = config.TELEGRAM_BOT_TOKEN
            if not token:
                logger.error("Bot token not found in configuration")
                return

            # Create a bot instance
            bot = Bot(token)

            # Configure the connection pool size if possible
            if hasattr(bot.request, "connection_pool_size"):
                bot.request.connection_pool_size = 10

            # Check for NSFW words
            found_nsfw_words = []

            # First, check for exact matches
            text_lower = text.lower()
            nsfw_words = load_nsfw_words()
            for word in nsfw_words:
                if word.lower() in text_lower:
                    found_nsfw_words.append(word)

            # If no exact matches, check for flexible patterns
            if not found_nsfw_words:
                # Check for common profanity with more flexible patterns
                logger.info("Checking for common profanity with flexible patterns")

                # Common profanity patterns (simplified for example)
                profanity_patterns = [
                    r'\bf+[\W_]*u+[\W_]*c+[\W_]*k+',  # Various forms of the f-word
                    r'\bs+[\W_]*h+[\W_]*i+[\W_]*t+',  # Various forms of s-word
                    r'\ba+[\W_]*s+[\W_]*s+',         # Various forms of a-word
                    r'\bp+[\W_]*u+[\W_]*s+[\W_]*s+[\W_]*y+', # Various forms of p-word
                    r'\bc+[\W_]*u+[\W_]*n+[\W_]*t+',    # Various forms of c-word
                    r'\bd+[\W_]*i+[\W_]*c+[\W_]*k+',    # Various forms of d-word
                    r'\bb+[\W_]*i+[\W_]*t+[\W_]*c+[\W_]*h+', # Various forms of b-word
                    r'\bp+[\W_]*o+[\W_]*r+[\W_]*n+',    # Various forms of porn
                    r'\bs+[\W_]*e+[\W_]*x+',          # Various forms of sex
                ]

                for pattern in profanity_patterns:
                    matches = re.finditer(pattern, text_lower, re.IGNORECASE)
                    for match in matches:
                        found_word = match.group(0)
                        if found_word and len(found_word) > 2:  # Avoid false positives with very short matches
                            found_nsfw_words.append(found_word)
                            logger.info(f"Found common profanity '{found_word}' in message: {text[:50]}...")
                            break

            # If NSFW words were found, send to AI moderator
            if found_nsfw_words:
                logger.info(f"Found {len(found_nsfw_words)} NSFW words in message by direct check, passing to AI moderator")
                logger.info(f"Found NSFW words by direct check: {found_nsfw_words}")

                # Create context for AI moderation
                nsfw_context = f"This message contains the following potentially NSFW words: {', '.join(found_nsfw_words)}. Please classify if this is actually NSFW content."
                logger.info(f"Using NSFW context: {nsfw_context}")

                # Send to AI moderator
                logger.info(f"Sending to AI moderator - Text: '{text[:50]}...', User: {user_id}, Context: ['{nsfw_context}']")

                # Initialize AI moderator
                from ...utils.ai_moderator import AIModerator
                ai_moderator = AIModerator()

                # Get AI moderation result
                is_nsfw, details = await ai_moderator.analyze_content(text, [nsfw_context])

                # Log the result
                logger.info(f"AI moderation result for message: NSFW = {is_nsfw}")
                logger.info(f"🤖 AI moderation result: NSFW = {is_nsfw}")

                if details:
                    category = details.get('category', 'UNKNOWN')
                    confidence = details.get('confidence', 0)
                    reason = details.get('reason', 'No reason provided')
                    logger.info(f"Moderation details: Category={category}, Confidence={confidence}%, Reason={reason}")
                    logger.info(f"Category: {category}\nConfidence: {confidence}%\nReason: {reason}")

                # If the content is NSFW, send moderation confirmation
                if is_nsfw:
                    logger.info(f"NSFW content detected in message, sending moderation confirmation")
                    logger.info(f"🚨 Sending moderation confirmation to admins")

                    # Log the detection
                    logger.info(f"DETECTED NSFW CONTENT in message from chat {chat_id}")
                    logger.info(f"Content type: {details.get('category', 'UNKNOWN')}")
                    logger.info(f"Confidence: {details.get('confidence', 0)}%")
                    logger.info(f"Found words: {found_nsfw_words}")
                    logger.info(f"User: {user_id}")

                    # Format found words as spoilers
                    spoiler_words = [f"||{word}||" for word in found_nsfw_words]

                    # Send moderation alert to admins
                    moderation_message = (
                        f"🚨 <b>NSFW CONTENT DETECTED</b> 🚨\n\n"
                        f"<b>User:</b> {user_id}\n"
                        f"<b>Chat:</b> {chat_id}\n"
                        f"<b>Content Type:</b> {details.get('category', 'UNKNOWN')}\n"
                        f"<b>Confidence:</b> {details.get('confidence', 0)}%\n"
                        f"<b>Reason:</b> {details.get('reason', 'No reason provided')}\n"
                        f"<b>Detected Words:</b> {', '.join(spoiler_words)}\n\n"
                        f"<b>Message:</b>\n<code>{text[:200]}{'...' if len(text) > 200 else ''}</code>"
                    )

                    try:
                        from telegram.constants import ParseMode
                        await bot.send_message(
                            chat_id=chat_id,
                            text=moderation_message,
                            parse_mode=ParseMode.HTML
                        )
                    except Exception as e:
                        logger.error(f"Failed to send moderation alert: {e}")

                    # Delete the message if it's NSFW
                    try:
                        await bot.delete_message(chat_id=chat_id, message_id=moderation_data['message_id'])
                        logger.info(f"Deleted NSFW message {moderation_data['message_id']} from chat {chat_id}")
                    except Exception as e:
                        logger.error(f"Failed to delete NSFW message: {e}")
                else:
                    logger.info(f"Message was classified as safe by AI moderator")
            else:
                logger.info(f"No NSFW words found in message, skipping AI moderation")
        except Exception as e:
            logger.error(f"Error in thread-safe moderation: {e}")
            logger.error(traceback.format_exc())

if __name__ == "__main__":
    # Module test routine can be added here if needed.
    print("ProtectionHandler module (without antiflood) loaded. Integrate with your bot to test functionality.")
