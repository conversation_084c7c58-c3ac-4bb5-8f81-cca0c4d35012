"""Captcha verification handler for new members 🔒"""

from telegram import ChatPermissions, Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes, ConversationHandler
from telegram.constants import ParseMode
from telegram.error import TelegramError
import random
import string
import logging
import traceback
from typing import Dict, Optional
import time
import html
from datetime import datetime, timedelta

from ..database.db import Database
from ..utils.decorators import admin_only, group_only
from .admin_tools import AdminTools

logger = logging.getLogger(__name__)

# Conversation states
CAPTCHA_VERIFICATION = 1

class CaptchaHandler:
    def __init__(self, db: Database):
        self.db = db
        self.pending_captchas: Dict[int, str] = {}  # user_id -> captcha_text
        self.captcha_timeouts: Dict[int, int] = {}  # user_id -> timeout_timestamp
        self.captcha_attempts: Dict[int, int] = {}  # user_id -> number of attempts
        self.admin_tools = AdminTools(db)

    def generate_captcha(self) -> str:
        """Generate a random captcha string"""
        # Generate 6 random characters (letters and numbers)
        characters = string.ascii_letters + string.digits
        return ''.join(random.choice(characters) for _ in range(6))

    async def handle_new_member(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle new member join with captcha verification"""
        if not update.message or not update.effective_chat:
            return

        chat_id = update.effective_chat.id
        captcha_enabled = await self.db.get_group_setting(chat_id, "captcha_enabled", False)
        
        if not captcha_enabled:
            # If captcha is disabled, directly trigger welcome message
            from .group_settings.welcome import WelcomeHandler
            welcome_handler = WelcomeHandler(self.db)
            await welcome_handler.handle_new_member(update, context)
            return

        for new_member in update.message.new_chat_members:
            if new_member.is_bot:
                continue

            try:
                # Generate captcha
                captcha_text = self.generate_captcha()
                self.pending_captchas[new_member.id] = captcha_text
                self.captcha_attempts[new_member.id] = 0
                
                # Create captcha message with buttons
                keyboard = []
                # Generate 3 random wrong answers
                wrong_answers = random.sample([self.generate_captcha() for _ in range(10)], 3)
                all_answers = [captcha_text] + wrong_answers
                random.shuffle(all_answers)
                
                # Create 2x2 grid of buttons
                for i in range(0, 4, 2):
                    row = [
                        InlineKeyboardButton(
                            text=all_answers[i],
                            callback_data=f"captcha_{new_member.id}_{all_answers[i]}"
                        ),
                        InlineKeyboardButton(
                            text=all_answers[i+1],
                            callback_data=f"captcha_{new_member.id}_{all_answers[i+1]}"
                        )
                    ]
                    keyboard.append(row)
                
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                # Send captcha message
                captcha_msg = await context.bot.send_message(
                    chat_id=chat_id,
                    text=(
                        f"🔒 <b>Welcome {new_member.mention_html()}!</b>\n\n"
                        "Please verify that you're human by selecting the correct captcha:\n\n"
                        f"<code>{captcha_text}</code>\n\n"
                        "⚠️ <b>WARNING:</b>\n"
                        "• You have 5 minutes to verify\n"
                        "• 3 wrong attempts will result in a 10m ban\n"
                        "• Timeout will result in a 10m ban\n"
                        "• Spamming will result in a permanent ban"
                    ),
                    parse_mode=ParseMode.HTML,
                    reply_markup=reply_markup
                )
                
                # Store timeout timestamp (5 minutes from now)
                self.captcha_timeouts[new_member.id] = int(time.time()) + 300
                
                # Schedule captcha timeout
                context.job_queue.run_once(
                    self.handle_captcha_timeout,
                    when=300,
                    data={"user_id": new_member.id, "chat_id": chat_id, "message_id": captcha_msg.message_id}
                )
                
                # Restrict user's permissions
                await context.bot.restrict_chat_member(
                    chat_id=chat_id,
                    user_id=new_member.id,
                    permissions=ChatPermissions(
                        can_send_messages=False,
                        can_send_polls=False,
                        can_send_other_messages=False,
                        can_add_web_page_previews=False,
                        can_change_info=False,
                        can_invite_users=False,
                        can_pin_messages=False,
                        can_manage_topics=False,
                        can_send_audios=False,
                        can_send_documents=False,
                        can_send_photos=False,
                        can_send_videos=False,
                        can_send_video_notes=False,
                        can_send_voice_notes=False
                    )
                )
                
            except Exception as e:
                logger.error(f"Error handling new member captcha: {str(e)}")
                logger.error(traceback.format_exc())

    async def handle_captcha_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle captcha verification callback"""
        query = update.callback_query
        if not query:
            return
            
        try:
            # Parse callback data
            _, user_id, answer = query.data.split("_")
            user_id = int(user_id)
            
            # Check if the user clicking is the same as the one who received the captcha
            if query.from_user.id != user_id:
                await query.answer("❌ This captcha is not for you!", show_alert=True)
                return
            
            # Check if captcha exists and hasn't timed out
            if user_id not in self.pending_captchas:
                await query.answer("Captcha has expired. Please contact an admin.", show_alert=True)
                return
                
            # Check timeout using current time
            current_time = int(time.time())
            if current_time > self.captcha_timeouts[user_id]:
                await query.answer("Captcha has expired. Please contact an admin.", show_alert=True)
                return
            
            # Increment attempts
            self.captcha_attempts[user_id] = self.captcha_attempts.get(user_id, 0) + 1
            
            # Verify answer
            if answer == self.pending_captchas[user_id]:
                # Correct answer
                await query.answer("✅ Captcha verified successfully!")
                
                # Remove captcha data
                del self.pending_captchas[user_id]
                del self.captcha_timeouts[user_id]
                del self.captcha_attempts[user_id]
                
                # Restore user permissions
                await context.bot.restrict_chat_member(
                    chat_id=update.effective_chat.id,
                    user_id=user_id,
                    permissions=ChatPermissions(
                        can_send_messages=True,
                        can_send_polls=True,
                        can_send_other_messages=True,
                        can_add_web_page_previews=True,
                        can_change_info=False,
                        can_invite_users=True,
                        can_pin_messages=False,
                        can_manage_topics=False,
                        can_send_audios=True,
                        can_send_documents=True,
                        can_send_photos=True,
                        can_send_videos=True,
                        can_send_video_notes=True,
                        can_send_voice_notes=True
                    )
                )
                
                # Delete captcha message
                await query.message.delete()
                
                # Get user information for welcome message
                chat_member = await context.bot.get_chat_member(update.effective_chat.id, user_id)
                if chat_member and chat_member.user:
                    # Import WelcomeHandler
                    from .group_settings.welcome import WelcomeHandler, WELCOME_GIFS
                    import random
                    import html
                    
                    # Get current chat ID
                    chat_id = update.effective_chat.id
                    logger.info(f"Sending welcome after captcha for chat_id: {chat_id}")
                    
                    # Check if this is the DevsDoCode server
                    is_ddc_chat = (
                        chat_id == -1002307106134 or  # Integer format
                        str(chat_id) == "-1002307106134" or  # String format
                        chat_id == int("-1002307106134")  # Parsed string format
                    )
                    
                    # Create a custom welcome for DevsDoCode
                    if is_ddc_chat:
                        logger.info(f"Using custom DDC welcome message for chat {chat_id} after captcha")
                        welcome_msg = """
<b>🌟 Welcome {mention} to DevsDoCode! 🚀</b>

<i>You're our {count}th member! Let's build something amazing together! 💫</i>

<b>👨‍💻 ABOUT US:</b>
• A community of passionate developers 💻
• Learn, share, and grow together 📚
• Daily coding discussions and tips 💡
• Supportive environment for everyone ✨
• Zero toxicity, all good vibes 🌟

<b>🎯 GET STARTED:</b>
• Say "Hi" or "GM" to break the ice 👋
• Check out /help for all bot features 🛠️
• Read /rules to understand our guidelines 📜
• Share your projects and ideas 💭

<b>💪 NEED HELP?</b>
• Use /report to contact our friendly admins
• Ask questions in the chat - we're here to help!
• Share your ideas - we love community input!


<i>Welcome to the DevsDoCode family! Let's code something amazing! 💻</i>
"""
                    else:
                        # Get welcome message from database
                        welcome_msg = await self.db.get_welcome_message(chat_id)
                        if not welcome_msg:
                            from .group_settings.welcome import DEFAULT_WELCOME
                            welcome_msg = DEFAULT_WELCOME
                    
                    # Get member count
                    chat = await context.bot.get_chat(chat_id)
                    member_count = await chat.get_member_count()
                    
                    # Format welcome message
                    formatted_msg = welcome_msg.format(
                        mention=chat_member.user.mention_html(),
                        chat_title=html.escape(update.effective_chat.title),
                        count=member_count,
                        name=html.escape(chat_member.user.first_name),
                        username=html.escape(chat_member.user.username or "No username"),
                        first_name=html.escape(chat_member.user.first_name),
                        last_name=html.escape(chat_member.user.last_name or ""),
                        id=chat_member.user.id
                    )
                    
                    # Get random welcome GIF
                    welcome_gif = random.choice(WELCOME_GIFS)
                    
                    # Get welcome buttons with proper chat ID
                    welcome_handler = WelcomeHandler(self.db)
                    reply_markup = welcome_handler.get_welcome_buttons(
                        update.effective_chat.title,
                        -1002307106134 if is_ddc_chat else chat_id
                    )
                    
                    # Send GIF with welcome message and buttons
                    await context.bot.send_animation(
                        chat_id=chat_id,
                        animation=welcome_gif,
                        caption=formatted_msg,
                        parse_mode=ParseMode.HTML,
                        reply_markup=reply_markup
                    )
                    
                    logger.info(f"Sent welcome message for user {chat_member.user.id} after captcha verification")
                
            else:
                # Wrong answer
                attempts = self.captcha_attempts[user_id]
                if attempts >= 3:
                    # Ban user for 10 minutes
                    await context.bot.ban_chat_member(
                        chat_id=update.effective_chat.id,
                        user_id=user_id,
                        until_date=int(time.time()) + 600  # 10 minutes
                    )
                    
                    # Send ban message
                    await context.bot.send_message(
                        chat_id=update.effective_chat.id,
                        text=(
                            f"🚫 {chat_member.user.mention_html()} has been banned for 10 minutes!\n\n"
                            "Reason: Failed captcha verification 3 times.\n"
                            "Please try again after the ban expires."
                        ),
                        parse_mode=ParseMode.HTML
                    )
                    
                    # Clean up
                    del self.pending_captchas[user_id]
                    del self.captcha_timeouts[user_id]
                    del self.captcha_attempts[user_id]
                    
                    await query.answer("❌ Too many wrong attempts! Banned for 10 minutes.", show_alert=True)
                else:
                    remaining = 3 - attempts
                    await query.answer(f"❌ Wrong answer! {remaining} attempts remaining.", show_alert=True)
                
        except Exception as e:
            logger.error(f"Error handling captcha callback: {str(e)}")
            logger.error(traceback.format_exc())
            await query.answer("An error occurred. Please try again.", show_alert=True)

    async def handle_captcha_timeout(self, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle captcha timeout"""
        job = context.job
        user_id = job.data["user_id"]
        chat_id = job.data["chat_id"]
        message_id = job.data["message_id"]
        
        try:
            # Check if captcha is still pending
            if user_id in self.pending_captchas:
                # Delete captcha message
                await context.bot.delete_message(chat_id, message_id)
                
                # Get user info for ban message
                chat_member = await context.bot.get_chat_member(chat_id, user_id)
                
                # Ban user for 10 minutes
                await context.bot.ban_chat_member(
                    chat_id=chat_id,
                    user_id=user_id,
                    until_date=int(time.time()) + 600  # Ban for 10 minutes
                )
                
                # Send timeout message
                await context.bot.send_message(
                    chat_id=chat_id,
                    text=(
                        f"⏰ <b>Captcha Verification Timeout</b>\n\n"
                        f"User: {chat_member.user.mention_html()}\n"
                        f"Status: Banned for 10 minutes\n"
                        f"Reason: Failed to verify within 5 minutes\n\n"
                        "Please try again after the ban expires."
                    ),
                    parse_mode=ParseMode.HTML
                )
                
                # Clean up captcha data
                del self.pending_captchas[user_id]
                del self.captcha_timeouts[user_id]
                del self.captcha_attempts[user_id]
                
        except Exception as e:
            logger.error(f"Error handling captcha timeout: {str(e)}")
            logger.error(traceback.format_exc())

    @admin_only()
    @group_only()
    async def toggle_captcha(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Toggle captcha verification for the group"""
        chat_id = update.effective_chat.id
        
        try:
            # Get current setting and convert to boolean type
            current = await self.db.get_group_setting(chat_id, "captcha_enabled", False)
            logger.info(f"Current captcha setting for chat {chat_id}: {current} (type: {type(current)})")
            
            # Toggle the setting
            new_value = not current
            logger.info(f"New captcha setting for chat {chat_id}: {new_value}")
            
            # Save the new setting
            await self.db.set_group_setting(chat_id, "captcha_enabled", new_value)

            # Verify by reading it back from the database
            actual_value = await self.db.get_group_setting(chat_id, "captcha_enabled", False)
            logger.info(f"Verified captcha setting for chat {chat_id}: {actual_value}")
            
            # Send status message based on actual current value
            status = "enabled" if actual_value else "disabled"
            emoji = "✅" if actual_value else "❌"
            
            await update.message.reply_text(
                f"{emoji} Captcha verification is now {status}!\n\n"
                f"New members will {'need to verify' if actual_value else 'join normally'}.",
                parse_mode=ParseMode.HTML
            )
            
            # Log the change
            logger.info(f"Captcha verification {status} in chat {chat_id}")
            
        except Exception as e:
            logger.error(f"Error toggling captcha: {str(e)}")
            logger.error(traceback.format_exc())
            await update.message.reply_text(
                "❌ Failed to toggle captcha verification. Please try again.",
                parse_mode=ParseMode.HTML
            )

    async def check_restricted_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Check if user is restricted and delete their messages"""
        if not update.message or not update.effective_chat:
            return

        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        
        # Check if captcha is enabled
        captcha_enabled = await self.db.get_group_setting(chat_id, "captcha_enabled", False)
        if not captcha_enabled:
            return
            
        # Check if user has pending captcha
        if user_id in self.pending_captchas:
            try:
                # Delete the message
                await update.message.delete()
                
                # Get remaining time
                current_time = int(time.time())
                remaining_time = max(0, self.captcha_timeouts[user_id] - current_time)
                minutes = remaining_time // 60
                seconds = remaining_time % 60
                
                # Send reminder message
                await context.bot.send_message(
                    chat_id=chat_id,
                    text=(
                        f"🔒 <b>Captcha Required!</b>\n\n"
                        f"User: {update.effective_user.mention_html()}\n"
                        f"Time Remaining: {minutes}m {seconds}s\n\n"
                        "⚠️ <b>WARNING:</b>\n"
                        "• Complete the captcha to unlock chat access\n"
                        "• Spamming will result in a permanent ban\n"
                        "• Timeout will result in a 10m ban"
                    ),
                    parse_mode=ParseMode.HTML
                )
                
                # Double-check and enforce restrictions
                try:
                    await context.bot.restrict_chat_member(
                        chat_id=chat_id,
                        user_id=user_id,
                        permissions=ChatPermissions(
                            can_send_messages=False,
                            can_send_polls=False,
                            can_send_other_messages=False,
                            can_add_web_page_previews=False,
                            can_change_info=False,
                            can_invite_users=False,
                            can_pin_messages=False,
                            can_manage_topics=False,
                            can_send_audios=False,
                            can_send_documents=False,
                            can_send_photos=False,
                            can_send_videos=False,
                            can_send_video_notes=False,
                            can_send_voice_notes=False
                        )
                    )
                except Exception as e:
                    logger.error(f"Error enforcing restrictions: {str(e)}")
                    
            except Exception as e:
                logger.error(f"Error handling restricted user message: {str(e)}")
                logger.error(traceback.format_exc()) 