import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Button,
  TextField,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Card,
  CardContent,
  CardActions,
  LinearProgress
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import VisibilityIcon from '@mui/icons-material/Visibility';
import BarChartIcon from '@mui/icons-material/BarChart';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { aiAPI } from '../services/api';

// Mock data for analytics
const usageData = [
  { time: '00:00', interactions: 40, tokens: 2400 },
  { time: '04:00', interactions: 30, tokens: 1398 },
  { time: '08:00', interactions: 200, tokens: 9800 },
  { time: '12:00', interactions: 278, tokens: 3908 },
  { time: '16:00', interactions: 189, tokens: 4800 },
  { time: '20:00', interactions: 239, tokens: 3800 },
  { time: '24:00', interactions: 349, tokens: 4300 },
];

const modelPerformance = [
  { name: 'Dhanishtha 2.0', value: 75 },
  { name: 'Dhanishtha 2.0 Mini', value: 25 },
];

const COLORS = ['#0088FE', '#00C49F'];

export default function AI() {
  const [interactions, setInteractions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [search, setSearch] = useState('');
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedInteraction, setSelectedInteraction] = useState(null);

  useEffect(() => {
    const fetchInteractions = async () => {
      try {
        const response = await aiAPI.getInteractions();
        setInteractions(response.data);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching AI interactions:', error);
        setLoading(false);
      }
    };

    fetchInteractions();
  }, []);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearchChange = (event) => {
    setSearch(event.target.value);
    setPage(0);
  };

  const handleViewDetails = (interaction) => {
    setSelectedInteraction(interaction);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedInteraction(null);
  };

  const filteredInteractions = interactions.filter(interaction =>
    interaction.user.toLowerCase().includes(search.toLowerCase()) ||
    interaction.query.toLowerCase().includes(search.toLowerCase())
  );

  const paginatedInteractions = filteredInteractions.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  // Calculate stats from real data
  const totalInteractions = interactions.length;
  const avgSatisfaction = interactions.length > 0 
    ? Math.round(interactions.reduce((sum, interaction) => sum + interaction.satisfaction, 0) / interactions.length)
    : 0;

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Typography variant="h4">Loading AI interactions...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Typography variant="h1" gutterBottom>
        AI Interaction Monitoring
      </Typography>
      
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {/* Stats Cards */}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Interactions
              </Typography>
              <Typography variant="h4">
                {totalInteractions.toLocaleString()}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Avg. Response Time
              </Typography>
              <Typography variant="h4">
                1.4s
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                User Satisfaction
              </Typography>
              <Typography variant="h4">
                {avgSatisfaction}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Active Models
              </Typography>
              <Typography variant="h4">
                2
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2, mb: 3 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <TextField
                variant="outlined"
                placeholder="Search AI interactions..."
                value={search}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: <SearchIcon />,
                }}
              />
            </Box>
            
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>User</TableCell>
                    <TableCell>Query</TableCell>
                    <TableCell>Response Time</TableCell>
                    <TableCell>Satisfaction</TableCell>
                    <TableCell>Time</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {paginatedInteractions.map((interaction) => (
                    <TableRow key={interaction.id}>
                      <TableCell>{interaction.user}</TableCell>
                      <TableCell>{interaction.query}</TableCell>
                      <TableCell>{interaction.responseTime}</TableCell>
                      <TableCell>
                        <Box display="flex" alignItems="center">
                          <Typography sx={{ mr: 1 }}>{interaction.satisfaction}%</Typography>
                          <LinearProgress 
                            variant="determinate" 
                            value={interaction.satisfaction} 
                            sx={{ width: 100 }} 
                          />
                        </Box>
                      </TableCell>
                      <TableCell>{interaction.timestamp}</TableCell>
                      <TableCell>
                        <IconButton onClick={() => handleViewDetails(interaction)}>
                          <VisibilityIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            
            <TablePagination
              rowsPerPageOptions={[5, 10, 25]}
              component="div"
              count={filteredInteractions.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, mb: 3 }}>
            <Typography variant="h2" gutterBottom>
              Model Usage
            </Typography>
            <ResponsiveContainer width="100%" height={200}>
              <PieChart>
                <Pie
                  data={modelPerformance}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                >
                  {modelPerformance.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
          
          <Paper sx={{ p: 2 }}>
            <Typography variant="h2" gutterBottom>
              Usage Trends
            </Typography>
            <ResponsiveContainer width="100%" height={250}>
              <LineChart
                data={usageData}
                margin={{
                  top: 5,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line type="monotone" dataKey="interactions" stroke="#8884d8" activeDot={{ r: 8 }} />
                <Line type="monotone" dataKey="tokens" stroke="#82ca9d" />
              </LineChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>
      
      {/* Interaction Detail Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          AI Interaction Details
        </DialogTitle>
        <DialogContent>
          {selectedInteraction && (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <Typography variant="h3">{selectedInteraction.user}</Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle1"><strong>Query:</strong></Typography>
                <Paper variant="outlined" sx={{ p: 2, mt: 1 }}>
                  <Typography>{selectedInteraction.query}</Typography>
                </Paper>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="subtitle1"><strong>Response Time:</strong> {selectedInteraction.responseTime}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="subtitle1"><strong>Satisfaction:</strong> {selectedInteraction.satisfaction}%</Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle1"><strong>Timestamp:</strong> {selectedInteraction.timestamp}</Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle1"><strong>AI Response:</strong></Typography>
                <Paper variant="outlined" sx={{ p: 2, mt: 1 }}>
                  <Typography>
                    Machine learning is a subset of artificial intelligence that enables computers to learn and make decisions from data...
                  </Typography>
                </Paper>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}