from typing import Dict, List, Optional, Generator, Union, Set, Any
import json
import requests
from pydantic import BaseModel
# from src.config.api_keys import HELPINGAI_API_KEY


DEFAULT_HEADERS = {
    'Accept': '*/*',
    'Accept-Language': 'en-US,en;q=0.9',
    'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
}


class Message(BaseModel):
    role: str
    content: str


class Usage(BaseModel):
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int


class Choice(BaseModel):
    index: int
    message: Message
    finish_reason: str


class ChatCompletionResponse(BaseModel):
    id: str
    object: str
    created: int
    model: str
    choices: List[Choice]
    usage: Usage


class HelpingAI:
    label: str = "Helping AI"
    url: str = "https://api.helpingai.co"
    working: bool = True
    supports_system_message: bool = True
    supports_message_history: bool = True

    # API endpoint for chat completions
    chat_api_endpoint: str = "https://api.helpingai.co/v1/chat/completions"

    @staticmethod
    def generate_text(
        model: str,
        messages: List[Dict[str, str]],
        temperature: Optional[float] = 0.7,
        max_tokens: Optional[int] = 150,
        top_p: Optional[float] = 1,
        frequency_penalty: Optional[float] = None,
        stop: Optional[List[str]] = None,
        stream: Optional[bool] = False,
        api_key: Optional[str] = "hl-328f259e-e3c7-4bbc-bc1e-4fbc625d5b23"
    ) -> str:
        """Generate text using the HelpingAI API."""
        # Log the model request

        # Merge DEFAULT_HEADERS with the authorization and content type headers
        headers = DEFAULT_HEADERS.copy()
        headers.update({
            'Authorization': f'Bearer {api_key}' if api_key else '',
            'Content-Type': 'application/json'
        })

        payload = {
            'model': model,
            'messages': messages,
            'temperature': temperature,
            'max_tokens': max_tokens,
            'top_p': top_p,
            'frequency_penalty': frequency_penalty,
            'stop': stop,
            'stream': stream
        }
        # Remove keys with None values
        payload = {k: v for k, v in payload.items() if v is not None}

        try:
            response = requests.post(HelpingAI.chat_api_endpoint, headers=headers, json=payload)
            response.raise_for_status()
            response_data = response.json()
            print(response.text)

            return response_data['choices'][0]['message']['content']
        except Exception as e:
            pass



if __name__ == "__main__":
    # Example usage
    api_key = "hl-7d62542a-a836-4e2d-930b-32a0a72232e4"
    model = "Dhanishtha-2.0-preview-mini"
    messages = [{'role': 'user', 'content': 'How many r in strawberry'}]
    try:
        text = HelpingAI.generate_text(model, messages, api_key=api_key)
        print(text)
    except Exception as e:
        print("An error occurred:", e)