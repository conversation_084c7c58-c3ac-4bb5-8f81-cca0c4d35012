{"name": "haibotv1", "version": "1.0.0", "description": "Telegram bot with dashboard", "scripts": {"start": "concurrently \"npm run start:bot\" \"npm run start:dashboard\" \"npm run start:backend\"", "start:bot": "python bot.py", "start:dashboard": "cd dashboard && npm run dev", "start:backend": "cd dashboard/backend && npm start", "install:all": "npm install && cd dashboard && npm install && cd backend && npm install"}, "devDependencies": {"concurrently": "^7.6.0"}}