"""
Rules Handler Module
------------------
<PERSON><PERSON> group rules setting and display.
"""

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.constants import ParseMode
import html
import logging
import asyncio
from datetime import datetime
from src.database.db import Database
from src.utils.decorators import admin_only, group_only

logger = logging.getLogger(__name__)

DEFAULT_RULES = """
╭─「 <b>📜 Group Rules & Guidelines</b> 」
│
├─「 <b>🤝 Respect & Community</b> 」
│ 🔹 Be respectful to all members
│ 🔹 No harassment, bullying, or attacks
│ 🔹 Hate speech will result in instant ban
│ 🔹 Maintain a welcoming environment
│
├─「 <b>💬 Content Standards</b> 」
│ 🔹 Keep discussions relevant and on-topic
│ 🔹 No NSFW or inappropriate content
│ 🔹 No spam, flooding, or repetitive posts
│ 🔹 Quality over quantity in messages
│
├─「 <b>🛡️ Privacy & Security</b> 」
│ 🔹 Never share API keys, tokens, or credentials
│ 🔹 No personal information sharing
│ 🔹 Doxing = immediate permanent ban
│ 🔹 Protect everyone's privacy
│
├─「 <b>📱 Media & Sharing</b> 」
│ 🔹 No unauthorized advertising
│ 🔹 No copyrighted material
│ 🔹 No malicious files or links
│ 🔹 Share responsibly
│
├─「 <b>⚖️ Enforcement</b> 」
│ 🔹 Follow admin instructions
│ 🔹 3 warnings = automatic action
│ 🔹 Severe violations = instant ban
│ 🔹 Appeals via /report command
│
╰─「 <i>Enjoy our community responsibly! 🌟</i> 」"""

class RulesHandler:
    def __init__(self, db: Database):
        self.db = db

    async def _delete_message_later(self, context: ContextTypes.DEFAULT_TYPE, chat_id: int, message_id: int, delay: int = 60):
        """Delete a message after a specified delay in seconds."""
        await asyncio.sleep(delay)
        try:
            await context.bot.delete_message(chat_id=chat_id, message_id=message_id)
        except Exception as e:
            logger.warning(f"Failed to delete message: {e}")

    @group_only()
    async def show_rules(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Display group rules."""
        if not update.effective_message or not update.effective_chat:
            return

        chat_id = update.effective_chat.id
        rules = await self.db.get_group_setting(chat_id, "rules") or DEFAULT_RULES

        # Create simplified buttons
        buttons = [
            [
                InlineKeyboardButton("Full Rules", callback_data="rules_full"),
                InlineKeyboardButton("FAQ", callback_data="rules_faq")
            ]
        ]

        # Add admin buttons if user is admin
        user_id = update.effective_user.id
        try:
            member = await context.bot.get_chat_member(chat_id, user_id)
            if member.status in ['creator', 'administrator']:
                buttons.append([
                    InlineKeyboardButton("Edit Rules", callback_data="rules_edit"),
                    InlineKeyboardButton("Reset Rules", callback_data="rules_reset")
                ])
        except Exception as e:
            logger.error(f"Error checking admin status: {e}")

        reply_markup = InlineKeyboardMarkup(buttons)

        # Send rules with auto-delete in 60 seconds
        message = await update.effective_message.reply_text(
            f"{rules}\n\n<i>This message will auto-delete in 60 seconds</i>",
            parse_mode=ParseMode.HTML,
            reply_markup=reply_markup
        )

        # Schedule deletion of both command and response
        asyncio.create_task(self._delete_message_later(context, chat_id, message.message_id))
        asyncio.create_task(self._delete_message_later(context, chat_id, update.effective_message.message_id))

    @group_only()
    async def show_rules_from_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Display group rules when called from a callback command.
        This method is specifically used by the welcome callback handler."""
        if not update.callback_query or not update.effective_chat:
            return

        chat_id = update.effective_chat.id
        rules = await self.db.get_group_setting(chat_id, "rules") or DEFAULT_RULES

        # Create simplified buttons
        buttons = [
            [
                InlineKeyboardButton("Full Rules", callback_data="rules_full"),
                InlineKeyboardButton("FAQ", callback_data="rules_faq")
            ],
            [
                InlineKeyboardButton("Back to Welcome", callback_data="back_to_welcome")
            ]
        ]

        # Add admin buttons if user is admin
        user_id = update.effective_user.id
        try:
            member = await context.bot.get_chat_member(chat_id, user_id)
            if member.status in ['creator', 'administrator']:
                buttons.insert(1, [
                    InlineKeyboardButton("Edit Rules", callback_data="rules_edit"),
                    InlineKeyboardButton("Reset Rules", callback_data="rules_reset")
                ])
        except Exception as e:
            logger.error(f"Error checking admin status: {e}")

        reply_markup = InlineKeyboardMarkup(buttons)

        try:
            # Try to edit the message text first
            await update.callback_query.message.edit_text(
                f"{rules}\n\n<i>Use buttons below to navigate</i>",
                parse_mode=ParseMode.HTML,
                reply_markup=reply_markup
            )
        except Exception as e:
            # If editing fails (e.g., message is an animation), send a new message
            logger.warning(f"Could not edit message, sending new one: {e}")
            await context.bot.send_message(
                chat_id=chat_id,
                text=f"{rules}\n\n<i>Use buttons below to navigate</i>",
                parse_mode=ParseMode.HTML,
                reply_markup=reply_markup
            )

    @admin_only()
    @group_only()
    async def set_rules(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Set new rules for the group."""
        if not update.effective_message or not context.args:
            await update.effective_message.reply_text(
                "Please provide the rules text after the command.\n"
                "Example: /setrules Rule 1: Be respectful\nRule 2: No spam"
            )
            return

        chat_id = update.effective_chat.id
        new_rules = " ".join(context.args)

        # Format rules with HTML and fixed ancient date
        formatted_rules = f"""
<b>Group Rules</b>

{html.escape(new_rules)}

<i>Last updated by: {update.effective_user.first_name}</i>
<i>Last updated: 5000 BCE</i>
<i>Use /rules to view these rules again</i>
"""

        try:
            await self.db.set_group_setting(chat_id, "rules", formatted_rules)
            
            success_msg = await update.effective_message.reply_text(
                "Rules have been updated successfully.\n\n"
                "<i>This message will auto-delete in 60 seconds</i>",
                parse_mode=ParseMode.HTML
            )
            
            # Auto-delete both command and response
            asyncio.create_task(self._delete_message_later(context, chat_id, success_msg.message_id))
            asyncio.create_task(self._delete_message_later(context, chat_id, update.effective_message.message_id))
            
        except Exception as e:
            logger.error(f"Error setting rules: {e}")
            await update.effective_message.reply_text(
                "Failed to update rules. Please try again later."
            )

    @admin_only()
    @group_only()
    async def reset_rules(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Reset group rules to default."""
        if not update.effective_message:
            return

        chat_id = update.effective_chat.id

        try:
            await self.db.set_group_setting(chat_id, "rules", DEFAULT_RULES)
            
            success_msg = await update.effective_message.reply_text(
                "Rules have been reset to default.\n\n"
                "<i>This message will auto-delete in 60 seconds</i>",
                parse_mode=ParseMode.HTML
            )
            
            # Auto-delete both command and response
            asyncio.create_task(self._delete_message_later(context, chat_id, success_msg.message_id))
            asyncio.create_task(self._delete_message_later(context, chat_id, update.effective_message.message_id))
            
        except Exception as e:
            logger.error(f"Error resetting rules: {e}")
            await update.effective_message.reply_text(
                "Failed to reset rules. Please try again later."
            )

    async def rules_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle rules-related button clicks."""
        query = update.callback_query
        if not query or not query.message:
            return

        await query.answer()
        data = query.data

        if data == "rules_full":
            # Show full rules in a new message
            chat_id = query.message.chat_id
            rules = await self.db.get_group_setting(chat_id, "rules") or DEFAULT_RULES
            
            full_rules_msg = await query.message.reply_text(
                f"{rules}\n\n<i>This message will auto-delete in 60 seconds</i>",
                parse_mode=ParseMode.HTML
            )
            
            # Auto-delete the full rules message
            asyncio.create_task(self._delete_message_later(
                context, 
                chat_id, 
                full_rules_msg.message_id
            ))

        elif data == "rules_faq":
            faq_text = """
<b>Frequently Asked Questions</b>

<b>Q: How do I report rule violations?</b>
A: Use /report command or contact administrators directly

<b>Q: What is the warning system?</b>
A: Multiple warnings escalate to temporary restrictions or permanent removal

<b>Q: Can rules be modified?</b>
A: Only administrators can update group rules

<b>Q: How to appeal moderation actions?</b>
A: Use /appeal command with relevant details

For additional inquiries, please contact group administrators.
"""
            await query.message.edit_text(
                faq_text + "\n\n<i>This message will auto-delete in 60 seconds</i>",
                parse_mode=ParseMode.HTML,
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("Back to Rules", callback_data="rules_back")
                ]])
            )

        elif data == "rules_back":
            # Show original rules
            chat_id = query.message.chat_id
            rules = await self.db.get_group_setting(chat_id, "rules") or DEFAULT_RULES
            buttons = [[
                InlineKeyboardButton("Full Rules", callback_data="rules_full"),
                InlineKeyboardButton("FAQ", callback_data="rules_faq")
            ]]
            
            # Add admin buttons if user is admin
            user_id = query.from_user.id
            try:
                member = await context.bot.get_chat_member(chat_id, user_id)
                if member.status in ['creator', 'administrator']:
                    buttons.append([
                        InlineKeyboardButton("Edit Rules", callback_data="rules_edit"),
                        InlineKeyboardButton("Reset Rules", callback_data="rules_reset")
                    ])
            except Exception as e:
                logger.error(f"Error checking admin status: {e}")
                
            await query.message.edit_text(
                f"{rules}\n\n<i>This message will auto-delete in 60 seconds</i>",
                parse_mode=ParseMode.HTML,
                reply_markup=InlineKeyboardMarkup(buttons)
            )
