import logging
import os
from datetime import datetime
from typing import Dict, Any

# Configure logging
log_dir = os.path.join(os.path.dirname(__file__), '..', 'logs')
os.makedirs(log_dir, exist_ok=True)

# Create formatters and handlers
FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
file_handler = logging.FileHandler(
    os.path.join(log_dir, f'ai_{datetime.now().strftime("%Y%m%d")}.log')
)
file_handler.setFormatter(logging.Formatter(FORMAT))

# Configure root logger
root_logger = logging.getLogger('AI')
root_logger.setLevel(logging.INFO)
root_logger.addHandler(file_handler)

def log_model_request(model: str, messages: list, **kwargs):
    """Log model request details"""
    logger = logging.getLogger('AI.model_request')
    logger.info(
        f"Model Request - Model: {model}, "
        f"Messages: {messages}, "
        f"Additional params: {kwargs}"
    )

def log_model_response(model: str, response: Any, error: bool = False):
    """Log model response or error"""
    logger = logging.getLogger('AI.model_response')
    if error:
        logger.error(
            f"Model Error - Model: {model}, "
            f"Error: {response}"
        )
    else:
        logger.info(
            f"Model Response - Model: {model}, "
            f"Response: {response}"
        )

def log_user_interaction(user_id: int, message: str, model: str):
    """Log user interaction details"""
    logger = logging.getLogger('AI.user_interaction')
    logger.info(
        f"User Interaction - User ID: {user_id}, "
        f"Message: {message}, "
        f"Model: {model}"
    )

def log_stream_chunk(model: str, chunk: str):
    """Log streaming response chunks"""
    logger = logging.getLogger('AI.stream')
    logger.debug(
        f"Stream Chunk - Model: {model}, "
        f"Chunk: {chunk}"
    )
