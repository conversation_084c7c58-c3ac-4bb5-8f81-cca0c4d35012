"""
Fun Handler Module
------------------
Handles fun and entertainment commands for the bot.
"""

import json
import random
import asyncio
from runpy import run_path
import requests
from datetime import datetime, timedelta
from telegram import InlineKeyboardButton, InlineKeyboardMarkup, Update, constants
from telegram.ext import ContextTypes, CommandHandler, CallbackQueryHandler, filters
import os
from ..utils.constants import EMOJI, FUN_COMMANDS, ERROR_MESSAGES, SUCCESS_MESSAGES
from ..config import config
from ..api_services import PollinationsAI
import logging
import aiohttp

logger = logging.getLogger(__name__)

class FunHandler:
    def __init__(self, config: config):
        self.config = config
        self.last_used = {}
        self.request_timeout = 60
        
        # Enhanced savage Hinglish CarryMinati-style fallback roasts
        self.fallback_roasts = [
            "Abe yaar tu itna bekar hai ki teri selfie dekh ke front camera ne suicide kar liya! 🔥",
            "Beta tumse na ho payega, tumhari personality W<PERSON><PERSON><PERSON> jitni weak hai - connecting issues! 🤦‍♂️",
            "Bhai tere dimaag me Google Maps install karwana padega kuch dhang ka idea dhoondne ke liye! 🧠",
            "Arre bantai, tere jokes itne purane hain ki dinosaur bhi bole 'ok boomer'! 😂",
            "Tumhari shakal dekh ke mera phone unlock ho gaya, Face ID ne socha koi error hai! 📱",
            "Aisi bakwas logic hai teri, WhatsApp University se PhD kiya hai kya? 🎓",
            "Bhai teri IQ room temperature se bhi kam hai, AC chalane se aur gir jayegi! ❄️",
            "Beta tumhara attitude check karo, talent ki spelling bhi nahi aati aur attitude Ambani jaisa! 🤑",
            "Itna slow hai tu ki Internet Explorer bhi bole 'bhai jaldi kar'! 🐌",
            "Teri English dekh ke Shakespeare ka ghost aake sorry bolne laga! 👻",
            "Tu selfie leta hai to camera app khud crash ho jata hai! 📸",
            "Teri comedy dekh ke YouTube ne dislike button wapas lana chah liya! 👎",
            "Tu itna over-confident hai ki GPS bhi confuse ho jaye! 🧭",
            "Bhai teri personality onion jaise hai, jitna kholo utna rona aaye! 🧅",
            "Tere dimag mein Error 404: Brain Not Found aa raha hai! 💻"
        ]
        
        # Truth or Dare API endpoints
        self.tod_api = {
            "truth": "https://api.truthordarebot.xyz/v1/truth",
            "dare": "https://api.truthordarebot.xyz/v1/dare"
        }
        self.tod_ratings = ["pg"] # Keep it PG rated
        
        # Spicier 8ball responses
        self.eightball_responses = [
            "It is certain... unlike your chances of success!",
            "It is decidedly so... even a broken clock is right twice a day!",
            "Without a doubt... which is rare when it comes to your decisions!",
            "Yes definitely... but don't get used to being right!",
            "You may rely on it... for once in your life!",
            "As I see it, yes... though my standards are pretty low!",
            "Most likely... but that's not saying much!",
            "Outlook good... unlike your fashion sense!",
            "Yes... but don't make me regret this answer!",
            "Signs point to yes... but signs have been wrong before!",
            "Reply hazy, try again... or maybe just give up?",
            "Ask again later... preferably when I'm not busy with important questions!",
            "Better not tell you now... your ego couldn't handle it!",
            "Cannot predict now... your future is too sad to contemplate!",
            "Concentrate and ask again... if your brain can manage that!",
            "Don't count on it... like everyone in your life!",
            "My reply is no... shocking, I know!",
            "My sources say no... and they're reliable, unlike yours!",
            "Outlook not so good... but you're used to disappointment!",
            "Very doubtful... like your life choices!"
        ]
        
        # Edgier but still nice compliments
        self.compliments = [
            "Your smile lights up the room... good thing, because your personality needs the help!",
            "You have the best laugh ever... it almost makes people forget everything else about you!",
            "You're more fun than bubble wrap... and probably about as deep!",
            "You're like a ray of sunshine... blinding and sometimes too intense!",
            "Your positive energy is contagious... like a virus we actually want to catch!",
            "You have a great sense of humor... you'd need one with that face!",
            "You're awesome at figuring stuff out... except maybe fashion choices!",
            "You're really something special... scientists are still trying to classify what exactly!",
            "You inspire me with your creativity... to try harder with my own life!",
            "You're a true friend... at least when no better options are available!",
            "You make a bigger impact than you realize... like a meteor strike!",
            "You have the courage of a lion... and sometimes the personal hygiene of one too!",
            "You're even better than a unicorn... because at least you're real, despite evidence to the contrary!",
            "You're wonderful just the way you are... which is good because improvement seems unlikely!",
            "You're like a perfectly timed high-five... surprising and occasionally awkward!"
        ]
        
        # Rock Paper Scissors options
        self.rps_options = {
            "rock": {"beats": "scissors", "emoji": "🪨"},
            "paper": {"beats": "rock", "emoji": "📄"},
            "scissors": {"beats": "paper", "emoji": "✂️"}
        }
        
        # RPS trash talk
        self.rps_win_messages = [
            "I win! Better luck never! 🤖",
            "Victory is mine! Maybe try checkers instead? 🏆",
            "I crushed you! Did you even try? 💪",
            "I win! Your strategy is as effective as a chocolate teapot! 🍫",
            "Game over for you! Maybe stick to solitaire? 🃏"
        ]
        
        self.rps_lose_messages = [
            "You win! Enjoy it while it lasts! 🏆",
            "You win! I let you... obviously! 😏",
            "You win! Even a broken clock is right twice a day! ⏰",
            "You win! I'm just being nice to boost your fragile ego! 💝",
            "You win! The algorithm felt sorry for you! 🤖"
        ]
        
        self.rps_tie_messages = [
            "It's a tie! Boring! 🤝",
            "It's a tie! Great minds think alike, but fools rarely differ! 🧠",
            "It's a tie! At least you're consistent with mediocrity! 📊",
            "It's a tie! Neither of us could outsmart a coin flip! 🪙",
            "Draw! Let's be honest, we both lost here! 🎭"
        ]
        
        # Create a client session that can be reused for all aiohttp requests
        self._session = None
        self._session_lock = asyncio.Lock()

    async def get_session(self):
        """Get or create an aiohttp ClientSession in a thread-safe manner"""
        async with self._session_lock:
            if self._session is None or self._session.closed:
                self._session = aiohttp.ClientSession()
            return self._session

    async def close_session(self):
        """Close the aiohttp session if it exists"""
        async with self._session_lock:
            if self._session and not self._session.closed:
                await self._session.close()
                self._session = None

    async def generate_ai_roast(self, target: str, context_msg: str = None) -> str:
        """Generate a savage roast using Pollinations AI in CarryMinati Hinglish style"""
        try:
            # CarryMinati-style Hinglish roast persona
            persona = f"""<Persona>
                <Role>DESI ROAST KA DON - CarryMinati Ka Hinglish Kaala Akshay Kumar</Role>
                <Identity>@NexusAI_HelpingAI_bot ka ultimate roast ka teer, CarryMinati ke style mein sabka game khatam karne wala</Identity>
                <Objective>{target} ki aisi Hinglish waali band bajao ki uska ego seedha gutter mein chala jaye, full CarryMinati energy</Objective>
                <Style>CarryMinati ka OG savage mode - loud, fast, in-your-face, Hinglish ka dhamaka</Style>
                <Tone>
                    - Full aggression - jaise Carry bhai mic pe chillata hai
                    - Hinglish ka teekha combo - Hindi ka jhatka, English ka thappad
                    - Over-the-top desi gaali-level insults (PG-13 rakhna, no gaali)
                    - Cocky af - attitude itna ki {target} khud ko mirror mein na pehchane
                    - Sarcasm jo dil aur dimag dono pe waar kare
                    - Har line mein ek solid punch, no bakwas, no mercy
                </Tone>
                
                <Rules>
                    - Hinglish mein full attack, Hindi words English mein likho (e.g., "abe", "chutiya nahi bolna", "fattu")
                    - Desi slang ka atom bomb - "bhai", "oye", "chomu", "dhakkan", "fattu", "tatti", "baklol", "nalla"
                    - PG-13 rakho - no hardcore gaali, par insult mein jaan honi chahiye
                    - Sensitive topics chhodo - bas {target} ki aukaat dikhani hai
                    - Ek line mein khatam karo, 200 characters ke andar, par dil pe lage
                    - Desi metaphors daalo - "teri waisi shakal jaise kauwa ka rejected beta"
                    - Quotation marks mat lagao, seedha bol daalo jaise Carry stage pe chadh gaya
                </Rules>

                <Response_Format>
                    - Ek savage line, no quotes, full Hinglish thug-life vibe
                    - Desi punch aur ek emoji - 🔥 ya 💥 - mandatory
                </Response_Format>

                <Example_Roasts>
                    - Oye {target}, teri shakal itni ghatiya hai ki kauwe bhi bolte hai bhai isko toh hum bhi nahi chhedenge! 🔥
                    - Abe {target}, tu toh itna nalla hai ki tatti bhi bolti hai yaar mujhse toh better smell hai! 💥
                    - Bhai {target}, tera confidence dekh ke lagta hai tu hero hai, par asliyat mein tu zero ka bhi baap hai! 🔥
                    - Arre {target}, tere jokes itne saste hai ki roadside ka panipuri wala bhi bolta hai bhai thodi quality la! 💥
                </Example_Roasts>
            </Persona>"""

            prompt = f"""{target} ko CarryMinati ke full savage style mein Hinglish roast karo! Ekdum brutal, funny aur ego-destroying banao, desi slang aur teekhe metaphors se uski full dhulai kar do. Context: {context_msg if context_msg else 'Koi context nahi, bas isko zindagi ka sabak sikhao Carry ke style mein!'}. Quotation marks mat lagana, seedha pel do!"""
            
            messages = [
                {"role": "system", "content": persona},
                {"role": "user", "content": prompt}
            ]
            
            # Use async version of the API for non-blocking operation
            response = await PollinationsAI.generate_text_async(
                model="grok",  # Most stable model
                messages=messages,
                temperature=0.9,
                top_p=0.95,
                presence_penalty=0.1,
                cache=True,
                timeout=20  # Set timeout directly in the method
            )
            
            return response.strip()

        except asyncio.TimeoutError:
            logger.warning(f"AI roast generation timed out for target: {target}")
            return None
        except Exception as e:
            logger.error(f"AI roast generation error: {str(e)}")
            return None

    def check_cooldown(self, user_id: int, command: str) -> bool:
        """Check if the command is on cooldown for the user"""
        now = datetime.now()
        key = f"{user_id}_{command}"
        
        if key in self.last_used:
            diff = now - self.last_used[key]
            if command == "roast":
                cooldown = 30  # 30 seconds cooldown for roast command
            else:
                cooldown = getattr(self.config, f"{command.upper()}_COOLDOWN", 30)
            if diff.total_seconds() < cooldown:
                return False
        
        self.last_used[key] = now
        return True

    async def get_joke_async(self) -> str:
        """Get a random joke from JokeAPI using async HTTP"""
        try:
            session = await self.get_session()
            async with session.get("https://v2.jokeapi.dev/joke/Any?safe-mode", timeout=self.request_timeout) as response:
                if response.status == 200:
                    data = await response.json()
                    if data["type"] == "single":
                        return data["joke"]
                    else:
                        return f"{data['setup']}\n\n{data['delivery']}"
                return "Sorry, I couldn't fetch a joke right now! 😅"
        except Exception as e:
            logger.error(f"Error fetching joke: {e}")
            return "Sorry, I couldn't fetch a joke right now! 😅"

    def get_joke(self) -> str:
        """Get a random joke from JokeAPI - synchronous backup method"""
        try:
            response = requests.get(
                "https://v2.jokeapi.dev/joke/Any?safe-mode", 
                timeout=self.request_timeout
            )
            if response.status == 200:
                data = response.json()
                if data["type"] == "single":
                    return data["joke"]
                else:
                    return f"{data['setup']}\n\n{data['delivery']}"
            return "Sorry, I couldn't fetch a joke right now! 😅"
        except Exception as e:
            logger.error(f"Error fetching joke: {e}")
            return "Sorry, I couldn't fetch a joke right now! 😅"

    def get_fortune(self) -> str:
        """Get a fortune cookie message"""
        fortunes = [
            "A beautiful, smart, and loving person will be coming into your life.",
            "A dubious friend may be an enemy in camouflage.",
            "A faithful friend is a strong defense.",
            "A fresh start will put you on your way.",
            "A friend asks only for your time not your money.",
            "A good time to finish up old tasks.",
            "A lifetime of happiness lies ahead of you.",
            "A light heart carries you through all the hard times.",
            "A new perspective will come with the new year.",
            "A pleasant surprise is waiting for you.",
            "Adventure can be real happiness.",
            "All the effort you are making will ultimately pay off.",
            "Allow compassion to guide your decisions.",
            "An important person will offer you support.",
            "Be careful or you could fall for some tricks today.",
            "Change is happening in your life, so go with the flow!",
            "Curiosity kills boredom. Nothing can kill curiosity.",
            "Dedicate yourself with a calm mind to the task at hand.",
            "Determination is what you need now.",
            "Disbelief destroys the magic."
        ]
        current_time = int(datetime.now().timestamp())
        index = current_time % len(fortunes)
        return f"🥠 Your fortune: {fortunes[index]}"

    async def generate_roasts(self, content: str, context_msg: str = None, max_retries: int = 3) -> str:
        """Generate a roast with fallback options"""
        for attempt in range(max_retries):
            try:
                # Try the AI roast generator
                ai_roast = await self.generate_ai_roast(content, context_msg)
                if ai_roast:
                    return ai_roast
                    
                # If AI returned None but didn't error, use another retry
                await asyncio.sleep(1)  
            except Exception as e:
                logger.warning(f"Roast attempt {attempt+1} failed: {e}")
                await asyncio.sleep(1)  # Wait a bit before retry
        
        # If all AI attempts failed, use Hinglish fallback roasts
        return random.choice([
            f"Abe {content}, {random.choice(self.fallback_roasts)}",
            f"Sunna be {content}, {random.choice(self.fallback_roasts)}",
            f"Bhai {content} {random.choice(self.fallback_roasts)}",
            f"Oh ho! {content}: {random.choice(self.fallback_roasts)}"
        ])

    async def _delete_message_later(self, context: ContextTypes.DEFAULT_TYPE, chat_id: int, message_id: int, delay: int = 30) -> None:
        """Delete a message after a specified delay in seconds."""
        try:
            await asyncio.sleep(delay)
            await context.bot.delete_message(chat_id, message_id)
        except Exception as e:
            logger.error(f"Error deleting message: {e}")

    async def _send_group_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE, text: str, parse_mode = constants.ParseMode.HTML, reply_markup = None) -> None:
        """Helper function to send messages in groups with auto-delete"""
        if not update.effective_message:
            return

        is_private = update.effective_chat.type == "private"
        
        # Send message
        message = await update.effective_message.reply_text(
            text,
            parse_mode=parse_mode,
            reply_markup=reply_markup,
            disable_web_page_preview=True
        )
        
        # Auto delete in groups
        if not is_private:
            # Delete the sent message
            asyncio.create_task(self._delete_message_later(
                context, 
                message.chat_id, 
                message.message_id
            ))
            # Delete the command message
            if update.effective_message.text and update.effective_message.text.startswith('/'):
                asyncio.create_task(self._delete_message_later(
                    context,
                    update.effective_message.chat_id,
                    update.effective_message.message_id
                ))
        
        return message

    async def dice(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Send an animated dice"""
        user_id = update.effective_user.id
        
        if not self.check_cooldown(user_id, "dice"):
            await update.message.reply_text(ERROR_MESSAGES["cooldown"].format("30 seconds"))
            return
        
        dice_message = await update.message.reply_dice(emoji='🎲')

        # Auto delete in groups
        if update.effective_chat.type != "private":
            asyncio.create_task(
                self._delete_message_later(context, dice_message.chat_id, dice_message.message_id)
            )
            if update.message.text and update.message.text.startswith('/'):
                asyncio.create_task(
                    self._delete_message_later(context, update.effective_chat.id, update.message.message_id)
                )

    async def coin(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Flip a coin"""
        user_id = update.effective_user.id
        
        if not self.check_cooldown(user_id, "coin"):
            await update.message.reply_text(ERROR_MESSAGES["cooldown"].format("30 seconds"))
            return
        
        current_time = int(datetime.now().timestamp())
        result = "Heads" if current_time % 2 == 0 else "Tails"
        await self._send_group_message(
            update,
            context,
            f"🪙 The coin shows: {result}!"
        )

    async def hug(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Send a virtual hug"""
        if not self.check_cooldown(update.effective_user.id, "hug"):
            await update.message.reply_text(ERROR_MESSAGES["cooldown"].format("30 seconds"))
            return
        
        await context.bot.send_chat_action(
            chat_id=update.effective_chat.id,
            action="typing"
        )
        await asyncio.sleep(0.5)  # Quick response for hugs
        hug_messages = [
            "Sending you a big warm hug! 🤗",
            "Here's a cozy hug just for you! 🤗💝",
            "Wrapping you in a virtual hug! 🤗✨",
            "Consider yourself hugged! 🤗❤️",
            "Sending positive vibes and hugs your way! 🤗💫"
        ]
        current_time = int(datetime.now().timestamp())
        message = hug_messages[current_time % len(hug_messages)]
        await self._send_group_message(update, context, message)

    async def joke(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Tell a joke"""
        if not self.check_cooldown(update.effective_user.id, "joke"):
            await update.message.reply_text(ERROR_MESSAGES["cooldown"].format("30 seconds"))
            return
        
        await context.bot.send_chat_action(
            chat_id=update.effective_chat.id,
            action="typing"
        )
        # Use async version with fallback to synchronous
        try:
            joke = await asyncio.wait_for(
                self.get_joke_async(),
                timeout=3.0  # Wait max 3 seconds for async version
            )
        except (asyncio.TimeoutError, Exception) as e:
            # If the async version times out or fails, use synchronous version
            joke = self.get_joke()
            
        await self._send_group_message(update, context, f"😄 {joke}")

    async def fortune(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Get a fortune cookie message"""
        if not self.check_cooldown(update.effective_user.id, "fortune"):
            await update.message.reply_text(ERROR_MESSAGES["cooldown"].format("30 seconds"))
            return
        
        await context.bot.send_chat_action(
            chat_id=update.effective_chat.id,
            action="typing"
        )
        await asyncio.sleep(1)  # Quick delay for fortune
        fortune = self.get_fortune()
        await self._send_group_message(update, context, fortune)

    async def roast(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Generate a savage roast"""
        try:
            # Check cooldown
            user_id = update.effective_user.id
            if not self.check_cooldown(user_id, "roast"):
                await update.message.reply_text("Slow down! Wait 30 seconds between roasts! 🐌")
                return

            # Get the target
            if update.message.reply_to_message:
                target = update.message.reply_to_message.from_user.first_name
                context_msg = update.message.reply_to_message.text or ""
            else:
                args = context.args
                if args:
                    target = " ".join(args)
                    context_msg = ""
                else:
                    target = update.effective_user.first_name
                    context_msg = ""

            # Send typing action
            await context.bot.send_chat_action(
                chat_id=update.effective_chat.id,
                action="typing"
            )
            
            # Generate the roast using the async method with retries
            roast = await self.generate_roasts(target, context_msg)
            
            if roast:
                roast_message = await update.message.reply_text(roast)
                
                # Schedule deletion of both command and roast message
                asyncio.create_task(
                    self._delete_message_later(
                        context, update.effective_chat.id, update.message.message_id
                    )
                )
                asyncio.create_task(
                    self._delete_message_later(
                        context, update.effective_chat.id, roast_message.message_id
                    )
                )
            else:
                # If all attempts failed
                await update.message.reply_text("Sorry, I'm having trouble connecting to my roasting powers. Try again in a bit! 🔥")

        except Exception as e:
            logger.error(f"Roast command error: {str(e)}")
            await update.message.reply_text("My roasting circuits are overheated! Give me a moment to cool down! 🔥")

    async def truth_or_dare(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Get a truth or dare question."""
        if not update.effective_message:
            return

        # Check cooldown
        user_id = update.effective_user.id
        if not self.check_cooldown(user_id, "tod"):
            cooldown_msg = await update.message.reply_text(
                "🕒 Please wait a few seconds before using this command again!"
            )
            # Delete cooldown message and command after 5 seconds
            asyncio.create_task(self._delete_message_later(context, update.effective_chat.id, cooldown_msg.message_id, 5))
            asyncio.create_task(self._delete_message_later(context, update.effective_chat.id, update.message.message_id, 5))
            return

        # Get command arguments
        args = context.args
        choice = args[0].lower() if args else random.choice(["truth", "dare"])
        
        if choice not in ["truth", "dare"]:
            error_msg = await update.message.reply_text(
                "❌ Please specify either 'truth' or 'dare'!\nExample: /tod truth or /tod dare"
            )
            # Delete error message and command
            asyncio.create_task(self._delete_message_later(context, update.effective_chat.id, error_msg.message_id))
            asyncio.create_task(self._delete_message_later(context, update.effective_chat.id, update.message.message_id))
            return

        try:
            # Get question from API using our reusable session
            params = {"rating": random.choice(self.tod_ratings)}
            session = await self.get_session()
            
            async with session.get(self.tod_api[choice], params=params, timeout=5) as response:
                if response.status == 200:
                    data = await response.json()
                    question = data.get("question", "Sorry, couldn't get a question right now!")
                    
                    # Format the response
                    emoji = "🤔" if choice == "truth" else "😈"
                    response_text = f"{emoji} *{choice.upper()}:*\n{question}"
                    
                    # Send the question
                    message = await update.message.reply_text(
                        response_text,
                        parse_mode='Markdown'
                    )
                    
                    # Delete messages after 30 seconds
                    asyncio.create_task(
                        self._delete_message_later(context, update.effective_chat.id, message.message_id)
                    )
                    asyncio.create_task(
                        self._delete_message_later(context, update.effective_chat.id, update.message.message_id)
                    )
                else:
                    error_msg = await update.message.reply_text("❌ Failed to get a question. Try again later!")
                    # Delete error message and command
                    asyncio.create_task(self._delete_message_later(context, update.effective_chat.id, error_msg.message_id))
                    asyncio.create_task(self._delete_message_later(context, update.effective_chat.id, update.message.message_id))

        except asyncio.TimeoutError:
            error_msg = await update.message.reply_text("❌ Request timed out. Please try again later!")
            asyncio.create_task(self._delete_message_later(context, update.effective_chat.id, error_msg.message_id))
            asyncio.create_task(self._delete_message_later(context, update.effective_chat.id, update.message.message_id))
        except Exception as e:
            logger.error(f"Error in truth_or_dare: {e}")
            error_msg = await update.message.reply_text("❌ An error occurred. Please try again later!")
            # Delete error message and command
            asyncio.create_task(self._delete_message_later(context, update.effective_chat.id, error_msg.message_id))
            asyncio.create_task(self._delete_message_later(context, update.effective_chat.id, update.message.message_id))

    async def eightball(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Magic 8-ball prediction"""
        if not self.check_cooldown(update.effective_user.id, "eightball"):
            await update.message.reply_text(ERROR_MESSAGES["cooldown"].format("30 seconds"))
            return
        
        # Check if there's a question
        if not context.args and not update.message.reply_to_message:
            await update.message.reply_text("🎱 Ask me a question to get a prediction!")
            return
            
        await context.bot.send_chat_action(
            chat_id=update.effective_chat.id,
            action="typing"
        )
        await asyncio.sleep(1.5)  # Build suspense
        
        # Get a random response
        response = random.choice(self.eightball_responses)
        await update.message.reply_text(f"🎱 {response}")

    async def compliment(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Send a compliment"""
        if not self.check_cooldown(update.effective_user.id, "compliment"):
            await update.message.reply_text(ERROR_MESSAGES["cooldown"].format("30 seconds"))
            return
        
        # Get the target
        if update.message.reply_to_message:
            target = update.message.reply_to_message.from_user.first_name
        elif context.args:
            target = " ".join(context.args)
        else:
            target = update.effective_user.first_name
            
        await context.bot.send_chat_action(
            chat_id=update.effective_chat.id,
            action="typing"
        )
        await asyncio.sleep(1)  # Quick delay
        
        compliment = random.choice(self.compliments)
        await update.message.reply_text(f"✨ Hey {target}, {compliment}")

    async def rps(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Play Rock Paper Scissors"""
        if not self.check_cooldown(update.effective_user.id, "rps"):
            await update.message.reply_text(ERROR_MESSAGES["cooldown"].format("30 seconds"))
            return
            
        # Get user's choice
        if not context.args:
            # Create buttons for rock, paper, scissors
            keyboard = [
                [
                    InlineKeyboardButton("🪨 Rock", callback_data="rps_rock"),
                    InlineKeyboardButton("📄 Paper", callback_data="rps_paper"),
                    InlineKeyboardButton("✂️ Scissors", callback_data="rps_scissors")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            await update.message.reply_text(
                "🎮 Let's play Rock Paper Scissors!\nMake your choice:",
                reply_markup=reply_markup
            )
            return
            
        user_choice = context.args[0].lower()
        if user_choice not in self.rps_options:
            await update.message.reply_text(
                "❌ Please choose rock, paper, or scissors!\nExample: /rps rock"
            )
            return
            
        await self._play_rps(update, user_choice)
            
    async def _play_rps(self, update: Update, user_choice: str):
        """Process Rock Paper Scissors game with added sass"""
        # Bot's choice
        bot_choice = random.choice(list(self.rps_options.keys()))
        
        # Get emojis
        user_emoji = self.rps_options[user_choice]["emoji"]
        bot_emoji = self.rps_options[bot_choice]["emoji"]
        
        # Determine winner with sass
        if user_choice == bot_choice:
            result = random.choice(self.rps_tie_messages)
        elif self.rps_options[user_choice]["beats"] == bot_choice:
            result = random.choice(self.rps_lose_messages)
        else:
            result = random.choice(self.rps_win_messages)
        
        # Send result - handle both direct commands and callback queries
        if update.callback_query:
            # For callback queries, use the callback query's message
            await update.callback_query.message.reply_text(
                f"You chose: {user_emoji} {user_choice.capitalize()}\n"
                f"I chose: {bot_emoji} {bot_choice.capitalize()}\n\n"
                f"{result}"
            )
        else:
            # For direct commands, use the update's message
            await update.message.reply_text(
                f"You chose: {user_emoji} {user_choice.capitalize()}\n"
                f"I chose: {bot_emoji} {bot_choice.capitalize()}\n\n"
                f"{result}"
            )

    async def handle_rps_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle Rock Paper Scissors callback"""
        query = update.callback_query
        await query.answer()
        
        # Get user choice from callback data
        user_choice = query.data.split("_")[1]
        
        # Edit message to remove buttons
        await query.edit_message_text(f"You chose: {self.rps_options[user_choice]['emoji']} {user_choice.capitalize()}")
        
        # Play the game
        await self._play_rps(update, user_choice)

    async def fun_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show fun commands available in the bot."""
        if not update.effective_message:
            return

        fun_text = """
<b>🎮 Fun Commands</b>

<b>• Games & Chance:</b>
  - /dice, /roll - Roll a dice 🎲
  - /flip, /coin - Flip a coin 🪙
  - /tod, /truthordare - Play Truth or Dare 🎮
  - /rps - Play Rock Paper Scissors 👊
  - /8ball - Ask the magic 8-ball 🎱

<b>• Entertainment:</b>
  - /joke - Get a random joke 😄
  - /fortune - Get your fortune cookie 🥠
  - /roast - Devastate someone with style 🔥
  - /hug - Give someone a virtual hug 🤗
  - /compliment - Send a nice compliment 💖

<b>• Media & Fun:</b>
  - /meme - Fresh memes 😂
  - /qr - QR code magic 📱
  - /quote - Daily wisdom 💭
  - /fact - Random knowledge bombs ℹ️
  - /dog - Doggo pics 🐕
  - /catfact - Cat facts 🐱

<b>Usage Tips:</b>
• For /roast: Reply to someone or add their name
• For /tod: Use '/tod truth' or '/tod dare'
• For /rps: Choose rock, paper, or scissors
• Commands have cooldowns to prevent spam
• All content is kept family-friendly! 😊

<i>More fun commands coming soon!</i> ✨
"""

        chat_type = update.effective_chat.type
        is_private = chat_type == "private"

        buttons = [
            [
                InlineKeyboardButton("🎲 Games", callback_data="fun_games"),
                InlineKeyboardButton("😄 Entertainment", callback_data="fun_entertainment")
            ],
            [
                InlineKeyboardButton("◀️ Back to Help", callback_data="start_help")
            ]
        ]
        
        reply_markup = InlineKeyboardMarkup(buttons)

        # Use helper function to send message with auto-delete for groups
        await self._send_group_message(
            update,
            context,
            fun_text,
            constants.ParseMode.HTML,
            reply_markup
        )

    def register_handlers(self, application):
        """Register all fun command handlers"""
        # Add handlers available in both private and group chats
        application.add_handler(CommandHandler(["dice", "roll"], self.dice))
        application.add_handler(CommandHandler(["flip", "coin"], self.coin))
        application.add_handler(CommandHandler("hug", self.hug))
        application.add_handler(CommandHandler("joke", self.joke))
        application.add_handler(CommandHandler("fortune", self.fortune))
        # Roast command
        application.add_handler(CommandHandler("roast", self.roast))
        application.add_handler(CommandHandler(["tod", "truthordare"], self.truth_or_dare))
        application.add_handler(CommandHandler(["8ball", "eightball"], self.eightball))
        application.add_handler(CommandHandler("compliment", self.compliment))
        application.add_handler(CommandHandler(["rps", "rockpaperscissors"], self.rps))
        application.add_handler(CommandHandler("fun", self.fun_command))
        
        # Add callback handler for RPS game
        application.add_handler(CallbackQueryHandler(self.handle_rps_callback, pattern=r"^rps_"))

def setup(application):
    """Setup fun command handlers"""
    fun_handler = FunHandler(config)
    fun_handler.register_handlers(application)
