from typing import Optional, Dict
from datetime import datetime
from telegram import Update, Message, ChatMember, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.constants import ParseMode
from ..database.db import Database
from ..utils.decorators import admin_only, group_only
import logging
import html
from collections import deque

logger = logging.getLogger(__name__)

class LogHandler:
    def __init__(self, db: Database):
        self.db = db
        self.log_queue = deque(maxlen=100)
        self.default_log_channel = -1002345906730
        self.default_events = {
            # Admin Actions
            "ban": True,       # Ban commands
            "unban": True,     # Unban commands
            "mute": True,      # Mute commands
            "unmute": True,    # Unmute commands
            "warn": True,      # Warning commands
            "unwarn": True,    # Remove warnings
            "voteban": True,   # Vote ban system
            "promote": True,   # Promote to admin
            "demote": True,    # Demote from admin
            "zombies": True,   # Zombie removal
            
            # Message Management
            "edit": True,      # Message edits
            "delete": True,    # Message deletions
            "pin": True,       # Pin messages
            "unpin": True,     # Unpin messages
            "purge": True,     # Message purge
            
            # Settings & Config
            "settings": True,  # Settings changes
            "setwelcome": True,  # Welcome message
            "setrules": True,    # Rules setup
            "filter": True,      # Filter management
            
            # Reports & Appeals
            "report": True,      # User reports
            "appeal": True,      # Ban appeals
            "reportabuse": True  # Abuse reports
        }
        
        # Command categories for better organization
        self.command_categories = {
            "moderation": ["ban", "unban", "mute", "unmute", "warn", "unwarn", "voteban", "zombies"],
            "management": ["promote", "demote", "pin", "unpin", "purge"],
            "settings": ["setwelcome", "setrules", "filter", "settings"],
            "reports": ["report", "appeal", "reportabuse"]
        }
        
        self.severity_colors = {
            "low": "",      # Regular commands
            "medium": "",    # Warnings, mutes
            "high": "",      # Bans, abuse reports
            "critical": ""   # Settings changes
        }

    def get_severity(self, event_type: str) -> str:
        """Get severity level for event type"""
        high_severity = ["ban", "unban", "voteban", "reportabuse", "zombies"]
        medium_severity = ["mute", "unmute", "warn", "unwarn", "report", "appeal"]
        critical_severity = ["settings", "setwelcome", "setrules", "filter", "promote", "demote"]
        
        if event_type in high_severity:
            return "high"
        elif event_type in medium_severity:
            return "medium"
        elif event_type in critical_severity:
            return "critical"
        else:
            return "low"

    def get_category_icon(self, event_type: str) -> str:
        """Get category-specific icon for event type"""
        icons = {
            "moderation": "",
            "management": "",
            "settings": "",
            "reports": ""
        }
        
        for category, commands in self.command_categories.items():
            if event_type in commands:
                return icons.get(category, "")
        return ""

    async def log_event(self, chat_id: int, event_type: str, details: Dict, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Log an event to the designated log channel"""
        log_channel = await self.db.get_group_setting(chat_id, "log_channel") or self.default_log_channel
        if not log_channel:
            return
            
        # Get disabled events
        disabled_events = await self.db.get_group_setting(chat_id, "disabled_log_events", [])
        if event_type in disabled_events:
            return

        # Get group info
        try:
            chat = await context.bot.get_chat(chat_id)
            group_name = chat.title
        except:
            group_name = f"Chat ID: {chat_id}"

        severity = self.get_severity(event_type)
        category_icon = self.get_category_icon(event_type)
        severity_icon = self.severity_colors[severity]
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        log_text = (
            f"{severity_icon} <b>{event_type.upper()}</b> {category_icon}\n"
            f"<b>Group:</b> {html.escape(group_name)}\n"
            f"<b>Time:</b> {timestamp}\n"
            f"<b>Severity:</b> {severity.upper()}\n\n"
        )
        
        # Format details without @ mentions
        for key, value in details.items():
            if key == "user":
                user_id = f"<code>{value.id}</code>"
                name = html.escape(value.full_name)
                log_text += f"<b>User:</b> {name} • ID: {user_id}\n"
            elif key == "admin":
                admin_id = f"<code>{value.id}</code>"
                name = html.escape(value.full_name)
                log_text += f"<b>Admin:</b> {name} • ID: {admin_id}\n"
            elif key == "duration":
                log_text += f"<b>Duration:</b> {value}\n"
            elif key == "reason":
                log_text += f"<b>Reason:</b> {html.escape(str(value))}\n"
            elif key == "command":
                log_text += f"<b>Command:</b> <code>{html.escape(str(value))}</code>\n"
            elif key == "old_message":
                log_text += f"<b>Old Message:</b>\n<code>{html.escape(str(value))}</code>\n"
            elif key == "new_message":
                log_text += f"<b>New Message:</b>\n<code>{html.escape(str(value))}</code>\n"
            elif key == "message":
                log_text += f"<b>Message:</b>\n<code>{html.escape(str(value))}</code>\n"
            elif key == "message_link":
                log_text += f"<b>Message Link:</b> {value}\n"
            elif key == "message_id":
                log_text += f"<b>Message ID:</b> {value}\n"
            elif key not in ["poll_message", "poll_link"]:
                log_text += f"<b>{key}:</b> {html.escape(str(value))}\n"

        # Add action buttons for moderation events
        keyboard = None
        if event_type in self.command_categories["moderation"]:
            keyboard = [
                [
                    InlineKeyboardButton("", callback_data=f"log_approve_{chat_id}_{details.get('user', {}).get('id', 0)}"),
                    InlineKeyboardButton("", callback_data=f"log_reject_{chat_id}_{details.get('user', {}).get('id', 0)}")
                ],
                [
                    InlineKeyboardButton("", callback_data=f"log_info_{chat_id}_{details.get('user', {}).get('id', 0)}")
                ]
            ]

        # Store in queue for history
        self.log_queue.append({
            "text": log_text,
            "timestamp": datetime.now(),
            "severity": severity,
            "event_type": event_type,
            "chat_id": chat_id
        })

        try:
            await context.bot.send_message(
                chat_id=log_channel,
                text=log_text,
                parse_mode=ParseMode.HTML,
                disable_web_page_preview=True,
                reply_markup=InlineKeyboardMarkup(keyboard) if keyboard else None
            )
        except Exception as e:
            logger.error(f"Failed to send log message: {e}")

    @admin_only()
    @group_only()
    async def set_log_channel(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Set the logging channel for the group"""
        if not context.args:
            await update.message.reply_text(
                " Set Log Channel\n\n"
                "1. Add the bot to your logging channel\n"
                "2. Make the bot an admin in the channel\n"
                "3. Use the command with channel username/ID:\n\n"
                "<code>/setlogchannel @channel</code> or\n"
                "<code>/setlogchannel -100xxx</code>",
                parse_mode=ParseMode.HTML
            )
            return

        channel = context.args[0]
        try:
            if channel.startswith("@"):
                channel_chat = await context.bot.get_chat(channel)
                channel_id = channel_chat.id
            else:
                channel_id = int(channel)
                channel_chat = await context.bot.get_chat(channel_id)

            # Test bot's permissions in the channel
            bot_member = await channel_chat.get_member(context.bot.id)
            if not (bot_member.can_post_messages or bot_member.status in ['administrator', 'creator']):
                await update.message.reply_text(
                    " Bot needs to be an admin in the channel with posting rights!"
                )
                return

            await self.db.set_group_setting(update.effective_chat.id, "log_channel", channel_id)
            
            success_message = (
                " Log Channel Setup Complete!\n\n"
                f" Channel: {channel_chat.title}\n"
                f" Channel ID: <code>{channel_id}</code>\n\n"
                " Testing log channel..."
            )
            
            await update.message.reply_text(success_message, parse_mode=ParseMode.HTML)
            
            # Send test message to log channel
            test_log = (
                " Log Channel Test\n\n"
                f" Successfully connected to {html.escape(update.effective_chat.title)}\n"
                " You will receive group logs here in real-time\n\n"
                " Available Events:\n"
                " • Message Edits\n"
                " • Message Deletions\n"
                " • Warnings\n"
                " • Bans/Unbans\n"
                " • Mutes/Unmutes\n"
                " • Settings Changes\n"
                " • Vote Bans"
            )
            
            await context.bot.send_message(
                chat_id=channel_id,
                text=test_log,
                parse_mode=ParseMode.HTML
            )
            
            # Log the setting change
            await self.log_event(
                update.effective_chat.id,
                "settings",
                {
                    "admin": update.effective_user,
                    "message": f"Set {channel_chat.title} as logging channel"
                },
                context
            )

        except Exception as e:
            await update.message.reply_text(
                f" Failed to set logging channel: {str(e)}\n"
                "Make sure:\n"
                "1. The channel ID/username is correct\n"
                "2. The bot is an admin in the channel\n"
                "3. The bot has permission to post messages"
            )

    @admin_only()
    @group_only()
    async def toggle_log_events(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Toggle specific logging events"""
        if not context.args or len(context.args) != 2:
            events_list = ", ".join(sorted(self.default_events.keys()))
            events_bullet = events_list.replace(", ", "\n• ")
            await update.message.reply_text(
                " Toggle Logging Events\n\n"
                "<code>/togglelog [event] [on/off]</code>\n\n"
                f"Available events:\n• {events_bullet}\n\n"
                "Example: <code>/togglelog poll off</code>",
                parse_mode=ParseMode.HTML
            )
            return

        event = context.args[0].lower()
        state = context.args[1].lower() in ["on", "true", "1", "yes"]

        if event not in self.default_events:
            events_list = ", ".join(sorted(self.default_events.keys()))
            events_bullet = events_list.replace(", ", "\n• ")
            await update.message.reply_text(
                f" Invalid event type!\n\n"
                f"Available events:\n• {events_bullet}",
                parse_mode=ParseMode.HTML
            )
            return

        # Get current disabled events
        disabled_events = list(await self.db.get_group_setting(update.effective_chat.id, "disabled_log_events", []))
        
        if state:  # Enable event
            if event in disabled_events:
                disabled_events.remove(event)
                status = "enabled"
            else:
                status = "already enabled"
        else:  # Disable event
            if event not in disabled_events:
                disabled_events.append(event)
                status = "disabled"
            else:
                status = "already disabled"
        
        # Update disabled events in database
        await self.db.set_group_setting(update.effective_chat.id, "disabled_log_events", disabled_events)
        
        # Log the change only if status changed
        if status in ["enabled", "disabled"]:
            await self.log_event(
                update.effective_chat.id,
                "settings",
                {
                    "admin": update.effective_user,
                    "message": f"{'Enabled' if state else 'Disabled'} logging for {event} events"
                },
                context
            )
        
        # Send response with current status
        icon = "" if state else ""
        await update.message.reply_text(
            f"{icon} Logging for <b>{event}</b> events is now {status}!",
            parse_mode=ParseMode.HTML
        )

    async def log_member_update(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Log member join/leave events"""
        if update.message.new_chat_members:
            for member in update.message.new_chat_members:
                if member.id != context.bot.id:
                    await self.log_event(
                        update.effective_chat.id,
                        "join",
                        {"user": member},
                        context
                    )
        elif update.message.left_chat_member:
            member = update.message.left_chat_member
            if member.id != context.bot.id:
                await self.log_event(
                    update.effective_chat.id,
                    "leave",
                    {"user": member},
                    context
                )

    async def log_message_edit(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Log message edits"""
        if not update.edited_message:
            return
            
        # Don't log bot's own message edits
        if update.edited_message.from_user.id == context.bot.id:
            return
            
        # Get old and new message text
        old_message = update.edited_message.text or update.edited_message.caption or ""
        new_message = update.edited_message.text or update.edited_message.caption or ""
        
        if old_message == new_message:
            return
            
        await self.log_event(
            update.effective_chat.id,
            "edit",
            {
                "user": update.effective_user,
                "old_message": old_message,
                "new_message": new_message,
                "message_link": f"https://t.me/c/{str(update.effective_chat.id)[4:]}/{update.edited_message.message_id}"
            },
            context
        )

    async def log_message_delete(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Log message deletions"""
        if not update.message:
            return
            
        # Don't log bot's own message deletions
        if update.message.from_user and update.message.from_user.id == context.bot.id:
            return
            
        message_text = update.message.text or update.message.caption or ""
        
        await self.log_event(
            update.effective_chat.id,
            "delete",
            {
                "user": update.effective_user,
                "message": message_text,
                "message_id": update.message.message_id
            },
            context
        )