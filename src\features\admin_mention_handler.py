import logging
from telegram import Update
from telegram.constants import <PERSON><PERSON><PERSON><PERSON>, ChatType
from telegram.ext import ContextTypes, MessageHandler, filters, Application

logger = logging.getLogger(__name__)

class AdminMentionHandler:
    """Handle @admin mentions by tagging all group administrators."""

    def __init__(self):
        pass

    async def handle_admin_ping(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Reply with mentions of all admins when @admin is detected."""
        message = update.effective_message
        chat = update.effective_chat

        if not message or not chat or chat.type not in [ChatType.GROUP, ChatType.SUPERGROUP]:
            return

        text = message.text or message.caption
        if not text or "@admin" not in text.lower():
            return

        try:
            admins = await context.bot.get_chat_administrators(chat.id)
            mentions = []
            for admin in admins:
                user = admin.user
                if user.is_bot:
                    continue
                mentions.append(user.mention_html())

            if mentions:
                mention_text = " ".join(mentions)
                await message.reply_text(mention_text, parse_mode=ParseMode.HTML)
        except Exception as e:
            logger.error(f"Failed to mention admins: {e}")

    def register_handlers(self, application: Application) -> None:
        """Register the admin mention message handler."""
        handler = MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_admin_ping)
        application.add_handler(handler, group=9)
