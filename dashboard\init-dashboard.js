const { exec } = require('child_process');
const path = require('path');

console.log('Initializing HelpingAI Dashboard...');

// Install frontend dependencies
console.log('Installing frontend dependencies...');
exec('npm install', (error, stdout, stderr) => {
  if (error) {
    console.error(`Error installing frontend dependencies: ${error}`);
    return;
  }
  
  console.log('Frontend dependencies installed successfully.');
  
  // Install backend dependencies
  console.log('Installing backend dependencies...');
  const backendPath = path.join(__dirname, 'backend');
  
  exec(`cd ${backendPath} && npm install`, (error, stdout, stderr) => {
    if (error) {
      console.error(`Error installing backend dependencies: ${error}`);
      return;
    }
    
    console.log('Backend dependencies installed successfully.');
    console.log('Dashboard initialization complete!');
    console.log('');
    console.log('To start the dashboard:');
    console.log('1. Start the backend: cd backend && npm start');
    console.log('2. Start the frontend: npm run dev');
  });
});