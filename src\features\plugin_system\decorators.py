"""
Decorators for the plugin system
"""
from functools import wraps
from typing import Callable, Optional, TypeVar, ParamSpec
from telegram import Update
from telegram.ext import ContextTypes
from .plugin_types import CommandInfo, PluginPermission

P = ParamSpec('P')
T = TypeVar('T')

def command(
    name: str,
    description: str,
    permission: PluginPermission = PluginPermission.USER,
    usage: Optional[str] = None,
    aliases: tuple[str, ...] = (),
    enabled: bool = True
) -> Callable[[Callable[P, T]], Callable[P, T]]:
    """
    Decorator to register a plugin command
    
    Example:
        @command(
            name="hello",
            description="Say hello",
            permission=PluginPermission.USER,
            usage="/hello [name]",
            aliases=("hi", "hey")
        )
        async def hello_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
            await update.message.reply_text("Hello!")
    """
    def decorator(func: Callable[P, T]) -> Callable[P, T]:
        @wraps(func)
        async def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
            # Get the first argument which should be 'self' (the plugin instance)
            if not args:
                raise ValueError("Plugin command must be a method")
            
            plugin_instance = args[0]
            if not plugin_instance.is_enabled:
                return None
                
            return await func(*args, **kwargs)
            
        # Store command info in the function
        wrapper.__command_info__ = CommandInfo(
            name=name,
            description=description,
            permission=permission,
            function=func,
            usage=usage,
            aliases=aliases,
            enabled=enabled
        )
        return wrapper
    return decorator

def event_handler(event_type: str) -> Callable[[Callable[P, T]], Callable[P, T]]:
    """
    Decorator to register an event handler
    
    Example:
        @event_handler("new_member")
        async def on_new_member(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
            await update.message.reply_text("Welcome!")
    """
    def decorator(func: Callable[P, T]) -> Callable[P, T]:
        @wraps(func)
        async def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
            if not args:
                raise ValueError("Event handler must be a method")
                
            plugin_instance = args[0]
            if not plugin_instance.is_enabled:
                return None
                
            return await func(*args, **kwargs)
            
        wrapper.__event_type__ = event_type
        return wrapper
    return decorator
