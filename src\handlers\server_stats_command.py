"""
Server statistics command handler.
"""
import logging
import asyncio
from telegram import Update
from telegram.ext import ContextTypes
from src.utils.decorators import admin_only, group_only
from src.database.tracker import db_tracker

logger = logging.getLogger(__name__)

@admin_only()
@group_only()
async def server_stats_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Get statistics for the current server.
    
    This command is only available to admins in groups.
    """
    if not update.effective_chat:
        return
    
    chat_id = update.effective_chat.id
    
    # Get server statistics
    stats = db_tracker.get_server_stats(chat_id)
    
    if not stats:
        result_msg = await update.message.reply_text("❌ <b>Failed to retrieve server statistics.</b>", parse_mode="HTML")
        
        # Schedule deletion after 30 seconds
        async def delete_messages():
            await asyncio.sleep(30)
            try:
                await result_msg.delete()
                if update.message:
                    await update.message.delete()
            except Exception:
                pass
        
        asyncio.create_task(delete_messages())
        return
    
    # Format statistics message using HTML
    # Escape HTML special characters in the title
    title = stats['title'].replace("<", "&lt;").replace(">", "&gt;").replace("&", "&amp;")
    message = (
        f"📊 <b>Server Stats for {title}</b>\n\n"
        f"👥 <b>Members:</b> {stats['member_count']}\n"
        f"💬 <b>Total Messages:</b> {stats['message_count']}\n"
        f"🤖 <b>Commands Used:</b> {stats['command_count']}\n\n"
    )

    # Add top users
    message += "👑 <b>Top Active Users:</b>\n"
    for i, user in enumerate(stats['top_users'][:5], 1):
        username = user['username'] or f"User {user['user_id']}"
        # Escape HTML special characters
        username = username.replace("<", "&lt;").replace(">", "&gt;").replace("&", "&amp;")
        message += f"{i}. {username}: {user['message_count']} messages\n"

    result_msg = await update.message.reply_text(message, parse_mode="HTML")
    
    # Schedule deletion after 30 seconds
    async def delete_messages():
        await asyncio.sleep(30)
        try:
            await result_msg.delete()
            if update.message:
                await update.message.delete()
        except Exception:
            pass
    
    asyncio.create_task(delete_messages())

@admin_only()
@group_only()
async def user_activity_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Get activity for a specific user in the server.
    
    Usage: /useractivity @username or /useractivity in reply to a message
    
    This command is only available to admins in groups.
    """
    if not update.effective_chat or not update.effective_user:
        return
    
    chat_id = update.effective_chat.id
    
    # Get target user
    target_user = None
    
    # Check if command is a reply to a message
    if update.message.reply_to_message and update.message.reply_to_message.from_user:
        target_user = update.message.reply_to_message.from_user
    # Check if username is provided
    elif context.args and len(context.args) > 0:
        username = context.args[0].replace("@", "")
        # Try to find user by username in the database
        try:
            from sqlalchemy import select
            from src.database.models import User, ServerMember, Server
            
            session = db_tracker.session
            server_stmt = select(Server).where(Server.telegram_id == chat_id)
            server = session.execute(server_stmt).scalar_one_or_none()
            
            if not server:
                result_msg = await update.message.reply_text("❌ <b>Server not found in database.</b>", parse_mode="HTML")
                
                # Schedule deletion after 30 seconds
                async def delete_messages():
                    await asyncio.sleep(30)
                    try:
                        await result_msg.delete()
                        if update.message:
                            await update.message.delete()
                    except Exception:
                        pass
                
                asyncio.create_task(delete_messages())
                return
            
            user_stmt = select(User).where(User.username == username)
            user = session.execute(user_stmt).scalar_one_or_none()
            
            if not user:
                result_msg = await update.message.reply_text(f"❌ <b>User @{username} not found in database.</b>", parse_mode="HTML")
                
                # Schedule deletion after 30 seconds
                async def delete_messages():
                    await asyncio.sleep(30)
                    try:
                        await result_msg.delete()
                        if update.message:
                            await update.message.delete()
                    except Exception:
                        pass
                
                asyncio.create_task(delete_messages())
                return
            
            # Check if user is a member of this server
            member_stmt = select(ServerMember).where(
                (ServerMember.server_id == server.id) & 
                (ServerMember.user_id == user.id)
            )
            member = session.execute(member_stmt).scalar_one_or_none()
            
            if not member:
                result_msg = await update.message.reply_text(f"❌ <b>User @{username} is not a member of this server.</b>", parse_mode="HTML")
                
                # Schedule deletion after 30 seconds
                async def delete_messages():
                    await asyncio.sleep(30)
                    try:
                        await result_msg.delete()
                        if update.message:
                            await update.message.delete()
                    except Exception:
                        pass
                
                asyncio.create_task(delete_messages())
                return
            
            # Create a simple user object with the necessary attributes
            class SimpleUser:
                def __init__(self, id, username, first_name):
                    self.id = id
                    self.username = username
                    self.first_name = first_name
            
            target_user = SimpleUser(
                id=user.telegram_id,
                username=user.username,
                first_name=user.first_name
            )
        except Exception as e:
            logger.error(f"Error finding user by username: {e}")
            result_msg = await update.message.reply_text("❌ <b>Failed to find user.</b>", parse_mode="HTML")
            
            # Schedule deletion after 30 seconds
            async def delete_messages():
                await asyncio.sleep(30)
                try:
                    await result_msg.delete()
                    if update.message:
                        await update.message.delete()
                except Exception:
                    pass
            
            asyncio.create_task(delete_messages())
            return
    
    if not target_user:
        result_msg = await update.message.reply_text(
            "❌ <b>Please specify a user by replying to their message or mentioning their username.</b>",
            parse_mode="HTML"
        )
        
        # Schedule deletion after 30 seconds
        async def delete_messages():
            await asyncio.sleep(30)
            try:
                await result_msg.delete()
                if update.message:
                    await update.message.delete()
            except Exception:
                pass
        
        asyncio.create_task(delete_messages())
        return
    
    # Get user activity
    try:
        from sqlalchemy import select, func
        from src.database.models import User, ServerMember, Server, Message, CommandUsage
        
        session = db_tracker.session
        
        # Get user from database
        user_stmt = select(User).where(User.telegram_id == target_user.id)
        user = session.execute(user_stmt).scalar_one_or_none()
        
        if not user:
            result_msg = await update.message.reply_text(f"❌ <b>User not found in database.</b>", parse_mode="HTML")
            
            # Schedule deletion after 30 seconds
            async def delete_messages():
                await asyncio.sleep(30)
                try:
                    await result_msg.delete()
                    if update.message:
                        await update.message.delete()
                except Exception:
                    pass
            
            asyncio.create_task(delete_messages())
            return
        
        # Get server from database
        server_stmt = select(Server).where(Server.telegram_id == chat_id)
        server = session.execute(server_stmt).scalar_one_or_none()
        
        if not server:
            result_msg = await update.message.reply_text("❌ <b>Server not found in database.</b>", parse_mode="HTML")
            
            # Schedule deletion after 30 seconds
            async def delete_messages():
                await asyncio.sleep(30)
                try:
                    await result_msg.delete()
                    if update.message:
                        await update.message.delete()
                except Exception:
                    pass
            
            asyncio.create_task(delete_messages())
            return
        
        # Get server member
        member_stmt = select(ServerMember).where(
            (ServerMember.server_id == server.id) & 
            (ServerMember.user_id == user.id)
        )
        member = session.execute(member_stmt).scalar_one_or_none()
        
        if not member:
            result_msg = await update.message.reply_text(f"❌ <b>User is not a member of this server.</b>", parse_mode="HTML")
            
            # Schedule deletion after 30 seconds
            async def delete_messages():
                await asyncio.sleep(30)
                try:
                    await result_msg.delete()
                    if update.message:
                        await update.message.delete()
                except Exception:
                    pass
            
            asyncio.create_task(delete_messages())
            return
        
        # Get message count
        message_count_stmt = select(func.count(Message.id)).where(
            (Message.user_id == user.id) & 
            (Message.chat_id == chat_id)
        )
        message_count = session.execute(message_count_stmt).scalar_one()
        
        # Get command count
        command_count_stmt = select(func.count(CommandUsage.id)).where(
            (CommandUsage.user_id == user.id) & 
            (CommandUsage.server_id == server.id)
        )
        command_count = session.execute(command_count_stmt).scalar_one()
        
        # Get most used commands
        top_commands_stmt = select(
            CommandUsage.command, 
            func.count(CommandUsage.id).label('count')
        ).where(
            (CommandUsage.user_id == user.id) & 
            (CommandUsage.server_id == server.id)
        ).group_by(
            CommandUsage.command
        ).order_by(
            func.count(CommandUsage.id).desc()
        ).limit(5)
        
        top_commands = session.execute(top_commands_stmt).all()
        
        # Format user activity message
        username = target_user.username or target_user.first_name or f"User {target_user.id}"
        # Escape HTML special characters
        username = username.replace("<", "&lt;").replace(">", "&gt;").replace("&", "&amp;")
        message = (
            f"📊 <b>User Activity for @{username}</b>\n\n"
            f"💬 <b>Total Messages:</b> {message_count}\n"
            f"🤖 <b>Commands Used:</b> {command_count}\n"
            f"⏱️ <b>First Seen:</b> {member.joined_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"⏱️ <b>Last Active:</b> {member.last_active.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        )
        
        # Add top commands
        if top_commands:
            message += "🔝 <b>Most Used Commands:</b>\n"
            for cmd, count in top_commands:
                message += f"/{cmd}: {count} times\n"
        
        result_msg = await update.message.reply_text(message, parse_mode="HTML")
        
        # Schedule deletion after 30 seconds
        async def delete_messages():
            await asyncio.sleep(30)
            try:
                await result_msg.delete()
                if update.message:
                    await update.message.delete()
            except Exception:
                pass
        
        asyncio.create_task(delete_messages())
    except Exception as e:
        logger.error(f"Error getting user activity: {e}")
        result_msg = await update.message.reply_text("❌ <b>Failed to retrieve user activity.</b>", parse_mode="HTML")
        
        # Schedule deletion after 30 seconds
        async def delete_messages():
            await asyncio.sleep(30)
            try:
                await result_msg.delete()
                if update.message:
                    await update.message.delete()
            except Exception:
                pass
        
        asyncio.create_task(delete_messages())
        return