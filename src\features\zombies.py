import asyncio
import logging
from typing import Op<PERSON>, <PERSON>, Tuple, Set

from telegram import Update, Chat<PERSON><PERSON><PERSON>
from telegram.constants import Parse<PERSON><PERSON>, ChatMemberStatus
from telegram.error import TelegramError, BadRequest
from telegram.ext import ContextTypes, CommandHandler

# Assuming Database is defined elsewhere; replace with your actual DB implementation
from src.database.db import Database

logger = logging.getLogger(__name__)

class ZombieHandler:
    """Handles detection and optional removal of deleted accounts (zombies) from groups."""

    def __init__(self, db: Optional[Database] = None):
        """
        Initialize the ZombieHandler.

        Args:
            db (Optional[Database]): Database instance for storing member IDs or settings.
        """
        self.db = db
        self.zombie_cleanup_lock = {}  # Lock per chat_id to prevent concurrent cleanups
        self.progress_messages = {}    # Store progress message IDs per chat

    async def check_admin(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> bool:
        """Check if the user is an admin or owner."""
        if not update.effective_chat or not update.effective_user:
            return False
        try:
            chat_member = await context.bot.get_chat_member(
                update.effective_chat.id, update.effective_user.id
            )
            return chat_member.status in [ChatMemberStatus.ADMINISTRATOR, ChatMemberStatus.OWNER]
        except TelegramError as e:
            logger.error(f"Error checking admin status: {e}")
            return False

    async def update_progress(self, message_id: int, chat_id: int, context: ContextTypes.DEFAULT_TYPE,
                              current: int, total: int, zombies_found: int) -> None:
        """Update the progress message during scanning."""
        try:
            progress = min((current / max(total, 1)) * 100, 100)
            progress_bar = "▓" * int(progress // 5) + "░" * (20 - int(progress // 5))
            status_text = (
                f"🔍 <b>Scanning for zombies...</b>\n\n"
                f"Progress: [{progress_bar}] {progress:.1f}%\n"
                f"Checked: {current}/{total} members\n"
                f"Zombies found: {zombies_found}\n\n"
                f"<i>Please wait; scanning in progress...</i>"
            )
            await context.bot.edit_message_text(
                chat_id=chat_id, message_id=message_id, text=status_text, parse_mode=ParseMode.HTML
            )
        except TelegramError as e:
            logger.error(f"Error updating progress: {e}")

    async def is_zombie(self, member: ChatMember) -> bool:
        """Check if a member is a deleted account (zombie)."""
        if not member.user:
            return True
        user = member.user
        try:
            # Refined check for deleted accounts based on Telegram API behavior
            is_deleted = (
                hasattr(user, 'is_deleted') and user.is_deleted or
                (user.first_name == "Deleted Account" and not user.last_name and not user.username) or
                (not user.first_name and not user.last_name and not user.username)
            )
            if is_deleted:
                logger.info(f"Found zombie account: ID={user.id}, Name={user.first_name}")
            return is_deleted
        except Exception as e:
            logger.error(f"Error checking zombie status for user {user.id}: {e}")
            return False

    async def scan_for_zombies(self, chat_id: int, context: ContextTypes.DEFAULT_TYPE) -> Tuple[List[int], int]:
        """
        Scan the group for zombie accounts using available methods.

        Since full member enumeration isn't directly supported, we:
        1. Fetch admins directly.
        2. Collect user IDs from recent updates or messages (requires prior activity tracking).
        3. Check each member's status individually via get_chat_member.

        Returns:
            Tuple of zombie user IDs and total members processed.
        """
        zombie_ids: List[int] = []
        processed_ids: Set[int] = set()

        try:
            total_members = await context.bot.get_chat_member_count(chat_id)
            if total_members <= 1:
                return [], total_members

            # Step 1: Check administrators
            admins = await context.bot.get_chat_administrators(chat_id)
            for admin in admins:
                if admin.user:
                    processed_ids.add(admin.user.id)
                    if await self.is_zombie(admin):
                        zombie_ids.append(admin.user.id)

            # Step 2: Collect user IDs from recent updates (limited scope)
            # Note: This requires the bot to have seen messages or updates from members
            updates = await context.bot.get_updates(limit=100)
            for update in updates:
                if update.message and update.message.chat.id == chat_id and update.message.from_user:
                    processed_ids.add(update.message.from_user.id)

            # Step 3: Scan collected IDs
            processed = len(processed_ids)
            for user_id in processed_ids:
                try:
                    member = await context.bot.get_chat_member(chat_id, user_id)
                    if await self.is_zombie(member):
                        zombie_ids.append(user_id)
                    if processed % 5 == 0 and chat_id in self.progress_messages:
                        await self.update_progress(
                            self.progress_messages[chat_id], chat_id, context,
                            processed, total_members, len(zombie_ids)
                        )
                except TelegramError as e:
                    logger.debug(f"Skipping user {user_id}: {e}")
                    continue

            # Log limitation warning
            if processed < total_members:
                logger.warning(f"Only scanned {processed}/{total_members} members due to API limits.")

            return list(set(zombie_ids)), total_members
        except TelegramError as e:
            logger.error(f"Error scanning for zombies: {e}")
            return [], 0

    async def clean_zombies(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Remove zombie accounts from the group."""
        if not update.effective_chat or not update.effective_message:
            return

        chat_id = update.effective_chat.id
        if self.zombie_cleanup_lock.get(chat_id, False):
            await update.effective_message.reply_text(
                "⚠️ A zombie cleanup is already in progress!", parse_mode=ParseMode.HTML
            )
            return

        if not await self.check_admin(update, context):
            await update.effective_message.reply_text(
                "❌ This command is for administrators only!", parse_mode=ParseMode.HTML
            )
            return

        bot_member = await context.bot.get_chat_member(chat_id, context.bot.id)
        if not bot_member.can_restrict_members:
            await update.effective_message.reply_text(
                "❌ I need permission to ban users to remove zombies!", parse_mode=ParseMode.HTML
            )
            return

        try:
            self.zombie_cleanup_lock[chat_id] = True
            progress_msg = await update.effective_message.reply_text(
                "🔍 <b>Starting zombie scan and removal...</b>\n\n"
                "<i>Please wait; this may take a while...</i>",
                parse_mode=ParseMode.HTML
            )
            self.progress_messages[chat_id] = progress_msg.message_id

            zombie_ids, total_members = await self.scan_for_zombies(chat_id, context)
            if not zombie_ids:
                await progress_msg.edit_text(
                    f"✅ <b>No zombies found!</b>\n\nChecked {total_members} members.\nThis group is clean! 🧹",
                    parse_mode=ParseMode.HTML
                )
                return

            removed_count = 0
            failed_count = 0
            for zombie_id in zombie_ids:
                try:
                    await context.bot.ban_chat_member(chat_id, zombie_id)
                    removed_count += 1
                    if removed_count % 5 == 0:
                        await progress_msg.edit_text(
                            f"🧹 <b>Removing zombies...</b>\n\n"
                            f"Progress: {removed_count}/{len(zombie_ids)}\n"
                            f"Failed: {failed_count}\n\n<i>Please wait...</i>",
                            parse_mode=ParseMode.HTML
                        )
                except BadRequest as e:
                    logger.error(f"Failed to remove zombie {zombie_id}: {e}")
                    failed_count += 1
                await asyncio.sleep(0.5)  # Avoid rate limits

            report = (
                f"🧟‍♂️ <b>Zombie Cleanup Report</b>\n\n"
                f"Total members: {total_members}\n"
                f"Zombies found: {len(zombie_ids)}\n"
                f"Successfully removed: {removed_count}\n"
                f"Failed to remove: {failed_count}\n\n"
                f"<i>The group has been cleaned! 🧹</i>"
            )
            await progress_msg.edit_text(report, parse_mode=ParseMode.HTML)
        except Exception as e:
            logger.error(f"Error in zombie cleanup: {e}")
            await update.effective_message.reply_text(
                f"❌ An error occurred: {str(e)}", parse_mode=ParseMode.HTML
            )
        finally:
            self.zombie_cleanup_lock[chat_id] = False
            if chat_id in self.progress_messages:
                del self.progress_messages[chat_id]

    async def scan_zombies(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Scan for zombies without removing them."""
        if not update.effective_chat or not update.effective_message:
            return

        chat_id = update.effective_chat.id
        if not await self.check_admin(update, context):
            await update.effective_message.reply_text(
                "❌ This command is for administrators only!", parse_mode=ParseMode.HTML
            )
            return

        try:
            progress_msg = await update.effective_message.reply_text(
                "🔍 <b>Starting zombie scan...</b>\n\n<i>Please wait; this may take a while...</i>",
                parse_mode=ParseMode.HTML
            )
            self.progress_messages[chat_id] = progress_msg.message_id

            zombie_ids, total_members = await self.scan_for_zombies(chat_id, context)
            if not zombie_ids:
                await progress_msg.edit_text(
                    f"✅ <b>No zombies found!</b>\n\nChecked {total_members} members.",
                    parse_mode=ParseMode.HTML
                )
            else:
                report = (
                    f"🧟‍♂️ <b>Zombie Scan Report</b>\n\n"
                    f"Total members scanned: {total_members}\n"
                    f"Zombies detected: {len(zombie_ids)}\n"
                    f"Zombie IDs: {', '.join(str(z) for z in zombie_ids)}\n\n"
                    f"<i>No actions were taken.</i>"
                )
                await progress_msg.edit_text(report, parse_mode=ParseMode.HTML)
        except Exception as e:
            logger.error(f"Error during zombie scan: {e}")
            await update.effective_message.reply_text(
                f"❌ An error occurred: {str(e)}", parse_mode=ParseMode.HTML
            )
        finally:
            if chat_id in self.progress_messages:
                del self.progress_messages[chat_id]

    async def zombies(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle the /zombies command."""
        if not update.effective_chat or update.effective_chat.type == "private":
            msg = await update.message.reply_text("❌ This command can only be used in groups!")
            await asyncio.sleep(30)
            try:
                await context.bot.delete_message(chat_id=update.effective_chat.id, message_id=msg.message_id)
                await context.bot.delete_message(chat_id=update.effective_chat.id, message_id=update.message.message_id)
            except TelegramError:
                pass
            return

        args = context.args or []
        flags = [arg.lower() for arg in args]
        if "--cls" in flags or "--clean" in flags:
            await self.clean_zombies(update, context)
        else:
            await self.scan_zombies(update, context)

def setup(application):
    """Setup the zombie command handler."""
    zombie_handler = ZombieHandler()
    application.add_handler(CommandHandler("zombies", zombie_handler.zombies))
