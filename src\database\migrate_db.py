"""
Database migration script - SQLite initialization.
"""
import logging
import os

logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def migrate_database(db_path="data/bot_database.db"):
    """
    Initialize SQLite database with all necessary tables.
    This function creates the database structure if it doesn't exist.
    """
    try:
        # Import and initialize the database
        from .db import Database
        
        # Create data directory if it doesn't exist
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # Initialize the database (this will create all tables)
        db = Database(db_path)
        
        logger.info(f"SQLite database initialized successfully at {db_path}")
        return True
        
    except Exception as e:
        logger.error(f"Error during database migration: {e}")
        return False

if __name__ == "__main__":
    migrate_database()
