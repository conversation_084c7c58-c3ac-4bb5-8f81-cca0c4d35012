"""
Database statistics command handler.
"""
import logging
from telegram import Update
from telegram.ext import ContextTypes
from src.utils.decorators import owner_only
from src.database.tracker import db_tracker
from sqlalchemy import func, select
from src.database.models import User, Server, Message, CommandUsage, ServerMember

logger = logging.getLogger(__name__)

@owner_only()
async def db_stats_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Get statistics for the entire database.
    
    This command is only available to the bot owner.
    """
    try:
        session = db_tracker.session
        
        # Get counts
        user_count = session.execute(select(func.count(User.id))).scalar_one()
        server_count = session.execute(select(func.count(Server.id))).scalar_one()
        message_count = session.execute(select(func.count(Message.id))).scalar_one()
        command_count = session.execute(select(func.count(CommandUsage.id))).scalar_one()
        member_count = session.execute(select(func.count(ServerMember.id))).scalar_one()
        
        # Get top servers by message count
        top_servers_stmt = select(
            Server.title,
            Server.telegram_id,
            func.count(Message.id).label('count')
        ).join(
            Message, 
            Message.chat_id == Server.telegram_id
        ).group_by(
            Server.id
        ).order_by(
            func.count(Message.id).desc()
        ).limit(5)
        
        top_servers = session.execute(top_servers_stmt).all()
        
        # Get top users by message count
        top_users_stmt = select(
            User.username,
            User.telegram_id,
            func.count(Message.id).label('count')
        ).join(
            Message,
            Message.user_id == User.id
        ).group_by(
            User.id
        ).order_by(
            func.count(Message.id).desc()
        ).limit(5)
        
        top_users = session.execute(top_users_stmt).all()
        
        # Get top commands
        top_commands_stmt = select(
            CommandUsage.command,
            func.count(CommandUsage.id).label('count')
        ).group_by(
            CommandUsage.command
        ).order_by(
            func.count(CommandUsage.id).desc()
        ).limit(5)
        
        top_commands = session.execute(top_commands_stmt).all()
        
        # Format statistics message
        message = (
            f"📊 *Database Statistics*\n\n"
            f"👤 *Users:* {user_count} — That's more fans than your mixtape ever had!\n"
            f"🌐 *Servers:* {server_count} — More homes than Monopoly, baby!\n"
            f"👥 *Server Members:* {member_count} — Squad goals, but only the real ones count.\n"
            f"💬 *Total Messages:* {message_count} — Talk is cheap, but you all spend big!\n"
            f"🤖 *Commands Used:* {command_count} — Y'all love pressing my buttons, huh?\n\n"
        )
        
        # Add top servers
        if top_servers:
            message += "🔝 *Top Servers by Activity:*\n"
            for i, (title, server_id, count) in enumerate(top_servers, 1):
                server_title = title or f"Server {server_id}"
                message += f"{i}. {server_title}: {count} messages — This server's got more action than a rap battle!\n"
            message += "\n"
        
        # Add top users
        if top_users:
            message += "🔝 *Top Users by Activity:*\n"
            for i, (username, user_id, count) in enumerate(top_users, 1):
                user_name = username or f"User {user_id}"
                message += f"{i}. {user_name}: {count} messages — Keyboard warriors, step aside!\n"
            message += "\n"
        
        # Add top commands
        if top_commands:
            message += "🔝 *Most Used Commands:*\n"
            for i, (command, count) in enumerate(top_commands, 1):
                message += f"{i}. /{command}: {count} times — Can't live without me, huh?\n"
        
        await update.message.reply_text(message, parse_mode="Markdown")
    except Exception as e:
        logger.error(f"Error getting database statistics: {e}")
        await update.message.reply_text("❌ Failed to retrieve database statistics.")
        return