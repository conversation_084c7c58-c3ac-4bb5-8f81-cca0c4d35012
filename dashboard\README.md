# HelpingAI Bot Dashboard

A modern web-based dashboard for managing the HelpingAI Telegram bot.

## Features

- Real-time statistics and analytics
- Group management
- User management
- Broadcast messaging system
- Content moderation tools
- AI interaction monitoring
- Settings configuration

## Tech Stack

### Frontend
- React.js with JSX
- Material-UI (MUI) for components
- Recharts for data visualization
- React Router for navigation

### Backend
- Node.js with Express.js
- SQLite database
- JWT for authentication

## Installation

1. Install frontend dependencies:
```bash
npm install
```

2. Install backend dependencies:
```bash
cd backend
npm install
```

## Running the Dashboard

1. Start the backend API server:
```bash
cd backend
npm start
```

2. In a new terminal, start the frontend development server:
```bash
npm run dev
```

The dashboard will be available at http://localhost:3000

## Building for Production

To create a production build:

```bash
npm run build
```

The built files will be in the `dist` directory.

## Project Structure

```
dashboard/
├── src/
│   ├── components/
│   ├── pages/
│   ├── App.jsx
│   └── main.jsx
├── backend/
│   ├── server.js
│   └── package.json
├── package.json
└── vite.config.js
```

## API Endpoints

- `POST /api/auth/login` - User login
- `GET /api/dashboard/stats` - Dashboard statistics
- `GET /api/dashboard/analytics` - Analytics data
- `GET /api/groups` - List all groups
- `GET /api/users` - List all users
- `GET /api/broadcast/history` - Broadcast history
- `GET /api/moderation/queue` - Moderation queue
- `GET /api/ai/interactions` - AI interactions
- `GET /api/settings` - Get settings
- `PUT /api/settings` - Update settings

## Development

To run in development mode with hot reloading:

```bash
npm run dev
```

## License

This project is proprietary to HelpingAI Technology.