
"""
🌐 Enhanced Translation Handler Module
----------------------------------------
<PERSON>les message translation using Google Translate via NexusAI.
Features:
 - Improved professional code standards and docstrings.
 - Comprehensive support for Indian languages.
 - Translation of long messages in manageable chunks to avoid API limitations.
"""

import asyncio
import time
import logging
from functools import lru_cache
from typing import Dict, List, Optional, Tuple
import aiohttp
from concurrent.futures import ThreadPoolExecutor

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, Message
from telegram.ext import ContextTypes, CommandHandler, CallbackQueryHandler
from telegram.constants import ParseMode
from telegram.error import TelegramError
from deep_translator import GoogleTranslator

# Add the import at the top of the file
from src.features.language_data import LANGUAGES, QUICK_LANGUAGES

# Configure logging for the module
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def split_text_chunks(text: str, max_chunk: int = 500) -> List[str]:
    """
    Split the text into chunks not exceeding max_chunk characters,
    ensuring words are not broken arbitrarily.
    """
    words = text.split()
    chunks = []
    current_chunk = ""
    for word in words:
        if current_chunk:
            # +1 accounts for a space character
            if len(current_chunk) + len(word) + 1 > max_chunk:
                chunks.append(current_chunk)
                current_chunk = word
            else:
                current_chunk += " " + word
        else:
            current_chunk = word
    if current_chunk:
        chunks.append(current_chunk)
    return chunks

class TranslationHandler:
    def __init__(self):
        """Initialize the translation handler with improved performance."""
        self.recent_messages: Dict[str, List[Dict]] = {}  # Recent messages per chat for translation
        self.translation_cache: Dict[str, Tuple[str, float]] = {}  # Cache for translations (result, timestamp)
        self.cache_timeout: int = 3600  # Cache timeout in seconds (1 hour)
        self.executor = ThreadPoolExecutor(max_workers=10)  # Thread pool for concurrent translations
        self.semaphore = asyncio.Semaphore(20)  # Limit concurrent API calls
        self.delete_delay = 60  # Delete messages after 60 seconds
        
    async def _delete_message_later(self, message: Message, delay: int = 60) -> None:
        """Delete a message after a specified delay in seconds."""
        await asyncio.sleep(delay)
        try:
            await message.delete()
        except Exception as e:
            logger.error(f"Error deleting message: {e}")
            
    async def translate_text(self, text: str, dest_lang: str) -> str:
        """
        Translate text to the specified language with improved performance.
        Uses thread pool for concurrent translations and semaphore for rate limiting.
        """
        cache_key = f"{text}:{dest_lang}"
        current_time = time.time()

        # Check cache first
        if cache_key in self.translation_cache:
            cached_result, timestamp = self.translation_cache[cache_key]
            if current_time - timestamp < self.cache_timeout:
                return cached_result

        async with self.semaphore:
            try:
                # Run translation in thread pool to avoid blocking
                loop = asyncio.get_running_loop()
                
                # Define the translation function to run in thread
                def translate_chunk(chunk):
                    translator = GoogleTranslator(source='auto', target=dest_lang)
                    return translator.translate(chunk)
                
                # For short text, translate directly
                if len(text) <= 1000:
                    translated = await loop.run_in_executor(
                        self.executor, 
                        lambda: translate_chunk(text)
                    )
                else:
                    # Split long text into chunks and translate concurrently
                    chunks = [text[i:i+1000] for i in range(0, len(text), 1000)]
                    tasks = []
                    for chunk in chunks:
                        task = loop.run_in_executor(self.executor, translate_chunk, chunk)
                        tasks.append(task)
                    
                    # Gather all translation results
                    results = await asyncio.gather(*tasks)
                    translated = " ".join(results)
                
                # Cache the result
                self.translation_cache[cache_key] = (translated, current_time)
                return translated
            except Exception as e:
                logger.error(f"Translation error: {str(e)}")
                return f"Translation error: {str(e)}"

    async def detect_language(self, text: str) -> str:
        """
        Detect the language of the provided text with improved performance.
        """
        async with self.semaphore:
            try:
                # Run detection in thread pool
                loop = asyncio.get_running_loop()
                detected = await loop.run_in_executor(
                    self.executor,
                    lambda: GoogleTranslator(source='auto', target='en').detect_language(text)
                )
                return detected if detected in LANGUAGES else 'unknown'
            except Exception as e:
                logger.error(f"Language detection error: {str(e)}")
                return 'unknown'

    async def translate_message(self, update: Update, context) -> None:
        """
        Process the '/tr' command with simplified output and faster response.
        """
        if not update.message:
            return

        # Schedule deletion of command message
        if update.message:
            asyncio.create_task(self._delete_message_later(update.message, self.delete_delay))

        # Handle missing reply or arguments
        if not update.message.reply_to_message or not context.args:
            response = await update.message.reply_text(
                "Usage: Reply to a message with /tr [language_code]",
                parse_mode=ParseMode.HTML
            )
            asyncio.create_task(self._delete_message_later(response, self.delete_delay))
            return

        target_lang = context.args[0].lower()
        if target_lang not in LANGUAGES:
            response = await update.message.reply_text(f"Unknown language code: {target_lang}")
            asyncio.create_task(self._delete_message_later(response, self.delete_delay))
            return

        original_text = update.message.reply_to_message.text
        if not original_text:
            response = await update.message.reply_text("Can only translate text messages")
            asyncio.create_task(self._delete_message_later(response, self.delete_delay))
            return

        # Send "translating" status message
        status_msg = await update.message.reply_text("Translating...")
        
        # Detect and translate concurrently
        source_lang_task = asyncio.create_task(self.detect_language(original_text))
        translated_task = asyncio.create_task(self.translate_text(original_text, target_lang))
        
        # Wait for both tasks to complete
        translated = await translated_task
        source_lang = await source_lang_task
        
        if translated.startswith("Translation error:"):
            await status_msg.edit_text(f"Translation failed: {translated}")
            asyncio.create_task(self._delete_message_later(status_msg, self.delete_delay))
            return

        # Simplified response - just the translation
        await status_msg.edit_text(
            f"{translated}",
            parse_mode=ParseMode.HTML
        )
        
        # Schedule deletion of translation message
        asyncio.create_task(self._delete_message_later(status_msg, self.delete_delay))

    async def translate_last_message(self, update: Update, context) -> None:
        """
        Process the '/tl' command with simplified output.
        """
        chat_id = str(update.effective_chat.id)
        if not update.message:
            return
            
        # Schedule deletion of command message
        if update.message:
            asyncio.create_task(self._delete_message_later(update.message, self.delete_delay))

        if chat_id not in self.recent_messages or not self.recent_messages[chat_id]:
            response = await update.message.reply_text("No recent messages available for translation")
            asyncio.create_task(self._delete_message_later(response, self.delete_delay))
            return

        # Parse target language from command
        args = context.args
        if not args:
            response = await update.message.reply_text("Usage: /tl [language_code]")
            asyncio.create_task(self._delete_message_later(response, self.delete_delay))
            return

        target_lang = args[0].lower()
        if target_lang not in LANGUAGES:
            response = await update.message.reply_text(f"Unknown language code: {target_lang}")
            asyncio.create_task(self._delete_message_later(response, self.delete_delay))
            return

        last_entry = self.recent_messages[chat_id][-1]
        original_text = last_entry['text']

        # Send "translating" status message
        status_msg = await update.message.reply_text("Translating...")
        
        # Translate directly
        translated = await self.translate_text(original_text, target_lang)
        
        if translated.startswith("Translation error:"):
            await status_msg.edit_text(f"Translation failed: {translated}")
            asyncio.create_task(self._delete_message_later(status_msg, self.delete_delay))
            return

        # Simplified response - just the translation
        await status_msg.edit_text(
            f"{translated}",
            parse_mode=ParseMode.HTML
        )
        
        # Schedule deletion of translation message
        asyncio.create_task(self._delete_message_later(status_msg, self.delete_delay))

    async def show_languages(self, update: Update, context, page: int = 0) -> None:
        """
        Display a paginated list of available languages with inline keyboards.
        """
        if not update.effective_message:
            return
            
        # Schedule deletion of command message
        if update.message:
            asyncio.create_task(self._delete_message_later(update.message, self.delete_delay))

        keyboard = []
        languages_list = sorted(LANGUAGES.items(), key=lambda x: x[1])
        languages_per_page = 15
        total_pages = (len(languages_list) + languages_per_page - 1) // languages_per_page
        start_idx = page * languages_per_page
        end_idx = start_idx + languages_per_page

        for code, name in languages_list[start_idx:end_idx]:
            keyboard.append([InlineKeyboardButton(
                f"{name}",
                callback_data=f"lang_{code}"
            )])
        
        nav_row = []
        if page > 0:
            nav_row.append(InlineKeyboardButton("⬅️ Previous", callback_data=f"lang_prev_{page}"))
        nav_row.append(InlineKeyboardButton("❌ Close", callback_data="lang_close"))
        if page < total_pages - 1:
            nav_row.append(InlineKeyboardButton("Next ➡️", callback_data=f"lang_next_{page}"))
        keyboard.append(nav_row)

        help_text = (
            "🌍 <b>Available Languages</b>\n\n"
            "<i>Select a language for translation:</i>\n\n"
            "Quick Guide:\n"
            "• /tr [code] - Translate the replied message\n"
            "• /tl [code] - Translate the last stored message\n"
            f"Page {page + 1}/{total_pages}"
        )

        if update.callback_query:
            await update.callback_query.edit_message_text(
                help_text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode=ParseMode.HTML
            )
        else:
            msg = await update.effective_message.reply_text(
                help_text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode=ParseMode.HTML
            )
            # Schedule deletion of languages message
            asyncio.create_task(self._delete_message_later(msg, self.delete_delay))

    async def handle_callback(self, update: Update, context) -> None:
        """
        Handle callback queries originating from language selection inline keyboards.
        """
        query = update.callback_query
        if not query:
            return

        data = query.data
        if data.startswith(("tr_", "tl_")):
            cmd, action = data.split("_")
            if action == "more":
                await self.show_languages(update, context, page=0)
            else:
                await query.answer(f"Selected language: {LANGUAGES.get(action, action)}")
                context.args = [action]
                if cmd == "tr":
                    await self.translate_message(update, context)
                else:
                    await self.translate_last_message(update, context)
        elif data.startswith("lang_"):
            parts = data.split("_")
            if len(parts) < 2:
                return
            action = parts[1]
            if action == "close":
                await query.message.delete()
                await query.answer("Language selection closed")
            elif action in ["prev", "next"]:
                current_page = int(parts[2]) if len(parts) > 2 else 0
                new_page = current_page - 1 if action == "prev" else current_page + 1
                await self.show_languages(update, context, page=new_page)
            elif action in LANGUAGES:
                await query.answer(f"Selected language: {LANGUAGES[action]}")
                # Handle language selection
                # This would typically trigger a translation

    def get_language_list(self) -> str:
        """
        Return a formatted list of available languages.
        """
        return "\n".join([f"{name} - <code>{code}</code>" for code, name in sorted(LANGUAGES.items(), key=lambda x: x[1])])

    def register_handlers(self, application):
        """Register command handlers for translation features."""
        application.add_handler(CommandHandler("tr", self.translate_message))
        application.add_handler(CommandHandler("tl", self.translate_last_message))
        application.add_handler(CommandHandler("languages", self.show_languages))
        
        # Add callback handler for translation-related buttons
        application.add_handler(CallbackQueryHandler(
            self.handle_callback,
            pattern=r"^(tr_|tl_|lang_)"
        ))

def setup(application):
    """Setup translation command handlers"""
    translation_handler = TranslationHandler()
    translation_handler.register_handlers(application)
    return translation_handler

