import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Button,
  Divider,
  Card,
  CardContent,
  CardActions,
  Tabs,
  Tab
} from '@mui/material';

export default function Settings() {
  const [activeTab, setActiveTab] = useState(0);
  
  // Bot settings
  const [botSettings, setBotSettings] = useState({
    botName: 'HelpingAI Bot',
    botUsername: '@helpingai_bot',
    ownerUsername: '@botowner',
    logChannel: '-1001234567890',
  });
  
  // Protection settings
  const [protectionSettings, setProtectionSettings] = useState({
    spamProtection: true,
    floodProtection: true,
    urlFilter: true,
    nsfwFilter: false,
    restrictNewUsers: false,
  });
  
  // Welcome settings
  const [welcomeSettings, setWelcomeSettings] = useState({
    welcomeEnabled: true,
    welcomeMessage: 'Welcome {user} to {chat}! Please read our rules and enjoy your stay!',
    goodbyeEnabled: true,
    goodbyeMessage: 'Goodbye {user}! We hope to see you again.',
  });
  
  // AI settings
  const [aiSettings, setAiSettings] = useState({
    aiEnabled: true,
    aiModel: 'Dhanishtha-2.0-preview',
    aiTemperature: 0.7,
    aiMaxTokens: 500,
  });

  const handleSaveSettings = () => {
    // Handle saving settings
    console.log('Saving settings:', {
      botSettings,
      protectionSettings,
      welcomeSettings,
      aiSettings
    });
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Typography variant="h1" gutterBottom>
        Bot Settings
      </Typography>
      
      <Paper sx={{ mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange} centered>
          <Tab label="General" />
          <Tab label="Protection" />
          <Tab label="Welcome" />
          <Tab label="AI Settings" />
        </Tabs>
      </Paper>
      
      {activeTab === 0 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h2" gutterBottom>
                General Settings
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Bot Name"
                    value={botSettings.botName}
                    onChange={(e) => setBotSettings({...botSettings, botName: e.target.value})}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Bot Username"
                    value={botSettings.botUsername}
                    onChange={(e) => setBotSettings({...botSettings, botUsername: e.target.value})}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Owner Username"
                    value={botSettings.ownerUsername}
                    onChange={(e) => setBotSettings({...botSettings, ownerUsername: e.target.value})}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Log Channel ID"
                    value={botSettings.logChannel}
                    onChange={(e) => setBotSettings({...botSettings, logChannel: e.target.value})}
                  />
                </Grid>
              </Grid>
            </Paper>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h3" gutterBottom>
                  Bot Information
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  <strong>Version:</strong> v1.2.0
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  <strong>Uptime:</strong> 15 days, 4 hours
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  <strong>Total Groups:</strong> 1,248
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  <strong>Total Users:</strong> 245,678
                </Typography>
              </CardContent>
              <CardActions>
                <Button size="small">Check for Updates</Button>
                <Button size="small" color="error">Restart Bot</Button>
              </CardActions>
            </Card>
          </Grid>
        </Grid>
      )}
      
      {activeTab === 1 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h2" gutterBottom>
                Protection Settings
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={protectionSettings.spamProtection}
                        onChange={(e) => setProtectionSettings({...protectionSettings, spamProtection: e.target.checked})}
                      />
                    }
                    label="Spam Protection"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={protectionSettings.floodProtection}
                        onChange={(e) => setProtectionSettings({...protectionSettings, floodProtection: e.target.checked})}
                      />
                    }
                    label="Flood Protection"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={protectionSettings.urlFilter}
                        onChange={(e) => setProtectionSettings({...protectionSettings, urlFilter: e.target.checked})}
                      />
                    }
                    label="URL Filter"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={protectionSettings.nsfwFilter}
                        onChange={(e) => setProtectionSettings({...protectionSettings, nsfwFilter: e.target.checked})}
                      />
                    }
                    label="NSFW Filter"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={protectionSettings.restrictNewUsers}
                        onChange={(e) => setProtectionSettings({...protectionSettings, restrictNewUsers: e.target.checked})}
                      />
                    }
                    label="Restrict New Users"
                  />
                </Grid>
              </Grid>
              
              <Divider sx={{ my: 3 }} />
              
              <Typography variant="h3" gutterBottom>
                Flood Protection Settings
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Message Limit"
                    type="number"
                    defaultValue="5"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Time Window (seconds)"
                    type="number"
                    defaultValue="30"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Action</InputLabel>
                    <Select defaultValue="mute">
                      <MenuItem value="mute">Mute</MenuItem>
                      <MenuItem value="kick">Kick</MenuItem>
                      <MenuItem value="ban">Ban</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </Paper>
          </Grid>
        </Grid>
      )}
      
      {activeTab === 2 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h2" gutterBottom>
                Welcome & Goodbye Messages
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={welcomeSettings.welcomeEnabled}
                        onChange={(e) => setWelcomeSettings({...welcomeSettings, welcomeEnabled: e.target.checked})}
                      />
                    }
                    label="Enable Welcome Messages"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    multiline
                    rows={4}
                    label="Welcome Message"
                    value={welcomeSettings.welcomeMessage}
                    onChange={(e) => setWelcomeSettings({...welcomeSettings, welcomeMessage: e.target.value})}
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={welcomeSettings.goodbyeEnabled}
                        onChange={(e) => setWelcomeSettings({...welcomeSettings, goodbyeEnabled: e.target.checked})}
                      />
                    }
                    label="Enable Goodbye Messages"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    multiline
                    rows={4}
                    label="Goodbye Message"
                    value={welcomeSettings.goodbyeMessage}
                    onChange={(e) => setWelcomeSettings({...welcomeSettings, goodbyeMessage: e.target.value})}
                  />
                </Grid>
              </Grid>
            </Paper>
          </Grid>
        </Grid>
      )}
      
      {activeTab === 3 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h2" gutterBottom>
                AI Settings
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={aiSettings.aiEnabled}
                        onChange={(e) => setAiSettings({...aiSettings, aiEnabled: e.target.checked})}
                      />
                    }
                    label="Enable AI Features"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>AI Model</InputLabel>
                    <Select
                      value={aiSettings.aiModel}
                      onChange={(e) => setAiSettings({...aiSettings, aiModel: e.target.value})}
                    >
                      <MenuItem value="Dhanishtha-2.0-preview">Dhanishtha 2.0 Preview</MenuItem>
                      <MenuItem value="Dhanishtha-2.0-mini">Dhanishtha 2.0 Mini</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Temperature"
                    type="number"
                    InputProps={{ inputProps: { min: 0, max: 1, step: 0.1 } }}
                    value={aiSettings.aiTemperature}
                    onChange={(e) => setAiSettings({...aiSettings, aiTemperature: parseFloat(e.target.value)})}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Max Tokens"
                    type="number"
                    InputProps={{ inputProps: { min: 100, max: 2000, step: 100 } }}
                    value={aiSettings.aiMaxTokens}
                    onChange={(e) => setAiSettings({...aiSettings, aiMaxTokens: parseInt(e.target.value)})}
                  />
                </Grid>
              </Grid>
            </Paper>
          </Grid>
        </Grid>
      )}
      
      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
        <Button variant="contained" onClick={handleSaveSettings}>
          Save Settings
        </Button>
      </Box>
    </Box>
  );
}