"""
🛠️ Utility Handler Module
--------------------------
Comprehensive utility commands in Rose-bot style.
"""

import asyncio
import html
import logging
import time
from datetime import datetime
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ChatMember
from telegram.constants import ParseMode
from telegram.ext import ContextTypes, CommandHandler, CallbackQueryHandler
from telegram.error import TelegramError

logger = logging.getLogger(__name__)

class UtilityHandler:
    def __init__(self, db=None):
        self.db = db

    async def _delete_message_later(self, context: ContextTypes.DEFAULT_TYPE, chat_id: int, message_id: int, delay: int = 30) -> None:
        """Delete a message after a specified delay in seconds."""
        await asyncio.sleep(delay)
        try:
            await context.bot.delete_message(chat_id=chat_id, message_id=message_id)
        except Exception as e:
            logger.debug(f"Failed to delete message: {e}")

    async def _send_group_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE, text: str, parse_mode: str = ParseMode.HTML, reply_markup = None) -> None:
        """Helper function to send messages in groups with auto-delete"""
        if not update.effective_message:
            return

        is_private = update.effective_chat.type == "private"
        
        # Send message
        message = await update.effective_message.reply_text(
            text,
            parse_mode=parse_mode,
            reply_markup=reply_markup,
            disable_web_page_preview=True
        )
        
        # Auto delete in groups after 30 seconds
        if not is_private:
            asyncio.create_task(self._delete_message_later(
                context, 
                message.chat_id, 
                message.message_id,
                delay=30
            ))
        
        # Delete the command message in groups
        if not is_private and update.effective_message.text and update.effective_message.text.startswith('/'):
            asyncio.create_task(self._delete_message_later(
                context, 
                update.effective_message.chat_id,
                update.effective_message.message_id,
                delay=30
            ))
        
        return message

    async def utility_commands(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Display comprehensive utility commands panel"""
        if not update.effective_message:
            return

        keyboard = [
            [
                InlineKeyboardButton("📊 Information", callback_data="utility_info"),
                InlineKeyboardButton("🔧 Tools", callback_data="utility_tools")
            ],
            [
                InlineKeyboardButton("💰 Finance", callback_data="utility_finance"),
                InlineKeyboardButton("📚 Knowledge", callback_data="utility_knowledge")
            ],
            [
                InlineKeyboardButton("🌐 Web Tools", callback_data="utility_web"),
                InlineKeyboardButton("📱 Generators", callback_data="utility_generators")
            ],
            [InlineKeyboardButton("◀️ Back to Help", callback_data="start_help")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        utility_text = (
            "╭─「 <b>🛠️ Utility Command Center</b> 」\n"
            "│\n"
            "│ 🎯 <b>Your daily-use toolkit!</b>\n"
            "│ Professional utilities for productivity\n"
            "│\n"
            "├─「 <b>🚀 Quick Access</b> 」\n"
            "│ 📊 <b>Information</b> - IDs, stats, system info\n"
            "│ 🔧 <b>Tools</b> - Calculators, converters, timers\n"
            "│ 💰 <b>Finance</b> - Crypto, stocks, currency\n"
            "│ 📚 <b>Knowledge</b> - Wikipedia, dictionary, news\n"
            "│ 🌐 <b>Web Tools</b> - URL shortener, web search\n"
            "│ 📱 <b>Generators</b> - QR codes, passwords, more\n"
            "│\n"
            "├─「 <b>💡 Popular Commands</b> 」\n"
            "│ • <code>/id</code> - Get user/chat information\n"
            "│ • <code>/ping</code> - Check bot performance\n"
            "│ • <code>/calc</code> - Quick calculations\n"
            "│ • <code>/qr</code> - Generate QR codes\n"
            "│ • <code>/weather</code> - Weather information\n"
            "│\n"
            "├─「 <b>📊 System Status</b> 」\n"
            f"│ 💭 Chat ID: <code>{update.effective_chat.id}</code>\n"
            f"│ 👤 User ID: <code>{update.effective_user.id}</code>\n"
            "│ 🟢 All utilities: <b>Online</b>\n"
            "│ ⚡ Response: <b>Optimized</b>\n"
            "│\n"
            "╰─「 <i>Select a category to explore!</i> 」"
        )

        if update.effective_chat.type != "private":
            utility_text += f"\n\n<i>⏰ Auto-deletes in 30 seconds</i>"

        await self._send_group_message(
            update,
            context,
            utility_text,
            ParseMode.HTML,
            reply_markup
        )

    async def handle_utility_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle utility command callbacks"""
        query = update.callback_query
        await query.answer()
        
        action = query.data.replace("utility_", "")
        
        if action == "info":
            await self._show_info_utilities(query, context)
        elif action == "tools":
            await self._show_tool_utilities(query, context)
        elif action == "finance":
            await self._show_finance_utilities(query, context)
        elif action == "knowledge":
            await self._show_knowledge_utilities(query, context)
        elif action == "web":
            await self._show_web_utilities(query, context)
        elif action == "generators":
            await self._show_generator_utilities(query, context)

    async def _show_info_utilities(self, query, context):
        """Show information utility commands"""
        keyboard = [
            [InlineKeyboardButton("◀ Back to Utilities", callback_data="utility_back")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        text = (
            "╭─「 <b>📊 Information Utilities</b> 」\n"
            "│\n"
            "├─「 <b>👤 User Information</b> 」\n"
            "│ • <code>/id [user]</code> - Get user/chat IDs\n"
            "│ • <code>/info [user]</code> - Detailed user info\n"
            "│ • <code>/whois [user]</code> - User profile lookup\n"
            "│ • <code>/avatar [user]</code> - Get profile picture\n"
            "│\n"
            "├─「 <b>💬 Chat Information</b> 」\n"
            "│ • <code>/chatinfo</code> - Current chat details\n"
            "│ • <code>/admins</code> - List group admins\n"
            "│ • <code>/stats</code> - Group statistics\n"
            "│ • <code>/members</code> - Member count & info\n"
            "│\n"
            "├─「 <b>🤖 System Information</b> 」\n"
            "│ • <code>/ping</code> - Bot response time & status\n"
            "│ • <code>/uptime</code> - How long bot is running\n"
            "│ • <code>/version</code> - Bot version information\n"
            "│ • <code>/server</code> - Server performance stats\n"
            "│\n"
            "├─「 <b>📈 Activity Tracking</b> 」\n"
            "│ • <code>/activity [user]</code> - User activity stats\n"
            "│ • <code>/top</code> - Most active users\n"
            "│ • <code>/analytics</code> - Group analytics\n"
            "│\n"
            "╰─「 <i>All info at your fingertips!</i> 」"
        )
        
        await query.edit_message_text(text, parse_mode=ParseMode.HTML, reply_markup=reply_markup)

    async def _show_tool_utilities(self, query, context):
        """Show tool utility commands"""
        keyboard = [
            [InlineKeyboardButton("◀ Back to Utilities", callback_data="utility_back")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        text = (
            "╭─「 <b>🔧 Tool Utilities</b> 」\n"
            "│\n"
            "├─「 <b>🧮 Calculators</b> 」\n"
            "│ • <code>/calc [expression]</code> - Basic calculator\n"
            "│ • <code>/convert [unit]</code> - Unit converter\n"
            "│ • <code>/percentage [num]%</code> - Percentage calc\n"
            "│ • <code>/tip [amount] [%]</code> - Tip calculator\n"
            "│\n"
            "├─「 <b>⏰ Time & Date</b> 」\n"
            "│ • <code>/time [timezone]</code> - World time\n"
            "│ • <code>/date</code> - Current date info\n"
            "│ • <code>/countdown [date]</code> - Event countdown\n"
            "│ • <code>/timer [minutes]</code> - Set timer\n"
            "│\n"
            "├─「 <b>🌤️ Weather & Location</b> 」\n"
            "│ • <code>/weather [city]</code> - Weather forecast\n"
            "│ • <code>/forecast [city]</code> - 7-day forecast\n"
            "│ • <code>/timezone [city]</code> - City timezone\n"
            "│ • <code>/sunrise [city]</code> - Sunrise/sunset times\n"
            "│\n"
            "├─「 <b>🔧 Text Tools</b> 」\n"
            "│ • <code>/encode [text]</code> - Base64 encode\n"
            "│ • <code>/decode [text]</code> - Base64 decode\n"
            "│ • <code>/hash [text]</code> - Generate hash\n"
            "│ • <code>/shorten [url]</code> - URL shortener\n"
            "│\n"
            "╰─「 <i>Tools for every need!</i> 」"
        )
        
        await query.edit_message_text(text, parse_mode=ParseMode.HTML, reply_markup=reply_markup)

    async def _show_finance_utilities(self, query, context):
        """Show finance utility commands"""
        keyboard = [
            [InlineKeyboardButton("◀ Back to Utilities", callback_data="utility_back")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        text = (
            "╭─「 <b>💰 Finance Utilities</b> 」\n"
            "│\n"
            "├─「 <b>₿ Cryptocurrency</b> 」\n"
            "│ • <code>/crypto [symbol]</code> - Crypto prices\n"
            "│ • <code>/btc</code> - Bitcoin price\n"
            "│ • <code>/eth</code> - Ethereum price\n"
            "│ • <code>/portfolio [coins]</code> - Track portfolio\n"
            "│\n"
            "├─「 <b>📈 Stock Market</b> 」\n"
            "│ • <code>/stock [symbol]</code> - Stock prices\n"
            "│ • <code>/markets</code> - Market overview\n"
            "│ • <code>/gainers</code> - Top gainers today\n"
            "│ • <code>/losers</code> - Top losers today\n"
            "│\n"
            "├─「 <b>💱 Currency Exchange</b> 」\n"
            "│ • <code>/exchange [from] [to]</code> - Exchange rates\n"
            "│ • <code>/convert [amount] [from] [to]</code> - Convert currency\n"
            "│ • <code>/rates</code> - Popular exchange rates\n"
            "│\n"
            "├─「 <b>🏦 Financial Tools</b> 」\n"
            "│ • <code>/mortgage [amount]</code> - Mortgage calculator\n"
            "│ • <code>/loan [amount] [rate]</code> - Loan calculator\n"
            "│ • <code>/compound [amount]</code> - Compound interest\n"
            "│ • <code>/investment [amount]</code> - ROI calculator\n"
            "│\n"
            "╰─「 <i>Stay on top of your finances!</i> 」"
        )
        
        await query.edit_message_text(text, parse_mode=ParseMode.HTML, reply_markup=reply_markup)

    async def _show_knowledge_utilities(self, query, context):
        """Show knowledge utility commands"""
        keyboard = [
            [InlineKeyboardButton("◀ Back to Utilities", callback_data="utility_back")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        text = (
            "╭─「 <b>📚 Knowledge Utilities</b> 」\n"
            "│\n"
            "├─「 <b>🔍 Search & Lookup</b> 」\n"
            "│ • <code>/wiki [query]</code> - Wikipedia search\n"
            "│ • <code>/search [query]</code> - Web search\n"
            "│ • <code>/define [word]</code> - Dictionary lookup\n"
            "│ • <code>/synonym [word]</code> - Find synonyms\n"
            "│\n"
            "├─「 <b>📰 News & Updates</b> 」\n"
            "│ • <code>/news</code> - Latest headlines\n"
            "│ • <code>/tech</code> - Tech news\n"
            "│ • <code>/sports</code> - Sports updates\n"
            "│ • <code>/science</code> - Science news\n"
            "│\n"
            "├─「 <b>🎬 Entertainment Info</b> 」\n"
            "│ • <code>/movie [title]</code> - Movie information\n"
            "│ • <code>/series [title]</code> - TV series info\n"
            "│ • <code>/book [title]</code> - Book details\n"
            "│ • <code>/recipe [dish]</code> - Cooking recipes\n"
            "│\n"
            "├─「 <b>🧠 Learning</b> 」\n"
            "│ • <code>/fact</code> - Random facts\n"
            "│ • <code>/quote</code> - Inspirational quotes\n"
            "│ • <code>/word</code> - Word of the day\n"
            "│ • <code>/history</code> - This day in history\n"
            "│\n"
            "╰─「 <i>Knowledge is power!</i> 」"
        )
        
        await query.edit_message_text(text, parse_mode=ParseMode.HTML, reply_markup=reply_markup)

    async def _show_web_utilities(self, query, context):
        """Show web utility commands"""
        keyboard = [
            [InlineKeyboardButton("◀ Back to Utilities", callback_data="utility_back")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        text = (
            "╭─「 <b>🌐 Web Utilities</b> 」\n"
            "│\n"
            "├─「 <b>🔗 URL Tools</b> 」\n"
            "│ • <code>/shorten [url]</code> - Shorten long URLs\n"
            "│ • <code>/expand [shorturl]</code> - Expand short URLs\n"
            "│ • <code>/qr [url]</code> - URL to QR code\n"
            "│ • <code>/preview [url]</code> - URL preview\n"
            "│\n"
            "├─「 <b>🌍 Website Analysis</b> 」\n"
            "│ • <code>/whois [domain]</code> - Domain information\n"
            "│ • <code>/ping [domain]</code> - Website ping test\n"
            "│ • <code>/ssl [domain]</code> - SSL certificate info\n"
            "│ • <code>/pagerank [url]</code> - SEO analysis\n"
            "│\n"
            "├─「 <b>📊 Web Search</b> 」\n"
            "│ • <code>/google [query]</code> - Google search\n"
            "│ • <code>/images [query]</code> - Image search\n"
            "│ • <code>/videos [query]</code> - Video search\n"
            "│ • <code>/scholar [query]</code> - Academic search\n"
            "│\n"
            "├─「 <b>🔧 Web Tools</b> 」\n"
            "│ • <code>/ip [address]</code> - IP information\n"
            "│ • <code>/dns [domain]</code> - DNS lookup\n"
            "│ • <code>/robots [domain]</code> - robots.txt check\n"
            "│ • <code>/sitemap [domain]</code> - Sitemap finder\n"
            "│\n"
            "╰─「 <i>Web mastery at your fingertips!</i> 」"
        )
        
        await query.edit_message_text(text, parse_mode=ParseMode.HTML, reply_markup=reply_markup)

    async def _show_generator_utilities(self, query, context):
        """Show generator utility commands"""
        keyboard = [
            [InlineKeyboardButton("◀ Back to Utilities", callback_data="utility_back")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        text = (
            "╭─「 <b>📱 Generator Utilities</b> 」\n"
            "│\n"
            "├─「 <b>📱 QR Codes</b> 」\n"
            "│ • <code>/qr [text]</code> - Generate QR code\n"
            "│ • <code>/qrwifi [ssid] [pass]</code> - WiFi QR code\n"
            "│ • <code>/qrvcard [details]</code> - Contact QR code\n"
            "│ • <code>/qrread [image]</code> - Read QR code\n"
            "│\n"
            "├─「 <b>🔐 Security</b> 」\n"
            "│ • <code>/password [length]</code> - Generate password\n"
            "│ • <code>/passphrase</code> - Generate passphrase\n"
            "│ • <code>/uuid</code> - Generate UUID\n"
            "│ • <code>/hash [text] [type]</code> - Hash generator\n"
            "│\n"
            "├─「 <b>🎲 Random Generators</b> 」\n"
            "│ • <code>/random [min] [max]</code> - Random number\n"
            "│ • <code>/shuffle [items]</code> - Shuffle list\n"
            "│ • <code>/pick [options]</code> - Random choice\n"
            "│ • <code>/username</code> - Username generator\n"
            "│\n"
            "├─「 <b>🎨 Creative</b> 」\n"
            "│ • <code>/color</code> - Random color\n"
            "│ • <code>/gradient [colors]</code> - Color gradient\n"
            "│ • <code>/lorem [words]</code> - Lorem ipsum text\n"
            "│ • <code>/ascii [text]</code> - ASCII art\n"
            "│\n"
            "╰─「 <i>Generate anything you need!</i> 」"
        )
        
        await query.edit_message_text(text, parse_mode=ParseMode.HTML, reply_markup=reply_markup)

    def register_handlers(self, application):
        """Register all utility command handlers"""
        application.add_handler(CommandHandler("utility", self.utility_commands))
        application.add_handler(CommandHandler("utils", self.utility_commands))
        
        # Add callback handlers
        application.add_handler(CallbackQueryHandler(self.handle_utility_callback, pattern=r"^utility_"))