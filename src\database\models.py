from sqlalchemy import create_engine, Column, Integer, String, DateTime, Text, ForeignKey, Boolean, JSON, Table
from sqlalchemy.orm import declarative_base, sessionmaker, relationship
from datetime import datetime

Base = declarative_base()

class User(Base):
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    telegram_id = Column(Integer, unique=True, nullable=False)
    username = Column(String(255), nullable=True)
    first_name = Column(String(255), nullable=True)
    last_name = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True)
    last_active = Column(DateTime, default=datetime.utcnow)
    preferences = Column(JSON, default={})
    created_at = Column(DateTime, default=datetime.utcnow)
    messages = relationship("Message", back_populates="user", cascade="all, delete-orphan")
    commands = relationship("CommandUsage", back_populates="user", cascade="all, delete-orphan")
    warnings_received = relationship("UserWarning", foreign_keys="UserWarning.user_id", back_populates="user", cascade="all, delete-orphan")
    warnings_given = relationship("UserWarning", foreign_keys="UserWarning.warned_by", back_populates="admin", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<User(id={self.id}, telegram_id={self.telegram_id}, username='{self.username}')>"

class Server(Base):
    __tablename__ = 'servers'
    
    id = Column(Integer, primary_key=True)
    telegram_id = Column(Integer, unique=True, nullable=False)
    title = Column(String(255), nullable=True)
    description = Column(Text, nullable=True)
    member_count = Column(Integer, default=0)
    is_active = Column(Boolean, default=True)
    joined_at = Column(DateTime, default=datetime.utcnow)
    settings = Column(JSON, default={})
    messages = relationship("Message", back_populates="server", cascade="all, delete-orphan")
    server_settings = relationship("ServerSettings", back_populates="server", uselist=False, cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Server(id={self.id}, telegram_id={self.telegram_id}, title='{self.title}')>"

class ServerSettings(Base):
    __tablename__ = 'server_settings'
    
    id = Column(Integer, primary_key=True)
    server_id = Column(Integer, ForeignKey('servers.id', ondelete="CASCADE"), nullable=False)
    
    # Protection settings
    protection_enabled = Column(Boolean, default=True)
    link_filter_enabled = Column(Boolean, default=False)
    media_filter_enabled = Column(Boolean, default=False)
    nsfw_filter_enabled = Column(Boolean, default=False)
    restrict_new_users = Column(Boolean, default=False)
    
    # Welcome settings
    welcome_enabled = Column(Boolean, default=True)
    welcome_message = Column(Text, nullable=True)
    goodbye_enabled = Column(Boolean, default=True)
    goodbye_message = Column(Text, nullable=True)
    
    # Rules settings
    rules_text = Column(Text, nullable=True)
    
    # Anti-flood settings
    antiflood_enabled = Column(Boolean, default=True)
    antiflood_limit = Column(Integer, default=5)
    antiflood_time = Column(Integer, default=3)
    antiflood_action = Column(String(50), default="mute")
    
    # Logging settings
    logging_enabled = Column(Boolean, default=False)
    log_channel_id = Column(Integer, nullable=True)
    
    # Last updated
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    server = relationship("Server", back_populates="server_settings")
    
    def __repr__(self):
        return f"<ServerSettings(id={self.id}, server_id={self.server_id})>"

class ServerMember(Base):
    __tablename__ = 'server_members'
    
    id = Column(Integer, primary_key=True)
    server_id = Column(Integer, ForeignKey('servers.id', ondelete="CASCADE"), nullable=False)
    user_id = Column(Integer, ForeignKey('users.id', ondelete="CASCADE"), nullable=False)
    joined_at = Column(DateTime, default=datetime.utcnow)
    is_admin = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    last_active = Column(DateTime, default=datetime.utcnow)
    message_count = Column(Integer, default=0)
    server = relationship("Server")
    user = relationship("User")
    
    def __repr__(self):
        return f"<ServerMember(id={self.id}, server_id={self.server_id}, user_id={self.user_id})>"

class Message(Base):
    __tablename__ = 'messages'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id', ondelete="CASCADE"), nullable=False)
    chat_id = Column(Integer, ForeignKey('servers.telegram_id', ondelete="CASCADE"), nullable=False)
    message_text = Column(Text, nullable=True)
    message_type = Column(String(50), default="text")  # text, photo, video, audio, etc.
    has_media = Column(Boolean, default=False)
    media_type = Column(String(50), nullable=True)
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False)
    user = relationship("User", back_populates="messages")
    server = relationship("Server", back_populates="messages")

    def __repr__(self):
        return f"<Message(id={self.id}, chat_id={self.chat_id}, user_id={self.user_id}, timestamp={self.timestamp})>"

class CommandUsage(Base):
    __tablename__ = 'command_usage'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id', ondelete="CASCADE"), nullable=False)
    server_id = Column(Integer, ForeignKey('servers.id', ondelete="CASCADE"), nullable=True)
    command = Column(String(100), nullable=False)
    arguments = Column(Text, nullable=True)
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False)
    success = Column(Boolean, default=True)
    user = relationship("User", back_populates="commands")
    
    def __repr__(self):
        return f"<CommandUsage(id={self.id}, command='{self.command}', user_id={self.user_id})>"

class UserWarning(Base):
    __tablename__ = 'user_warnings'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id', ondelete="CASCADE"), nullable=False)
    server_id = Column(Integer, ForeignKey('servers.id', ondelete="CASCADE"), nullable=False)
    warned_by = Column(Integer, ForeignKey('users.id', ondelete="SET NULL"), nullable=True)
    reason = Column(Text, nullable=True)
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False)
    user = relationship("User", back_populates="warnings_received", foreign_keys=[user_id])
    admin = relationship("User", back_populates="warnings_given", foreign_keys=[warned_by])
    
    def __repr__(self):
        return f"<UserWarning(id={self.id}, user_id={self.user_id}, server_id={self.server_id})>"

class ChatSummary(Base):
    __tablename__ = 'chat_summaries'
    
    id = Column(Integer, primary_key=True)
    chat_id = Column(Integer, ForeignKey('servers.telegram_id', ondelete="CASCADE"), nullable=False)
    summary_text = Column(Text, nullable=False)
    start_time = Column(DateTime, nullable=False)
    end_time = Column(DateTime, nullable=False)
    summary_type = Column(String(50), nullable=False)  # e.g. 'daily', 'user', 'ai_commentary'
    server = relationship("Server")

    def __repr__(self):
        return f"<ChatSummary(id={self.id}, chat_id={self.chat_id}, summary_type='{self.summary_type}')>"

class MusicPlayback(Base):
    __tablename__ = 'music_playback'
    
    id = Column(Integer, primary_key=True)
    server_id = Column(Integer, ForeignKey('servers.id', ondelete="CASCADE"), nullable=False)
    user_id = Column(Integer, ForeignKey('users.id', ondelete="CASCADE"), nullable=False)
    song_title = Column(String(255), nullable=True)
    artist = Column(String(255), nullable=True)
    url = Column(String(255), nullable=True)
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False)
    server = relationship("Server")
    user = relationship("User")
    
    def __repr__(self):
        return f"<MusicPlayback(id={self.id}, server_id={self.server_id}, song_title='{self.song_title}')>"

def init_db(db_url='sqlite:///bot_data.db'):
    engine = create_engine(db_url, echo=False)
    Base.metadata.create_all(engine)
    Session = sessionmaker(bind=engine)
    return Session()
      
if __name__ == "__main__":
    # Quick initialization test
    session = init_db()
    print("Database initialized successfully.")
    session.close()
