"""
🎉 Greeting Handler Module
----------------------
Handles AI-powered greetings, birthday wishes, and daily messages.
Powered by NexusAI
"""

import asyncio
import logging
import re
from datetime import datetime, time
import pytz
from pathlib import Path
from telegram import Update
from telegram.constants import ParseMode
from telegram.ext import ContextTypes, filters
from ..api_services.pollinations_ai import PollinationsAI
from ..database.db import Database

logger = logging.getLogger(__name__)

class GreetingHandler:
    def __init__(self):
        # Set the model to use for AI responses
        self.model = "openai-large"  # Using Llama 3.3 70B from Pollinations AI

        # Initialize database
        self.db = Database()

        # Birthday keywords for detection
        self.birthday_keywords = [
            r'happy\s+birthday',
            r'happy\s+bday',
            r'hbd',
            r'birthday\s+wishes',
            r'जन्मदिन\s+मुबारक',  # Hindi
            r'जन्मदिन\s+की\s+बधाई',
            r'बर्थडे',
            r'जनमदिन',
            r'janamdin\s+mubarak',  # Hinglish
            r'happy\s+wala\s+birthday',
            r'hpy\s+bday',
            r'many\s+happy\s+returns',
            r'birthday\s+boy',
            r'birthday\s+girl'
        ]
        # Compile regex patterns
        self.birthday_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.birthday_keywords]

    async def has_been_wished_today(self, user_id: int, username: str, chat_id: int) -> bool:
        """Check if user has already been wished today."""
        return await self.db.has_been_wished_today(user_id, username, chat_id)

    async def record_birthday_wish(self, user_id: int, username: str, chat_id: int):
        """Record that we've wished someone a happy birthday."""
        await self.db.record_birthday_wish(user_id, username, chat_id)

    async def detect_birthday_mention(self, text: str) -> bool:
        """Detect if a message contains birthday wishes."""
        if not text:
            return False

        for pattern in self.birthday_patterns:
            if pattern.search(text):
                return True

        return False

    async def get_user_id_from_username(self, context: ContextTypes.DEFAULT_TYPE, chat_id: int, username: str) -> tuple:
        """Get user ID from username by checking chat members."""
        try:
            # Remove @ if present
            clean_username = username.replace('@', '')

            # Try to get chat member info
            try:
                chat_member = await context.bot.get_chat_member(chat_id, clean_username)
                if chat_member and chat_member.user:
                    return chat_member.user.id, chat_member.user.first_name or clean_username
            except Exception as e:
                logger.debug(f"Could not get chat member directly: {e}")

            # If direct lookup fails, try getting chat administrators
            try:
                admins = await context.bot.get_chat_administrators(chat_id)
                for admin in admins:
                    if admin.user.username and admin.user.username.lower() == clean_username.lower():
                        return admin.user.id, admin.user.first_name or clean_username
            except Exception as e:
                logger.debug(f"Could not get chat admins: {e}")

            # If we still don't have an ID, return None but keep the username
            return None, clean_username

        except Exception as e:
            logger.error(f"Error resolving username to ID: {e}")
            return None, username

    async def handle_birthday(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle birthday mentions in messages."""
        try:
            message = update.effective_message
            if not message or not message.text:
                return

            if not await self.detect_birthday_mention(message.text):
                return

            celebrant_name = None
            celebrant_id = None
            celebrant_username = None
            chat_id = message.chat_id

            # Get celebrant info from reply or mention
            if message.reply_to_message and message.reply_to_message.from_user:
                celebrant = message.reply_to_message.from_user
                if celebrant:
                    celebrant_name = celebrant.first_name
                    celebrant_id = celebrant.id
                    celebrant_username = celebrant.username
            else:
                # Try to find username in the message
                matches = re.findall(r'@(\w+)', message.text)
                if matches:
                    celebrant_username = matches[0]
                    # Try to get user info from username
                    celebrant_id, celebrant_name = await self.get_user_id_from_username(
                        context,
                        chat_id,
                        celebrant_username
                    )

            if not celebrant_name and not celebrant_username:
                return

            # Check if we've already wished this person today
            if await self.has_been_wished_today(celebrant_id, celebrant_username, chat_id):
                logger.info(f"Already wished {celebrant_name or celebrant_username} today in chat {chat_id}")
                return

            sender_name = message.from_user.first_name if message.from_user else "Someone"

            prompt = self._build_birthday_prompt(celebrant_name or celebrant_username, sender_name)
            ai_wish = await self._generate_ai_response(prompt)

            if ai_wish:
                # Add mention if we have username
                if celebrant_username:
                    ai_wish = f"@{celebrant_username} {ai_wish}"

                await message.reply_text(
                    ai_wish,
                    parse_mode=ParseMode.HTML
                )

                # Record the birthday wish
                await self.record_birthday_wish(celebrant_id, celebrant_username, chat_id)

        except Exception as e:
            logger.error(f"❌ Error handling birthday wish: {e}")

    async def send_morning_greeting(self, context: ContextTypes.DEFAULT_TYPE, chat_id: int) -> None:
        """Send AI-generated morning greeting to a chat."""
        prompt = self._build_morning_prompt()
        greeting = await self._generate_ai_response(prompt)

        if greeting:
            try:
                sent_message = await context.bot.send_message(
                    chat_id=chat_id,
                    text=greeting,
                    parse_mode=ParseMode.HTML
                )
                if sent_message:
                    asyncio.create_task(self._delete_message_later(context, sent_message, 3600))  # Delete after 1 hour
            except Exception as e:
                logger.error(f"❌ Error sending morning greeting: {e}")
                # If HTML parsing fails, try sending without HTML
                try:
                    # Strip HTML tags as fallback
                    import re
                    plain_text = re.sub(r'<[^>]+>', '', greeting)
                    sent_message = await context.bot.send_message(
                        chat_id=chat_id,
                        text=plain_text
                    )
                    if sent_message:
                        asyncio.create_task(self._delete_message_later(context, sent_message, 3600))
                except Exception as e2:
                    logger.error(f"❌ Error sending plain text greeting: {e2}")

    async def schedule_morning_greeting(self, context: ContextTypes.DEFAULT_TYPE, chat_id: int) -> None:
        """Schedule daily morning greeting for 9:25 AM IST."""
        try:
            # Check if this chat already has a scheduled job
            job_name = f"morning_greeting_{chat_id}"
            existing_jobs = context.job_queue.get_jobs_by_name(job_name)

            if existing_jobs:
                logger.info(f"Morning greeting already scheduled for chat {chat_id}")
                return

            ist = pytz.timezone('Asia/Kolkata')
            utc = pytz.UTC
            target_time = time(hour=9, minute=25)

            ist_dt = datetime.now(ist).replace(
                hour=target_time.hour,
                minute=target_time.minute,
                second=0,
                microsecond=0
            )
            utc_time = ist_dt.astimezone(utc).time()

            context.job_queue.run_daily(
                callback=lambda ctx: self.send_morning_greeting(ctx, chat_id),
                time=utc_time,
                days=(0, 1, 2, 3, 4, 5, 6),
                chat_id=chat_id,
                name=job_name  # Add a unique name for the job
            )

            # Log the scheduling but don't send a message to the chat
            logger.info(f"✅ Daily morning greetings scheduled for chat {chat_id} at 9:25 AM IST")

        except Exception as e:
            logger.error(f"❌ Error scheduling morning greeting: {e}")
            # We can still send an error message if something goes wrong
            await context.bot.send_message(
                chat_id=chat_id,
                text="❌ Failed to schedule morning greetings. Please try again later.",
                parse_mode=ParseMode.HTML
            )

    async def _delete_message_later(self, context: ContextTypes.DEFAULT_TYPE, message, delay: int):
        """Delete a message after specified delay."""
        try:
            await asyncio.sleep(delay)
            await message.delete()
        except Exception as e:
            logger.error(f"❌ Error deleting message: {e}")

    def _build_birthday_prompt(self, celebrant_name: str, sender_name: str) -> str:
        """Build a prompt for generating birthday wishes."""
        return f"""
<system>
    <identity>
        <role>Elite Birthday Celebration AI</role>
        <version>3.0</version>
        <specialization>Crafting Personalized Birthday Messages</specialization>
        <core_traits>
            <trait>Highly Creative</trait>
            <trait>Emotionally Intelligent</trait>
            <trait>Culturally Aware</trait>
            <trait>Modern and Trendy</trait>
        </core_traits>
    </identity>

    <capabilities>
        <linguistic>
            <primary>English</primary>
            <style>Modern with trendy expressions</style>
            <adaptability>High</adaptability>
            <tone_range>
                <casual>Friendly and relaxed</casual>
                <trendy>Using current slang and expressions</trendy>
                <heartfelt>Genuine and warm</heartfelt>
            </tone_range>
        </linguistic>

        <emotional_intelligence>
            <empathy>High</empathy>
            <cultural_awareness>Advanced</cultural_awareness>
            <personality_adaptation>Dynamic</personality_adaptation>
        </emotional_intelligence>

        <creative_elements>
            <emoji_usage>Strategic and contextual</emoji_usage>
            <expression_style>Modern and engaging</expression_style>
            <message_structure>Dynamic and flowing</message_structure>
        </creative_elements>
    </capabilities>
</system>

<context>
    <event_details>
        <occasion>Birthday Celebration</occasion>
        <celebrant>
            <name>{celebrant_name}</name>
            <relationship_context>Friend/Group Member</relationship_context>
        </celebrant>
        <sender>
            <name>{sender_name}</name>
            <role>Well-wisher</role>
        </sender>
        <platform>Telegram Group Chat</platform>
    </event_details>
</context>

<output_requirements>
    <content_guidelines>
        <primary_elements>
            <element>Personalized greeting using celebrant's name</element>
            <element>Heartfelt good wishes</element>
            <element>Positive future reference</element>
        </primary_elements>

        <style_requirements>
            <length>2-3 impactful sentences</length>
            <emoji_count>2-3 contextually relevant emojis</emoji_count>
            <tone>Modern, friendly, and engaging</tone>
            <language_style>
                <mix>
                    <formal>20%</formal>
                    <casual>50%</casual>
                    <trendy>30%</trendy>
                </mix>
            </language_style>
        </style_requirements>

        <engagement_elements>
            <must_include>
                - Celebrant's name
                - Modern slang or trendy phrase
                - Positive energy
                - Relevant emojis
            </must_include>
            <must_avoid>
                - Generic wishes
                - Outdated expressions
                - Excessive formality
                - Overused phrases
            </must_avoid>
        </engagement_elements>
    </content_guidelines>

    <examples>
        <high_quality_example>
            <message>"Yooo {celebrant_name}! 🌟 Living your best life and absolutely crushing it on your special day! Keep that main character energy going strong, you're literally unstoppable! ✨🎉"</message>
            <analysis>
                - Uses modern slang ("crushing it", "main character energy")
                - Incorporates relevant emojis
                - Maintains personal touch
                - Expresses genuine enthusiasm
            </analysis>
        </high_quality_example>
        <high_quality_example>
            <message>"Hey {celebrant_name}! 🎈 Another trip around the sun and you're still serving nothing but wins! Stay iconic and keep that awesome vibe going! 💫✨"</message>
            <analysis>
                - Modern expression ("serving wins")
                - Positive and uplifting
                - Perfect emoji usage
                - Contemporary tone
            </analysis>
        </high_quality_example>
    </examples>
</output_requirements>

<instructions>
    Generate a birthday message that:
    1. Opens with an attention-grabbing greeting
    2. Includes a personalized wish or observation
    3. Closes with forward-looking positive energy
    4. Seamlessly incorporates 2-3 relevant emojis
    5. Uses at least one modern/trendy expression
    6. Maintains a natural, flowing conversation style

    Important: Generate only the final message without any XML tags or explanations.
</instructions>"""

    def _build_morning_prompt(self) -> list:
        """Build a detailed XML system prompt for morning greetings."""
        system_prompt = """
<morning_greeting_system>
    <identity>
        <role>Morning Motivation Assistant</role>
        <version>3.0</version>
        <personality>Energetic, Positive, Concise, Motivational</personality>
        <expertise>Crafting brief, impactful morning messages</expertise>
    </identity>

    <greeting_guidelines>
        <format>
            <structure>
                <opener>Brief, warm greeting with time-appropriate salutation (max 10 words)</opener>
                <body>Short motivational message (1-2 sentences max, under 20 words total)</body>
                <closer>Optional brief inspirational quote or actionable tip (under 15 words)</closer>
            </structure>
            <length>
                <total_words>Maximum 50 words total</total_words>
                <total_characters>Maximum 280 characters</total_characters>
            </length>
            <tone>Uplifting but not overwhelming</tone>
        </format>

        <content_rules>
            <rule>Be extremely concise - prefer shorter messages over longer ones</rule>
            <rule>Avoid clichés and generic motivation</rule>
            <rule>Include occasional emojis (1-3 max) for visual appeal</rule>
            <rule>Vary greeting styles to maintain freshness</rule>
            <rule>Avoid asking questions that require responses</rule>
            <rule>Never use ALL CAPS for entire sentences</rule>
            <rule>Focus on actionable, practical motivation</rule>
            <rule>Avoid overly complex vocabulary</rule>
        </content_rules>

        <style_variations>
            <variation>Mindfulness-focused</variation>
            <variation>Goal-oriented</variation>
            <variation>Gratitude-centered</variation>
            <variation>Nature-inspired</variation>
            <variation>Success-minded</variation>
            <variation>Energy-boosting</variation>
            <variation>Calm and centered</variation>
        </style_variations>

        <telegram_html_formatting>
            <allowed_tags>
                <tag name="b">Use for important words or phrases</tag>
                <tag name="i">Use sparingly for emphasis</tag>
                <tag name="u">Use rarely for special emphasis</tag>
                <tag name="code">Not recommended for morning messages</tag>
                <tag name="pre">Not recommended for morning messages</tag>
            </allowed_tags>
            <formatting_rules>
                <rule>Use <b>bold</b> for 1-3 key words maximum</rule>
                <rule>Use <i>italics</i> sparingly for 1-2 words maximum</rule>
                <rule>Never nest formatting tags</rule>
                <rule>Format should enhance readability, not distract</rule>
                <rule>Don't overuse formatting - less is more</rule>
            </formatting_rules>
            <examples>
                <example><![CDATA[Good morning! ☀️ Today is your chance to <b>shine</b>.]]></example>
                <example><![CDATA[Rise & shine! 🌅 <b>Today's</b> opportunities await.]]></example>
                <example><![CDATA[Morning! Make <i>one small</i> positive change today.]]></example>
            </examples>
        </telegram_html_formatting>
    </greeting_guidelines>

    <examples>
        <example>
            <greeting><![CDATA[Good morning! ☀️ Today brings <b>new opportunities</b>. Small steps lead to big changes.]]></greeting>
        </example>
        <example>
            <greeting><![CDATA[Rise and shine! 🌅 Your <b>potential</b> today is limitless.]]></greeting>
        </example>
        <example>
            <greeting><![CDATA[Morning! 🌞 <b>Focus</b> on what truly matters today.]]></greeting>
        </example>
        <example>
            <greeting><![CDATA[Hello, early birds! ✨ <b>Energy</b> and <b>intention</b> create results.]]></greeting>
        </example>
    </examples>

    <output_requirements>
        <format>Plain text with Telegram HTML tags only</format>
        <length>Keep under 280 characters total</length>
        <style>Concise, impactful, and easy to read at a glance</style>
        <must_include>
            <element>At least one emoji (maximum 3)</element>
            <element>At least one HTML formatting tag for emphasis</element>
            <element>A clear, positive message</element>
        </must_include>
        <must_avoid>
            <element>Questions</element>
            <element>Requests for user interaction</element>
            <element>Overly complex vocabulary</element>
            <element>Lengthy explanations</element>
            <element>Excessive formatting</element>
        </must_avoid>
    </output_requirements>
</morning_greeting_system>
"""
        user_prompt = "Generate a very concise, uplifting morning greeting message using Telegram HTML formatting. Keep it extremely brief (under 50 words) but impactful, with minimal formatting and 1-3 emojis maximum."

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

    async def _generate_ai_response(self, messages) -> str:
        """Generate AI response using Pollinations AI."""
        try:
            # Handle both string prompts and message lists
            if isinstance(messages, str):
                # Convert string prompt to message format
                messages = [{"role": "user", "content": messages}]
            
            response = await PollinationsAI.generate_text_async(
                model=self.model,
                messages=messages,
                temperature=0.7
            )
            return response
        except Exception as e:
            logger.error(f"Error generating AI response: {e}")
            return "Good morning! ☀️ Wishing you a <b>wonderful</b> day ahead."

if __name__ == "__main__":
    import asyncio

    async def test_birthday_detection():
        test = GreetingHandler()
        result = await test.detect_birthday_mention("happy birthday @user")
        print(f"Birthday mention detected: {result}")

    async def test_ai_response():
        test = GreetingHandler()
        prompt = test._build_birthday_prompt("John", "Alice")
        print("\nTesting with prompt:")
        print("-" * 50)
        print(prompt)
        print("-" * 50)

        print("\nGenerating AI response...")
        response = await test._generate_ai_response(prompt)
        print("\nAI Response:")
        print("-" * 50)
        print(response)
        print("-" * 50)

    asyncio.run(test_birthday_detection())
    asyncio.run(test_ai_response())
