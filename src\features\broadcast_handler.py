"""
📤 Push Handler Module
----------------------
Push content to specific chats or all groups
"""

import asyncio
import logging
from typing import List, Dict, Optional
from telegram import Update
from telegram.constants import ParseMode, ChatType
from telegram.error import TelegramError, Forbidden
from telegram.ext import ContextTypes, CommandHandler
from ..database.db import Database

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class PushHandler:
    def __init__(self, db: Database):
        self.db = db

    def register_handlers(self, application):
        """Register push and sync handlers."""
        # Push Commands
        application.add_handler(CommandHandler("bc", self.push_content))  # bc is now alias of push
        application.add_handler(CommandHandler("push", self.push_content))
        application.add_handler(<PERSON><PERSON>andler("syncchats", self.sync_chats))
        application.add_handler(<PERSON><PERSON><PERSON><PERSON>("verifysync", self.verify_sync))

    async def sync_chats(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Sync chats where bot is already added."""
        # Check if user is the bot owner by username
        bot_owner_username = context.bot_data.get("owner_username")
        user_username = update.effective_user.username if update.effective_user else None
        
        if not bot_owner_username or not user_username or user_username.lower() != bot_owner_username.lower():
            await update.message.reply_text("❌ Nice try, but you're not worthy! 😏")
            return

        status_msg = await update.message.reply_text("🔄 Starting optimized sync...")
        
        # Initialize counters
        synced = 0
        failed = 0
        admin_count = 0
        processed_chats = set()  # Track processed chats to avoid duplicates
        
        try:
            # Clear existing chats
            await self.db.clear_all_chats()
            
            # Update status
            await status_msg.edit_text("🔄 Cleared existing data, discovering chats...")
            
            # Get recent updates with smaller batch size to avoid pool exhaustion
            try:
                updates = await context.bot.get_updates(offset=-1, limit=50)  # Reduced from 100
                if not updates:
                    await status_msg.edit_text("❌ No recent updates found. Try sending a message in groups first.")
                    return
                
                await status_msg.edit_text(f"🔄 Processing {len(updates)} recent updates...")
                
                # Process updates with rate limiting
                for i, update_item in enumerate(updates):
                    try:
                        # Process different types of updates
                        message = None
                        if update_item.message:
                            message = update_item.message
                        elif update_item.edited_message:
                            message = update_item.edited_message
                        elif update_item.channel_post:
                            message = update_item.channel_post
                        elif update_item.edited_channel_post:
                            message = update_item.edited_channel_post
                        
                        if message and message.chat:
                            chat = message.chat
                            chat_id = chat.id
                            
                            # Skip if already processed or if it's a private chat
                            if chat_id in processed_chats or chat.type == 'private':
                                continue
                                
                            processed_chats.add(chat_id)
                            
                            # Only process groups and supergroups
                            if chat.type in ['group', 'supergroup']:
                                try:
                                    # Check bot's admin status with error handling
                                    is_admin = False
                                    try:
                                        member = await context.bot.get_chat_member(chat_id, context.bot.id)
                                        if member.status in ['administrator', 'creator']:
                                            is_admin = True
                                            admin_count += 1
                                    except Exception as admin_check_error:
                                        logger.debug(f"Could not check admin status for {chat_id}: {admin_check_error}")
                                        # Continue anyway, bot might still be a member
                                    
                                    # Add chat to database
                                    success = await self.db.add_chat(
                                        chat_id=chat_id,
                                        title=chat.title or f"Chat {chat_id}",
                                        chat_type=chat.type,
                                        is_admin=is_admin,
                                    )
                                    
                                    if success:
                                        synced += 1
                                        logger.info(f"Synced chat: {chat.title} ({chat_id}) - Admin: {is_admin}")
                                    else:
                                        failed += 1
                                        
                                except Exception as chat_error:
                                    logger.error(f"Error processing chat {chat_id}: {chat_error}")
                                    failed += 1
                        
                        # Rate limiting - longer delay to prevent pool exhaustion
                        if i % 10 == 0:  # Update status every 10 items
                            await status_msg.edit_text(
                                f"🔄 Processing updates... ({i+1}/{len(updates)})\n"
                                f"✅ Synced: {synced} | ❌ Failed: {failed}"
                            )
                        
                        # Increased delay to prevent connection pool issues
                        await asyncio.sleep(0.3)  # Increased from 0.1 to 0.3 seconds
                        
                    except Exception as update_error:
                        logger.error(f"Error processing update {i}: {update_error}")
                        failed += 1
                        continue
                
            except Exception as updates_error:
                logger.error(f"Error getting updates: {updates_error}")
                await status_msg.edit_text(
                    f"❌ Error getting updates: {str(updates_error)}\n\n"
                    f"This might be due to connection limits. Try again in a few minutes."
                )
                return

            # Final status update
            if synced > 0:
                await status_msg.edit_text(
                    f"✅ Sync completed successfully!\n\n"
                    f"📊 Results:\n"
                    f"• Total chats synced: {synced}\n"
                    f"• Failed attempts: {failed}\n"
                    f"• Admin rights in: {admin_count} chats\n\n"
                    f"💡 Use /bc to start broadcasting\n"
                    f"🔍 Use /verifysync to verify results"
                )
            else:
                await status_msg.edit_text(
                    f"⚠️ No chats were synced.\n\n"
                    f"📊 Results:\n"
                    f"• Total chats synced: {synced}\n"
                    f"• Failed attempts: {failed}\n\n"
                    f"💡 Tips:\n"
                    f"• Send messages in groups first\n"
                    f"• Make sure bot has proper permissions\n"
                    f"• Try again in a few minutes"
                )

        except Exception as e:
            logger.error(f"Sync operation failed: {e}")
            await status_msg.edit_text(
                f"❌ Sync failed: {str(e)}\n\n"
                f"📊 Partial results:\n"
                f"✅ Synced: {synced}\n"
                f"❌ Failed: {failed}\n"
                f"🔑 Admin rights in: {admin_count} chats\n\n"
                f"💡 This might be a temporary issue. Try again in a few minutes."
            )

    async def push_content(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """
        Push the exact content of a replied message to all groups or a specific chat.
        Only the bot owner can use this command in private chat.
        Usage:
        - Reply then send /push to broadcast to all groups
        - /push <chat_id> while replying to forward to one chat
        
        The bot will copy the exact content (text, image, video, sticker, etc.)
        and send it to the specified destination without any additional formatting.
        """
        # Ensure command is used in private chat
        if update.effective_chat.type != ChatType.PRIVATE:
            await update.message.reply_text("❌ This command can only be used in private messages.")
            return

        # Verify bot owner
        # Check if user is the bot owner by username
        bot_owner_username = context.bot_data.get("owner_username")
        user_username = update.effective_user.username
        
        if not bot_owner_username or not user_username or user_username.lower() != bot_owner_username.lower():
            await update.message.reply_text("❌ This command is only available to the bot owner.")
            return
        
        # Check if the command is replying to a message
        if not update.message.reply_to_message:
            await update.message.reply_text("❌ Please reply to a message you want to push.")
            return

        # Get the message to be pushed
        source_message = update.message.reply_to_message

        # Check if a specific chat_id was provided
        target_chat_id = None
        if context.args:
            try:
                target_chat_id = int(context.args[0])
            except ValueError:
                await update.message.reply_text("❌ Invalid chat ID format.")
                return

        status_message = await update.message.reply_text(
            "🔄 Initializing push operation..." if not target_chat_id else f"🔄 Pushing content to chat {target_chat_id}..."
        )

        async def send_content(chat_id: int) -> bool:
            """Helper function to send content to a specific chat"""
            try:
                if source_message.text and not source_message.caption:
                    await context.bot.send_message(
                        chat_id=chat_id,
                        text=source_message.text,
                        parse_mode=None
                    )
                elif source_message.photo:
                    photo = source_message.photo[-1]
                    await context.bot.send_photo(
                        chat_id=chat_id,
                        photo=photo.file_id,
                        caption=source_message.caption,
                        parse_mode=None
                    )
                elif source_message.video:
                    await context.bot.send_video(
                        chat_id=chat_id,
                        video=source_message.video.file_id,
                        caption=source_message.caption,
                        parse_mode=None
                    )
                elif source_message.audio:
                    await context.bot.send_audio(
                        chat_id=chat_id,
                        audio=source_message.audio.file_id,
                        caption=source_message.caption,
                        parse_mode=None
                    )
                elif source_message.document:
                    await context.bot.send_document(
                        chat_id=chat_id,
                        document=source_message.document.file_id,
                        caption=source_message.caption,
                        parse_mode=None
                    )
                elif source_message.sticker:
                    await context.bot.send_sticker(
                        chat_id=chat_id,
                        sticker=source_message.sticker.file_id
                    )
                elif source_message.animation:
                    await context.bot.send_animation(
                        chat_id=chat_id,
                        animation=source_message.animation.file_id,
                        caption=source_message.caption,
                        parse_mode=None
                    )
                elif source_message.voice:
                    await context.bot.send_voice(
                        chat_id=chat_id,
                        voice=source_message.voice.file_id,
                        caption=source_message.caption,
                        parse_mode=None
                    )
                elif source_message.video_note:
                    await context.bot.send_video_note(
                        chat_id=chat_id,
                        video_note=source_message.video_note.file_id
                    )
                else:
                    logger.warning(f"Unsupported message type for push to {chat_id}")
                    return False
                
                return True
                
            except Exception as e:
                logger.error(f"Failed to push content to chat {chat_id}: {str(e)}")
                return False

        if target_chat_id:
            # Push to specific chat
            try:
                # Verify chat exists and bot has access
                try:
                    chat = await context.bot.get_chat(target_chat_id)
                except TelegramError as te:
                    await status_message.edit_text(f"❌ Cannot access chat {target_chat_id}: {str(te)}")
                    return

                success = await send_content(target_chat_id)
                if success:
                    await status_message.edit_text(
                        f"✅ Successfully pushed content to chat:\n"
                        f"• Chat ID: {target_chat_id}\n"
                        f"• Title: {chat.title if hasattr(chat, 'title') else 'Private Chat'}"
                    )
                else:
                    await status_message.edit_text(f"❌ Failed to push content to chat {target_chat_id}")

            except Exception as e:
                await status_message.edit_text(f"❌ Error: {str(e)}")
                
        else:
            # Push to all groups
            groups = await self.db.get_all_groups()
            success_count = 0
            fail_count = 0
            seen_ids = set()

            for group in groups:
                chat_id = group["chat_id"]
                if chat_id in seen_ids:
                    continue
                seen_ids.add(chat_id)
                if await send_content(chat_id):
                    success_count += 1
                else:
                    fail_count += 1

            # Create and send a summary
            summary = (
                f"📊 *Push Summary*\n"
                f"✅ Successfully sent to: {success_count} chats\n"
                f"❌ Failed to send to: {fail_count} chats"
            )
            await status_message.edit_text(summary, parse_mode=ParseMode.MARKDOWN)

    async def verify_sync(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """
        Verify the synced chats and their status.
        Only the bot owner can use this command.
        """
        # Verify bot owner
        # Check if user is the bot owner by username
        bot_owner_username = context.bot_data.get("owner_username")
        user_username = update.effective_user.username
        
        if not bot_owner_username or not user_username or user_username.lower() != bot_owner_username.lower():
            await update.message.reply_text("❌ This command is only available to the bot owner.")
            return

        try:
            status_message = await update.message.reply_text("🔄 Verifying synced chats...")
            
            # Get all chats from the database
            all_chats = await self.db.get_all_chats()
            
            if not all_chats:
                await status_message.edit_text("❌ No chats found in the database. Use /syncchats to sync chats first.")
                return
            
            # Get the bot's ID
            bot_id = context.bot.id
            
            # Prepare the verification results
            verification_results = []
            valid_chats = 0
            invalid_chats = 0
            admin_chats = 0
            
            # Check each chat with improved rate limiting
            total_chats = len(all_chats)
            await status_message.edit_text(f"🔄 Verifying {total_chats} chats... This may take a moment.")
            
            for i, chat_id in enumerate(all_chats):
                try:
                    # Try to get chat info
                    chat_info = await context.bot.get_chat(chat_id)
                    
                    # Check if bot is admin with better error handling
                    is_admin = False
                    admin_status = "Member"
                    try:
                        chat_member = await context.bot.get_chat_member(chat_id, bot_id)
                        if chat_member.status in ['administrator', 'creator']:
                            is_admin = True
                            admin_chats += 1
                            admin_status = "Admin" if chat_member.status == 'administrator' else "Creator"
                    except Exception as admin_error:
                        logger.debug(f"Could not check admin status for {chat_id}: {admin_error}")
                        # Continue as member
                    
                    # Add to valid chats
                    valid_chats += 1
                    
                    # Add to verification results with better formatting
                    status_emoji = "👑" if admin_status == "Creator" else "🔑" if admin_status == "Admin" else "👤"
                    chat_title = chat_info.title[:30] + "..." if len(chat_info.title) > 30 else chat_info.title
                    verification_results.append(f"{status_emoji} {chat_title} ({chat_id})")
                    
                    # Update progress every 5 chats
                    if (i + 1) % 5 == 0 or (i + 1) == total_chats:
                        progress = f"({i + 1}/{total_chats})"
                        await status_message.edit_text(
                            f"🔄 Verifying chats... {progress}\n"
                            f"✅ Valid: {valid_chats} | ❌ Invalid: {invalid_chats}"
                        )
                    
                    # Increased delay to prevent connection pool issues
                    await asyncio.sleep(0.5)  # Increased from 0.1 to 0.5 seconds
                    
                except Exception as chat_error:
                    # Chat is invalid or bot is not a member
                    invalid_chats += 1
                    logger.debug(f"Chat {chat_id} verification failed: {chat_error}")
                    verification_results.append(f"❌ Chat {chat_id}: Invalid/No access")
                    
                    # Still add delay for failed chats
                    await asyncio.sleep(0.2)
            
            # Create and send the verification summary with better formatting
            if len(verification_results) > 20:  # Limit results to prevent message too long
                shown_results = verification_results[:15]
                hidden_count = len(verification_results) - 15
                results_text = "\n".join(shown_results) + f"\n... and {hidden_count} more chats"
            else:
                results_text = "\n".join(verification_results)
            
            summary = (
                f"✅ Verification completed!\n\n"
                f"📊 Summary:\n"
                f"• Total chats in DB: {len(all_chats)}\n"
                f"• ✅ Valid/Accessible: {valid_chats}\n"
                f"• ❌ Invalid/No access: {invalid_chats}\n"
                f"• 🔑 Admin rights: {admin_chats} chats\n\n"
                f"📋 Chat Status:\n{results_text}\n\n"
                f"💡 Use /syncchats to refresh the chat list"
            )
            
            # Split message if too long
            if len(summary) > 4000:
                # Send summary first
                summary_short = (
                    f"✅ Verification completed!\n\n"
                    f"📊 Summary:\n"
                    f"• Total chats in DB: {len(all_chats)}\n"
                    f"• ✅ Valid/Accessible: {valid_chats}\n"
                    f"• ❌ Invalid/No access: {invalid_chats}\n"
                    f"• 🔑 Admin rights: {admin_chats} chats\n\n"
                    f"💡 Use /syncchats to refresh the chat list"
                )
                await status_message.edit_text(summary_short)
                
                # Send detailed results in a separate message if not too many
                if len(verification_results) <= 30:
                    await update.message.reply_text(f"📋 Detailed Results:\n{results_text}")
            else:
                await status_message.edit_text(summary)
            
        except Exception as e:
            logger.error(f"Error verifying synced chats: {str(e)}")
            await update.message.reply_text(
                "❌ An error occurred while verifying synced chats. Please check the logs for details."
            )

def setup(application):
    """Setup push command handlers"""
    push_handler = PushHandler(Database())
    push_handler.register_handlers(application)
