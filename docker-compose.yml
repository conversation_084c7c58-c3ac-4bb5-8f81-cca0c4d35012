version: '3.8'

services:
  haibot:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: haibot_v1
    restart: unless-stopped
    environment:
      - PYTHONUNBUFFERED=1
    volumes:
      # Mount data directory to persist database and logs
      - ./data:/app/data
      - ./logs:/app/logs
      # Mount .env file if you have one
      - ./.env:/app/.env:ro
    networks:
      - haibot_network
    # Uncomment the following lines if your bot needs to expose a port
    # ports:
    #   - "8080:8080"

networks:
  haibot_network:
    driver: bridge
