from telegram import Update
from telegram.constants import ParseMode  # ParseMode is now in telegram.constants
from telegram.ext import ContextTypes
import logging

# Set up logging
logger = logging.getLogger(__name__)

async def user_stats_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Command to show bot usage statistics.
    Only available to the bot owner in private chat.
    """
    # Check if command is used in private chat
    if update.effective_chat.type != "private":
        await update.message.reply_text("❌ This command can only be used in private messages.")
        return
    
    # Check if user is the bot owner by username
    bot_owner_username = context.bot_data.get("owner_username")
    user_username = update.effective_user.username
    
    if not bot_owner_username or not user_username or user_username.lower() != bot_owner_username.lower():
        await update.message.reply_text("❌ This command is only available to the bot owner.")
        return
    
    # Get database instance from context
    db = context.bot_data.get("db")
    if not db:
        await update.message.reply_text("❌ Database connection not available.")
        return
    
    # Send typing action while processing
    await context.bot.send_chat_action(chat_id=update.effective_chat.id, action="typing")
    
    try:
        # Get comprehensive bot statistics
        stats = await db.get_bot_stats()
        
        # Format the statistics message
        stats_message = (
            f"📊 <b>Bot Usage Statistics</b>\n\n"
            f"👥 <b>Total Users:</b> {stats.get('total_users', 0)}\n"
            f"🏃‍♂️ <b>Active Users (7d):</b> {stats.get('active_users_7d', 0)}\n"
            f"👥 <b>Total Groups:</b> {stats.get('total_groups', 0)}\n"
            f"🆕 <b>New Users (24h):</b> {stats.get('new_users_24h', 0)}\n"
            f"💬 <b>Total Messages:</b> {stats.get('total_messages', 0)}\n"
            f"🔄 <b>Total Commands:</b> {stats.get('total_commands', 0)}\n\n"
        )
        
        # Add user count by chat type
        stats_message += "<b>Users by Chat Type:</b>\n"
        chat_types = stats.get('users_by_chat_type', {})
        for chat_type, count in chat_types.items():
            emoji = "👤" if chat_type == "private" else "👥" if chat_type in ["group", "supergroup"] else "📢" if chat_type == "channel" else "🔵"
            stats_message += f"{emoji} {chat_type.capitalize()}: {count}\n"
        
        # Add top commands
        top_commands = stats.get('top_commands', {})
        if top_commands:
            stats_message += "\n<b>Top Commands:</b>\n"
            for cmd, count in top_commands.items():
                stats_message += f"/{cmd}: {count} uses\n"
        
        # Send the statistics message
        await update.message.reply_text(stats_message, parse_mode=ParseMode.HTML)
        
    except Exception as e:
        logger.error(f"Error in user_stats_command: {e}")
        await update.message.reply_text(f"❌ Error retrieving user statistics: {str(e)}")


async def owner_help_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Display help for owner-only commands."""
    if update.effective_chat.type != "private":
        return

    bot_owner_username = context.bot_data.get("owner_username")
    user_username = update.effective_user.username

    if not bot_owner_username or not user_username or user_username.lower() != bot_owner_username.lower():
        await update.message.reply_text("❌ This command is only available to the bot owner.")
        return

    text = (
        "<b>👑 Owner Commands</b>\n\n"
        "• /bc - Broadcast a message to all chats\n"
        "• /bcstop - Cancel an active broadcast\n"
        "• /push [chat_id] - Forward replied content\n"
        "• /syncchats - Sync chat list for broadcast\n"
        "• /verifysync - Verify synced chats\n"
        "• /userstats - Show bot usage stats"
    )

    await update.message.reply_text(text, parse_mode=ParseMode.HTML)
