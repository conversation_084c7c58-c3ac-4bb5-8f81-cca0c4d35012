"""
Handlers for utility-related commands.
"""
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.constants import ParseMode
from telegram.ext import ContextTypes, CommandHandler
from ..api_services.utility_services import UtilityServices
import logging
import asyncio
import html

logger = logging.getLogger(__name__)

class UtilityCommandHandlers:
    def __init__(self):
        self.utility_service = UtilityServices()

    async def crypto(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Get cryptocurrency price information"""
        if not context.args:
            error_msg = await update.message.reply_text(
                "<b>❌ Usage Guide:</b>\n"
                "• <code>/crypto bitcoin</code>\n"
                "• <code>/crypto eth</code>\n"
                "• <code>/crypto doge</code>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()
            return

        symbol = context.args[0]
        try:
            # Send loading message
            loading_msg = await update.message.reply_text(
                "💰 <i>Checking those crypto prices...</i>",
                parse_mode=ParseMode.HTML
            )

            crypto_data = await self.utility_service.get_crypto_price(symbol)
            message = (
                f"<b>💎 {symbol.upper()} Price Update</b>\n\n"
                f"<b>USD:</b> <code>${crypto_data['usd']:,.2f}</code>\n"
                f"<b>EUR:</b> <code>€{crypto_data['eur']:,.2f}</code>\n"
                f"<b>24h Change:</b> <code>{crypto_data['usd_24h_change']:.2f}%</code>\n\n"
                f"<i>⏳ This message will self-destruct in 30s!</i>"
            )
            
            result_msg = await update.message.reply_text(message, parse_mode=ParseMode.HTML)
            await loading_msg.delete()
            
            # Delete command message
            try:
                await update.message.delete()
            except Exception:
                pass

            # Schedule deletion
            async def delete_messages():
                await asyncio.sleep(30)
                try:
                    await result_msg.delete()
                except Exception:
                    pass
            
            asyncio.create_task(delete_messages())

        except Exception as e:
            logger.error(f"Error getting crypto price: {e}")
            error_msg = await update.message.reply_text(
                "<b>❌ Oops! Something went wrong!</b>\n"
                "<i>Couldn't fetch that crypto info fam!</i>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()

    async def news(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Get news based on query"""
        if not context.args:
            error_msg = await update.message.reply_text(
                "<b>❌ Usage Guide:</b>\n"
                "• <code>/news AI technology</code>\n"
                "• <code>/news space exploration</code>\n"
                "• <code>/news crypto market</code>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()
            return

        query = " ".join(context.args)
        try:
            # Send loading message
            loading_msg = await update.message.reply_text(
                "📰 <i>Finding the latest news...</i>",
                parse_mode=ParseMode.HTML
            )

            news_data = await self.utility_service.get_news(query)
            message = f"<b>📰 News about '{html.escape(query)}'</b>\n\n"
            
            for i, article in enumerate(news_data, 1):
                date = article['date'].split('T')[0]
                message += (
                    f"<b>{i}.</b> <code>{html.escape(article['title'])}</code>\n"
                    f"📰 <b>Source:</b> <code>{html.escape(article['source'])}</code>\n"
                    f"📅 <b>Date:</b> <code>{date}</code>\n"
                    f"🔗 <a href='{article['url']}'>Read More</a>\n\n"
                )
            
            message += "<i>⏳ This message will self-destruct in 30s!</i>"
            
            result_msg = await update.message.reply_text(
                message, 
                parse_mode=ParseMode.HTML,
                disable_web_page_preview=True
            )
            await loading_msg.delete()
            
            # Delete command message
            try:
                await update.message.delete()
            except Exception:
                pass

            # Schedule deletion
            async def delete_messages():
                await asyncio.sleep(30)
                try:
                    await result_msg.delete()
                except Exception:
                    pass
            
            asyncio.create_task(delete_messages())

        except Exception as e:
            logger.error(f"Error getting news: {e}")
            error_msg = await update.message.reply_text(
                "<b>❌ Oops! Something went wrong!</b>\n"
                "<i>Couldn't fetch those news articles fam!</i>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()

    async def movie(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Get detailed movie information including ratings, awards, and box office data"""
        if not context.args:
            error_msg = await update.message.reply_text(
                "<b>🎬 YO! HOW TO USE THIS HEAT:</b>\n"
                "• <code>/movie Inception</code>\n"
                "• <code>/movie The Dark Knight</code>\n"
                "• <code>/movie Avatar</code>\n\n"
                "<i>Just drop the movie name after /movie and we lit! 🔥</i>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()
            try:
                await update.message.delete()
            except Exception:
                pass
            return

        user_command_msg = update.message
        title = " ".join(context.args)
        try:
            # Send loading message with style
            loading_msg = await update.message.reply_text(
                "🎬 <i>YO HOLD UP! Searching for that movie magic...</i>",
                parse_mode=ParseMode.HTML
            )

            movie_data = await self.utility_service.get_movie_info(title)
            if movie_data.get("Response") == "True":
                # Format ratings
                rating_text = ""
                for rating in movie_data.get('Ratings', []):
                    if rating['Source'] == "Internet Movie Database":
                        rating_text += f"⭐ <b>IMDb:</b> <code>{rating['Value']}</code>\n"
                    elif rating['Source'] == "Metacritic":
                        rating_text += f"📊 <b>Metacritic:</b> <code>{rating['Value']}</code>\n"
                    elif rating['Source'] == "Rotten Tomatoes":
                        rating_text += f"🍅 <b>Rotten Tomatoes:</b> <code>{rating['Value']}</code>\n"

                # Format keywords with emojis
                keywords = movie_data.get('Keywords', '').split(', ')
                if keywords and keywords[0] != 'N/A':
                    keyword_text = "🏷️ <b>Keywords:</b> " + " • ".join(f"<code>{k}</code>" for k in keywords[:5])
                else:
                    keyword_text = ""

                caption = (
                    f"<b>🎬 {html.escape(movie_data['Title'])} ({movie_data['Year']})</b>\n"
                    f"🎯 <b>IMDb ID:</b> <code>{movie_data['IMDbID']}</code>\n\n"
                    f"{rating_text}\n"
                    f"🎭 <b>Genre:</b> <code>{html.escape(movie_data['Genre'])}</code>\n"
                    f"⏱ <b>Runtime:</b> <code>{movie_data['Runtime']}</code>\n"
                    f"🎬 <b>Director:</b> <code>{html.escape(movie_data['Director'])}</code>\n"
                    f"👥 <b>Cast:</b> <code>{html.escape(movie_data['Actors'])}</code>\n\n"
                    f"💰 <b>Box Office:</b> <code>{movie_data['BoxOffice']}</code>\n"
                    f"🏆 <b>Awards:</b> <code>{html.escape(movie_data['Awards'])}</code>\n\n"
                    f"📝 <b>Plot:</b>\n<i>{html.escape(movie_data['Plot'])}</i>\n\n"
                )

                if keyword_text:
                    caption += f"{keyword_text}\n\n"

                caption += "<i>⏳ This heat will self-destruct in 45s!</i>"
                
                if movie_data.get('Poster') and movie_data['Poster'] != "N/A":
                    try:
                        result_msg = await update.message.reply_photo(
                            photo=movie_data['Poster'],
                            caption=caption,
                            parse_mode=ParseMode.HTML
                        )
                    except Exception:
                        # If poster fails, fall back to text-only
                        result_msg = await update.message.reply_text(
                            caption + "\n\n<i>❌ Poster couldn't be loaded</i>",
                            parse_mode=ParseMode.HTML
                        )
                else:
                    result_msg = await update.message.reply_text(
                        caption,
                        parse_mode=ParseMode.HTML
                    )
            else:
                result_msg = await update.message.reply_text(
                    "❌ <b>AYO! MOVIE NOT FOUND!</b>\n"
                    "<i>Double check that spelling or try another movie! We got you! 💯</i>",
                    parse_mode=ParseMode.HTML
                )

            await loading_msg.delete()

            # Schedule synchronized deletion of both messages
            async def delete_messages():
                await asyncio.sleep(45)
                try:
                    await asyncio.gather(
                        result_msg.delete(),
                        user_command_msg.delete()
                    )
                except Exception as e:
                    logger.error(f"Error deleting messages: {e}")
            
            asyncio.create_task(delete_messages())

        except Exception as e:
            logger.error(f"Error in movie command: {str(e)}")
            error_msg = await update.message.reply_text(
                "❌ <b>BRUH! SOMETHING AIN'T RIGHT!</b>\n"
                "<i>The movie search ain't working rn, try again later! We'll bounce back! 💪</i>",
                parse_mode=ParseMode.HTML
            )
            await loading_msg.delete()
            await asyncio.sleep(5)
            await error_msg.delete()
            try:
                await user_command_msg.delete()
            except Exception:
                pass

    async def recipe(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Get AI-generated recipe information"""
        if not context.args:
            error_msg = await update.message.reply_text(
                "❌ <b>Yo fam! Here's how to use the recipe command:</b>\n\n"
                "🔥 Drop the dish name after /recipe like:\n"
                "• <code>/recipe chicken curry</code>\n"
                "• <code>/recipe pizza</code>\n"
                "• <code>/recipe chocolate cake</code>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()
            return

        query = " ".join(context.args)
        try:
            loading_msg = await update.message.reply_text(
                "👨‍🍳 <i>Cooking up that recipe for you, fam! Just a sec...</i>",
                parse_mode=ParseMode.HTML
            )

            recipe_data = await self.utility_service.get_recipe(query)
            if recipe_data.get("Response") == "True":
                recipe = recipe_data["Recipe"]
                
                # Format ingredients with fire emojis
                ingredients_list = "\n".join([f"🔸 {ingredient}" for ingredient in recipe["ingredients"]])
                
                # Make instructions pop with numbered steps
                instructions_list = "\n".join([f"{i+1}. {instruction}" for i, instruction in enumerate(recipe["instructions"])])
                
                # Style those chef tips
                tips_list = "\n".join([f"💫 {tip}" for tip in recipe["tips"]])
                
                # Format those extra notes
                notes = recipe_data.get("Notes", [])
                notes_list = "\n".join([f"✨ {note}" for note in notes]) if notes else ""
                
                message = (
                    f"<b>🎯 {html.escape(recipe['name'])}</b>\n"
                    f"<i>📍 {html.escape(recipe['cuisine_type'])} Vibes</i>\n\n"
                    
                    f"⏱ <b>Prep Time:</b> <code>{recipe['prep_time']}</code>\n"
                    f"🔥 <b>Cook Time:</b> <code>{recipe['cook_time']}</code>\n"
                    f"💪 <b>Level:</b> <code>{recipe['difficulty']}</code>\n\n"
                    
                    f"<b>🛒 What You'll Need:</b>\n{ingredients_list}\n\n"
                    
                    f"<b>📝 Let's Cook It Up:</b>\n{instructions_list}\n\n"
                    
                    f"<b>💡 Pro Tips:</b>\n{tips_list}\n\n"
                    
                    f"<b>🥗 Nutrition Facts (per serving):</b>\n"
                    f"• Energy: <code>{recipe['nutrition']['calories']}</code>\n"
                    f"• Protein Power: <code>{recipe['nutrition']['protein']}</code>\n"
                    f"• Carb Count: <code>{recipe['nutrition']['carbs']}</code>\n"
                    f"• Healthy Fats: <code>{recipe['nutrition']['fats']}</code>\n"
                )
                
                # Add those extra tips if we got 'em
                if notes_list:
                    message += f"\n<b>📌 Extra Tips & Tricks:</b>\n{notes_list}\n"
                
                message += "\n<i>⏳ Recipe vanishes in 60s - Screenshot it if you need it! 📸</i>"
            else:
                message = "<b>❌ Aw snap! Couldn't cook up that recipe!</b>\n<i>Try another dish maybe? We got you! 💪</i>"

            result_msg = await update.message.reply_text(message, parse_mode=ParseMode.HTML)
            await loading_msg.delete()
            
            try:
                await update.message.delete()
            except Exception:
                pass

            async def delete_messages():
                await asyncio.sleep(60)
                try:
                    await result_msg.delete()
                except Exception:
                    pass
            
            asyncio.create_task(delete_messages())

        except Exception as e:
            logger.error(f"Error generating recipe: {e}")
            error_msg = await update.message.reply_text(
                "<b>❌ My bad fam! Something went wrong!</b>\n"
                "<i>Let's try that again! 💪</i>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()

    async def space(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Get information about astronauts in space"""
        try:
            # Send loading message
            loading_msg = await update.message.reply_text(
                "🚀 <i>Checking who's up in space...</i>",
                parse_mode=ParseMode.HTML
            )

            space_data = await self.utility_service.get_space_info()
            message = (
                f"<b>🛸 Space Station Crew Report</b>\n\n"
                f"<b>Currently in Space:</b> <code>{space_data['number']} astronauts</code>\n\n"
            )
            
            for person in space_data['people']:
                message += f"👨‍🚀 <code>{html.escape(person['name'])}</code> on <b>{html.escape(person['craft'])}</b>\n"
            
            message += "\n<i>⏳ This message will self-destruct in 30s!</i>"

            result_msg = await update.message.reply_text(message, parse_mode=ParseMode.HTML)
            await loading_msg.delete()
            
            # Delete command message
            try:
                await update.message.delete()
            except Exception:
                pass

            # Schedule deletion
            async def delete_messages():
                await asyncio.sleep(30)
                try:
                    await result_msg.delete()
                except Exception:
                    pass
            
            asyncio.create_task(delete_messages())

        except Exception as e:
            logger.error(f"Error getting space info: {e}")
            error_msg = await update.message.reply_text(
                "<b>❌ Houston, we have a problem!</b>\n"
                "<i>Couldn't get the space crew info!</i>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()

    async def wiki(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Search Wikipedia or get a random article"""
        try:
            # Send loading message
            loading_msg = await update.message.reply_text(
                "📚 <i>Searching Wikipedia...</i>",
                parse_mode=ParseMode.HTML
            )

            if context.args:
                # Search for specific query
                query = " ".join(context.args)
                wiki_data = await self.utility_service.get_wikipedia(query)
                if wiki_data:
                    message = (
                        f"<b>📚 {html.escape(wiki_data['title'])}</b>\n\n"
                        f"<i>{html.escape(wiki_data['extract'])}</i>\n\n"
                        f"🔗 <a href='{wiki_data['url']}'>Read More on Wikipedia</a>\n\n"
                        f"<i>⏳ This message will self-destruct in 30s!</i>"
                    )
                else:
                    message = "<b>❌ No Wikipedia article found!</b>\n<i>Try searching with different keywords!</i>"
            else:
                # Get random article
                wiki_data = await self.utility_service.get_random_wikipedia()
                message = (
                    f"<b>🎲 Random Wikipedia Article</b>\n\n"
                    f"<b>📚 {html.escape(wiki_data['title'])}</b>\n\n"
                    f"<i>{html.escape(wiki_data['extract'])}</i>\n\n"
                    f"🔗 <a href='{wiki_data['url']}'>Read More on Wikipedia</a>\n\n"
                    f"<i>⏳ This message will self-destruct in 30s!</i>"
                )

            result_msg = await update.message.reply_text(
                message, 
                parse_mode=ParseMode.HTML,
                disable_web_page_preview=True
            )
            await loading_msg.delete()
            
            # Delete command message
            try:
                await update.message.delete()
            except Exception:
                pass

            # Schedule deletion
            async def delete_messages():
                await asyncio.sleep(30)
                try:
                    await result_msg.delete()
                except Exception:
                    pass
            
            asyncio.create_task(delete_messages())

        except Exception as e:
            logger.error(f"Error getting Wikipedia article: {e}")
            error_msg = await update.message.reply_text(
                "<b>❌ Oops! Something went wrong!</b>\n"
                "<i>Couldn't fetch that Wikipedia article fam!</i>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()

    async def define(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Get word definition"""
        if not context.args:
            error_msg = await update.message.reply_text(
                "<b>❌ Usage Guide:</b>\n"
                "• <code>/define hello</code>\n"
                "• <code>/define awesome</code>\n"
                "• <code>/define programming</code>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()
            return

        word = context.args[0]
        try:
            # Send loading message
            loading_msg = await update.message.reply_text(
                "📖 <i>Looking up that word...</i>",
                parse_mode=ParseMode.HTML
            )

            word_data = await self.utility_service.get_word_info(word)
            if word_data:
                message = f"<b>📖 {html.escape(word_data['word'].capitalize())}</b>\n\n"
                
                for meaning in word_data['meanings']:
                    message += f"<b>({html.escape(meaning['partOfSpeech'])})</b>\n"
                    for definition in meaning['definitions']:
                        message += f"• <i>{html.escape(definition['definition'])}</i>\n"
                    message += "\n"
                
                message += "<i>⏳ This message will self-destruct in 30s!</i>"
            else:
                message = "<b>❌ Word not found!</b>\n<i>Try checking the spelling!</i>"

            result_msg = await update.message.reply_text(message, parse_mode=ParseMode.HTML)
            await loading_msg.delete()
            
            # Delete command message
            try:
                await update.message.delete()
            except Exception:
                pass

            # Schedule deletion
            async def delete_messages():
                await asyncio.sleep(30)
                try:
                    await result_msg.delete()
                except Exception:
                    pass
            
            asyncio.create_task(delete_messages())

        except Exception as e:
            logger.error(f"Error getting word definition: {e}")
            error_msg = await update.message.reply_text(
                "<b>❌ Oops! Something went wrong!</b>\n"
                "<i>Couldn't fetch that definition fam!</i>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()

    async def github(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Get GitHub user information"""
        if not context.args:
            error_msg = await update.message.reply_text(
                "<b>❌ Usage Guide:</b>\n"
                "• <code>/github torvalds</code>\n"
                "• <code>/github microsoft</code>\n"
                "• <code>/github google</code>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()
            return

        username = context.args[0]
        try:
            # Send loading message
            loading_msg = await update.message.reply_text(
                "🐙 <i>Fetching GitHub profile...</i>",
                parse_mode=ParseMode.HTML
            )

            user_data = await self.utility_service.get_github_user(username)
            if not user_data.get('message'):
                message = (
                    f"<b>🐙 GitHub Profile: {html.escape(user_data['login'])}</b>\n\n"
                    f"👤 <b>Name:</b> <code>{html.escape(user_data.get('name', 'N/A'))}</code>\n"
                    f"📝 <b>Bio:</b> <i>{html.escape(user_data.get('bio', 'N/A'))}</i>\n\n"
                    f"📊 <b>Stats:</b>\n"
                    f"• <b>Public Repos:</b> <code>{user_data['public_repos']}</code>\n"
                    f"• <b>Followers:</b> <code>{user_data['followers']}</code>\n"
                    f"• <b>Following:</b> <code>{user_data['following']}</code>\n\n"
                    f"🔗 <a href='{user_data['html_url']}'>View Profile</a>\n\n"
                    f"<i>⏳ This message will self-destruct in 30s!</i>"
                )
            else:
                message = "<b>❌ GitHub user not found!</b>\n<i>Check the username and try again!</i>"

            result_msg = await update.message.reply_text(
                message, 
                parse_mode=ParseMode.HTML,
                disable_web_page_preview=True
            )
            await loading_msg.delete()
            
            # Delete command message
            try:
                await update.message.delete()
            except Exception:
                pass

            # Schedule deletion
            async def delete_messages():
                await asyncio.sleep(30)
                try:
                    await result_msg.delete()
                except Exception:
                    pass
            
            asyncio.create_task(delete_messages())

        except Exception as e:
            logger.error(f"Error getting GitHub user info: {e}")
            error_msg = await update.message.reply_text(
                "<b>❌ Oops! Something went wrong!</b>\n"
                "<i>Couldn't fetch that GitHub profile fam!</i>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()

    async def riddle(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Send a random riddle"""
        try:
            # Send loading message
            loading_msg = await update.message.reply_text(
                "🤔 <i>Finding a tricky riddle...</i>",
                parse_mode=ParseMode.HTML
            )

            riddle_data = await self.utility_service.get_random_riddle()
            message = (
                f"<b>🎯 Here's a Riddle:</b>\n\n"
                f"<i>{html.escape(riddle_data['question'])}</i>\n\n"
                f"<tg-spoiler><b>Answer:</b> {html.escape(riddle_data['answer'])}</tg-spoiler>\n\n"
                f"<i>⏳ This message will self-destruct in 30s!</i>"
            )

            result_msg = await update.message.reply_text(message, parse_mode=ParseMode.HTML)
            await loading_msg.delete()
            
            # Delete command message
            try:
                await update.message.delete()
            except Exception:
                pass

            # Schedule deletion
            async def delete_messages():
                await asyncio.sleep(30)
                try:
                    await result_msg.delete()
                except Exception:
                    pass
            
            asyncio.create_task(delete_messages())

        except Exception as e:
            logger.error(f"Error getting riddle: {e}")
            error_msg = await update.message.reply_text(
                "<b>❌ Oops! Something went wrong!</b>\n"
                "<i>Couldn't fetch a riddle fam!</i>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()

    async def technews(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Get latest technology news"""
        try:
            # Send loading message
            loading_msg = await update.message.reply_text(
                "💻 <i>Fetching the latest tech news...</i>",
                parse_mode=ParseMode.HTML
            )

            news_data = await self.utility_service.get_tech_news()
            message = "<b>💻 Latest Tech News</b>\n\n"
            
            for i, article in enumerate(news_data, 1):
                message += (
                    f"<b>{i}.</b> <code>{html.escape(article['title'])}</code>\n"
                    f"🔗 <a href='{article['url']}'>Read More</a>\n\n"
                )
            
            message += "<i>⏳ This message will self-destruct in 30s!</i>"

            result_msg = await update.message.reply_text(
                message, 
                parse_mode=ParseMode.HTML,
                disable_web_page_preview=True
            )
            await loading_msg.delete()
            
            # Delete command message
            try:
                await update.message.delete()
            except Exception:
                pass

            # Schedule deletion
            async def delete_messages():
                await asyncio.sleep(30)
                try:
                    await result_msg.delete()
                except Exception:
                    pass
            
            asyncio.create_task(delete_messages())

        except Exception as e:
            logger.error(f"Error getting tech news: {e}")
            error_msg = await update.message.reply_text(
                "<b>❌ Oops! Something went wrong!</b>\n"
                "<i>Couldn't fetch tech news fam!</i>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()

    async def shorten(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Shorten a URL"""
        if not context.args:
            error_msg = await update.message.reply_text(
                "<b>❌ Usage Guide:</b>\n"
                "• <code>/shorten https://example.com</code>\n"
                "• <code>/shorten https://very-long-url.com</code>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()
            return

        url = context.args[0]
        try:
            # Send loading message
            loading_msg = await update.message.reply_text(
                "✂️ <i>Shortening that URL...</i>",
                parse_mode=ParseMode.HTML
            )

            short_url = await self.utility_service.shorten_url(url)
            message = (
                f"<b>🔗 URL Shortener</b>\n\n"
                f"📎 <b>Original:</b>\n<code>{html.escape(url)}</code>\n\n"
                f"✂️ <b>Shortened:</b>\n<code>{html.escape(short_url)}</code>\n\n"
                f"<i>⏳ This message will self-destruct in 30s!</i>"
            )

            result_msg = await update.message.reply_text(
                message, 
                parse_mode=ParseMode.HTML,
                disable_web_page_preview=True
            )
            await loading_msg.delete()
            
            # Delete command message
            try:
                await update.message.delete()
            except Exception:
                pass

            # Schedule deletion
            async def delete_messages():
                await asyncio.sleep(30)
                try:
                    await result_msg.delete()
                except Exception:
                    pass
            
            asyncio.create_task(delete_messages())

        except Exception as e:
            logger.error(f"Error shortening URL: {e}")
            error_msg = await update.message.reply_text(
                "<b>❌ Oops! Something went wrong!</b>\n"
                "<i>Couldn't shorten that URL fam!</i>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()

    async def utility(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show utility commands available in the bot."""
        if not update.effective_message:
            return

        utility_text = """
<b>🛠️ Utility Commands</b>

<b>• Information & Updates:</b>
  - /crypto [symbol] - Get cryptocurrency prices 💰
  - /news [query] - Get latest news on a topic 📰
  - /technews - Get latest tech news 📱
  - /weather [location] - Check weather forecast ⛅

<b>• Knowledge & Research:</b>
  - /wiki [query] - Quick Wikipedia lookup 📚
  - /define [word] - Dictionary definitions 📖
  - /github [username/repo] - GitHub repository info 💻
  - /space - Get interesting space facts 🚀

<b>• Entertainment & Media:</b>
  - /movie [title] - Get detailed movie information 🎬
  - /recipe [dish] - Find cooking recipes 🍳
  - /riddle - Get a brain teaser 🧩

<b>• Tools:</b>
  - /shorten [url] - Shorten long URLs 🔗

<b>Usage Tips:</b>
• For /crypto: Use common symbols like 'btc', 'eth', 'doge'
• For /news: Try topics like 'AI technology', 'space exploration'
• For /wiki: Keep queries concise for best results

<i>All responses auto-delete after a short time to keep chats clean!</i> ✨
"""

        chat_type = update.effective_chat.type
        is_private = chat_type == "private"

        buttons = [
            [
                InlineKeyboardButton("💰 Crypto", callback_data="utility_crypto"),
                InlineKeyboardButton("📰 News", callback_data="utility_news")
            ],
            [
                InlineKeyboardButton("🎬 Movies", callback_data="utility_movies"),
                InlineKeyboardButton("📚 Knowledge", callback_data="utility_knowledge")
            ],
            [
                InlineKeyboardButton("◀️ Back to Help", callback_data="start_help")
            ]
        ]
        
        reply_markup = InlineKeyboardMarkup(buttons)

        # Use helper function to send message with auto-delete for groups
        if hasattr(update, '_send_group_message'):
            await update._send_group_message(
                update,
                context,
                utility_text,
                ParseMode.HTML,
                reply_markup
            )
        else:
            # Fallback if helper function is not available
            result_msg = await update.effective_message.reply_text(
                utility_text,
                parse_mode=ParseMode.HTML,
                reply_markup=reply_markup
            )
            
            # Auto-delete in groups
            if not is_private:
                async def delete_messages():
                    await asyncio.sleep(60)
                    try:
                        await result_msg.delete()
                        if update.message:
                            await update.message.delete()
                    except Exception:
                        pass
                
                asyncio.create_task(delete_messages())

    def register_handlers(self, application):
        """Register all utility command handlers"""
        application.add_handler(CommandHandler("crypto", self.crypto))
        application.add_handler(CommandHandler("news", self.news))
        application.add_handler(CommandHandler("movie", self.movie))
        application.add_handler(CommandHandler("recipe", self.recipe))
        application.add_handler(CommandHandler("space", self.space))
        application.add_handler(CommandHandler("wiki", self.wiki))
        application.add_handler(CommandHandler("define", self.define))
        application.add_handler(CommandHandler("github", self.github))
        application.add_handler(CommandHandler("riddle", self.riddle))
        application.add_handler(CommandHandler("technews", self.technews))
        application.add_handler(CommandHandler("shorten", self.shorten))
        application.add_handler(CommandHandler("utility", self.utility))


def setup(application):
    """Setup utility command handlers"""
    utility_handler = UtilityCommandHandlers()
    utility_handler.register_handlers(application)
