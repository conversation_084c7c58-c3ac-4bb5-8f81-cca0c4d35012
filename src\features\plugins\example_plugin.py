"""
Example Plugin for NexusAI Telegram Bot
"""
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes, Application
from ..plugin_system.base_plugin import BasePlugin
from ..plugin_system.plugin_types import PluginMetadata, PluginPermission
from ..plugin_system.decorators import command, event_handler

class ExamplePlugin(BasePlugin):
    # Define plugin metadata
    metadata = PluginMetadata(
        name="ExamplePlugin",
        version="1.0.0",
        description="An example plugin to demonstrate the plugin system",
        author="NexusAI Team",
        settings_schema={
            "type": "object",
            "properties": {
                "greeting": {
                    "type": "string",
                    "default": "Hello!"
                },
                "farewell": {
                    "type": "string",
                    "default": "Goodbye!"
                }
            }
        }
    )
    
    def __init__(self):
        super().__init__()
        self.settings = {
            "greeting": "Hello!",
            "farewell": "Goodbye!"
        }
        
    async def setup(self, application: Application) -> None:
        """Set up the plugin"""
        await super().setup(application)
        self.logger.info("Example plugin setup complete")
        
    async def cleanup(self) -> None:
        """Clean up resources"""
        self.logger.info("Example plugin cleanup complete")
        
    @command(
        name="hello",
        description="Say hello with a customizable greeting",
        permission=PluginPermission.USER,
        usage="/hello [name]",
        aliases=("hi", "hey")
    )
    async def hello_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle the /hello command"""
        name = context.args[0] if context.args else "there"
        greeting = self.settings["greeting"]
        
        # Create inline keyboard
        keyboard = [
            [
                InlineKeyboardButton("👋 Wave back", callback_data="wave_back"),
                InlineKeyboardButton("👎 Ignore", callback_data="ignore")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            f"{greeting} {name}! How are you?",
            reply_markup=reply_markup
        )
        
    @command(
        name="plugin_info",
        description="Get information about this plugin",
        permission=PluginPermission.USER
    )
    async def plugin_info_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle the /plugin_info command"""
        info = self.get_info()
        message = (
            f"📌 Plugin: {info['metadata']['name']}\n"
            f"📝 Description: {info['metadata']['description']}\n"
            f"📦 Version: {info['metadata']['version']}\n"
            f"👤 Author: {info['metadata']['author']}\n"
            f"⚡ Status: {'Enabled' if info['enabled'] else 'Disabled'}\n\n"
            "Available Commands:\n"
        )
        
        for cmd_name, cmd_info in info['commands'].items():
            message += (
                f"/{cmd_name} - {cmd_info['description']}\n"
                f"  Permission: {cmd_info['permission']}\n"
                f"  Usage: {cmd_info['usage'] or 'No usage info'}\n"
            )
            
        await update.message.reply_text(message)
        
    @event_handler("new_member")
    async def on_new_member(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle new member events"""
        if not update.message.new_chat_members:
            return
            
        for member in update.message.new_chat_members:
            await update.message.reply_text(
                f"Welcome {member.mention_html()}! {self.settings['greeting']}",
                parse_mode='HTML'
            )
            
    @event_handler("member_left")
    async def on_member_left(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle member left events"""
        if not update.message.left_chat_member:
            return
            
        member = update.message.left_chat_member
        await update.message.reply_text(
            f"Goodbye {member.mention_html()}! {self.settings['farewell']}",
            parse_mode='HTML'
        )
