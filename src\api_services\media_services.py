"""
Advanced media manipulation services.
"""
import aiohttp
import base64
import io
from PIL import Image
from typing import Dict, Optional, Any, Tuple
import qrcode
import random
import string

class MediaServices:
    def __init__(self):
        self.session = None

    async def ensure_session(self):
        if self.session is None:
            self.session = aiohttp.ClientSession()

    async def close_session(self):
        if self.session:
            await self.session.close()
            self.session = None

    async def get_random_meme(self) -> Dict[str, str]:
        """Get a random meme from meme-api.com"""
        await self.ensure_session()
        url = "https://meme-api.com/gimme"
        async with self.session.get(url) as response:
            data = await response.json()
            return {
                "url": data["url"],
                "title": data["title"],
                "author": data["author"]
            }

    def generate_qr_code(self, data: str) -> io.BytesIO:
        """Generate QR code from text"""
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(data)
        qr.make(fit=True)

        img = qr.make_image(fill_color="black", back_color="white")
        bio = io.BytesIO()
        img.save(bio, 'PNG')
        bio.seek(0)
        return bio
