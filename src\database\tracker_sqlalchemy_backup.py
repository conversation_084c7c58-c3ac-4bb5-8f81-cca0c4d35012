"""
Database tracker for monitoring server and user activity.
"""
import logging
from typing import Optional, Dict, Any, List
from datetime import datetime
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import select, update, and_
from sqlalchemy.orm import Session

from src.database.models import (
    User, Server, ServerMember, Message, CommandUsage, 
    UserWarning, ChatSummary, MusicPlayback, ServerSettings, init_db
)

logger = logging.getLogger(__name__)

class DatabaseTracker:
    """Handles tracking of server and user activity in the database."""
    
    def __init__(self):
        """Initialize the database tracker."""
        self.session = init_db('sqlite:///bot_data.db')
    
    def close(self):
        """Close the database session."""
        if self.session:
            self.session.close()
    
    def track_user(self, user_id: int, username: str = None, first_name: str = None, 
                  last_name: str = None) -> Optional[User]:
        """
        Track a user in the database.
        
        Args:
            user_id: The Telegram user ID
            username: The user's username
            first_name: The user's first name
            last_name: The user's last name
            
        Returns:
            The User object or None if there was an error
        """
        try:
            # Check if user exists
            stmt = select(User).where(User.telegram_id == user_id)
            user = self.session.execute(stmt).scalar_one_or_none()
            
            if user:
                # Update user information
                user.username = username or user.username
                user.first_name = first_name or user.first_name
                # Skip last_name as it might not exist in the database yet
                user.last_active = datetime.utcnow()
                user.is_active = True
            else:
                # Create new user with only the columns that exist in the database
                user = User(
                    telegram_id=user_id,
                    username=username,
                    first_name=first_name,
                    # Skip last_name as it might not exist in the database yet
                    last_active=datetime.utcnow(),
                    is_active=True
                )
                self.session.add(user)
            
            self.session.commit()
            return user
        except SQLAlchemyError as e:
            logger.error(f"Error tracking user {user_id}: {e}")
            self.session.rollback()
            return None
    
    def track_server(self, server_id: int, title: str = None, 
                    description: str = None) -> Optional[Server]:
        """
        Track a server in the database.
        
        Args:
            server_id: The Telegram chat ID
            title: The server title
            description: The server description
            
        Returns:
            The Server object or None if there was an error
        """
        try:
            # Check if server exists
            stmt = select(Server).where(Server.telegram_id == server_id)
            server = self.session.execute(stmt).scalar_one_or_none()
            
            if server:
                # Update server information
                if title:
                    server.title = title
                if description:
                    server.description = description
                server.is_active = True
            else:
                # Create new server
                server = Server(
                    telegram_id=server_id,
                    title=title,
                    description=description,
                    is_active=True,
                    joined_at=datetime.utcnow()
                )
                self.session.add(server)
            
            self.session.commit()
            return server
        except SQLAlchemyError as e:
            logger.error(f"Error tracking server {server_id}: {e}")
            self.session.rollback()
            return None
    
    def track_server_member(self, server_id: int, user_id: int, 
                           is_admin: bool = False) -> Optional[ServerMember]:
        """
        Track a server member in the database.
        
        Args:
            server_id: The server ID
            user_id: The user ID
            is_admin: Whether the user is an admin
            
        Returns:
            The ServerMember object or None if there was an error
        """
        try:
            # Get server and user objects
            server_stmt = select(Server).where(Server.telegram_id == server_id)
            server = self.session.execute(server_stmt).scalar_one_or_none()
            
            user_stmt = select(User).where(User.telegram_id == user_id)
            user = self.session.execute(user_stmt).scalar_one_or_none()
            
            if not server or not user:
                logger.error(f"Server {server_id} or User {user_id} not found")
                return None
            
            # Check if server member exists
            stmt = select(ServerMember).where(
                and_(ServerMember.server_id == server.id, ServerMember.user_id == user.id)
            )
            member = self.session.execute(stmt).scalar_one_or_none()
            
            if member:
                # Update member information
                member.is_admin = is_admin
                member.is_active = True
                member.last_active = datetime.utcnow()
            else:
                # Create new server member
                member = ServerMember(
                    server_id=server.id,
                    user_id=user.id,
                    is_admin=is_admin,
                    is_active=True,
                    joined_at=datetime.utcnow(),
                    last_active=datetime.utcnow()
                )
                self.session.add(member)
            
            self.session.commit()
            return member
        except SQLAlchemyError as e:
            logger.error(f"Error tracking server member {user_id} in {server_id}: {e}")
            self.session.rollback()
            return None
    
    def track_message(self, chat_id: int, user_id: int, message_text: str = None,
                     message_type: str = "text", has_media: bool = False,
                     media_type: str = None) -> Optional[Message]:
        """
        Track a message in the database.
        
        Args:
            chat_id: The chat ID
            user_id: The user ID
            message_text: The message text
            message_type: The message type
            has_media: Whether the message has media
            media_type: The media type
            
        Returns:
            The Message object or None if there was an error
        """
        try:
            # Get server and user objects
            server_stmt = select(Server).where(Server.telegram_id == chat_id)
            server = self.session.execute(server_stmt).scalar_one_or_none()
            
            user_stmt = select(User).where(User.telegram_id == user_id)
            user = self.session.execute(user_stmt).scalar_one_or_none()
            
            if not server or not user:
                logger.error(f"Server {chat_id} or User {user_id} not found")
                return None
            
            # Create new message
            message = Message(
                user_id=user.id,
                chat_id=chat_id,
                message_text=message_text,
                message_type=message_type,
                has_media=has_media,
                media_type=media_type,
                timestamp=datetime.utcnow()
            )
            self.session.add(message)
            
            # Update server member message count
            member_stmt = select(ServerMember).where(
                and_(ServerMember.server_id == server.id, ServerMember.user_id == user.id)
            )
            member = self.session.execute(member_stmt).scalar_one_or_none()
            
            if member:
                member.message_count += 1
                member.last_active = datetime.utcnow()
            
            self.session.commit()
            return message
        except SQLAlchemyError as e:
            logger.error(f"Error tracking message from {user_id} in {chat_id}: {e}")
            self.session.rollback()
            return None
    
    def track_command(self, user_id: int, command: str, server_id: int = None,
                     arguments: str = None, success: bool = True) -> Optional[CommandUsage]:
        """
        Track a command usage in the database.
        
        Args:
            user_id: The user ID
            command: The command name
            server_id: The server ID (optional for private chats)
            arguments: The command arguments
            success: Whether the command was successful
            
        Returns:
            The CommandUsage object or None if there was an error
        """
        try:
            # Get user object
            user_stmt = select(User).where(User.telegram_id == user_id)
            user = self.session.execute(user_stmt).scalar_one_or_none()
            
            if not user:
                logger.error(f"User {user_id} not found")
                return None
            
            # Get server object if provided
            server = None
            if server_id:
                server_stmt = select(Server).where(Server.telegram_id == server_id)
                server = self.session.execute(server_stmt).scalar_one_or_none()
                
                if not server:
                    logger.error(f"Server {server_id} not found")
                    return None
            
            # Create new command usage
            command_usage = CommandUsage(
                user_id=user.id,
                server_id=server.id if server else None,
                command=command,
                arguments=arguments,
                timestamp=datetime.utcnow(),
                success=success
            )
            self.session.add(command_usage)
            self.session.commit()
            return command_usage
        except SQLAlchemyError as e:
            logger.error(f"Error tracking command {command} from {user_id}: {e}")
            self.session.rollback()
            return None
    
    def get_server_settings(self, server_id: int) -> Optional[ServerSettings]:
        """
        Get settings for a server.
        
        Args:
            server_id: The server ID (Telegram chat ID)
            
        Returns:
            The ServerSettings object or None if not found
        """
        try:
            # Get server object
            server_stmt = select(Server).where(Server.telegram_id == server_id)
            server = self.session.execute(server_stmt).scalar_one_or_none()
            
            if not server:
                logger.error(f"Server {server_id} not found")
                return None
            
            # Get or create server settings
            settings_stmt = select(ServerSettings).where(ServerSettings.server_id == server.id)
            settings = self.session.execute(settings_stmt).scalar_one_or_none()
            
            if not settings:
                # Create default settings
                settings = ServerSettings(server_id=server.id)
                self.session.add(settings)
                self.session.commit()
            
            return settings
        except SQLAlchemyError as e:
            logger.error(f"Error getting server settings for {server_id}: {e}")
            self.session.rollback()
            return None
    
    def update_server_settings(self, server_id: int, **kwargs) -> bool:
        """
        Update settings for a server.
        
        Args:
            server_id: The server ID (Telegram chat ID)
            **kwargs: Settings to update
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Get server settings
            settings = self.get_server_settings(server_id)
            
            if not settings:
                return False
            
            # Update settings
            for key, value in kwargs.items():
                if hasattr(settings, key):
                    setattr(settings, key, value)
            
            # Update timestamp
            settings.updated_at = datetime.utcnow()
            
            self.session.commit()
            return True
        except SQLAlchemyError as e:
            logger.error(f"Error updating server settings for {server_id}: {e}")
            self.session.rollback()
            return False
    
    def get_server_stats(self, server_id: int) -> Dict[str, Any]:
        """
        Get statistics for a server.
        
        Args:
            server_id: The server ID
            
        Returns:
            A dictionary of server statistics
        """
        try:
            # Get server object
            server_stmt = select(Server).where(Server.telegram_id == server_id)
            server = self.session.execute(server_stmt).scalar_one_or_none()
            
            if not server:
                logger.error(f"Server {server_id} not found")
                return {}
            
            # Get member count
            member_stmt = select(ServerMember).where(
                and_(ServerMember.server_id == server.id, ServerMember.is_active == True)
            )
            members = self.session.execute(member_stmt).scalars().all()
            
            # Get message count
            message_stmt = select(Message).where(Message.chat_id == server_id)
            messages = self.session.execute(message_stmt).scalars().all()
            
            # Get command usage
            command_stmt = select(CommandUsage).where(CommandUsage.server_id == server.id)
            commands = self.session.execute(command_stmt).scalars().all()
            
            # Get top users by message count
            top_users = sorted(members, key=lambda m: m.message_count, reverse=True)[:10]
            
            # Get server settings
            settings = self.get_server_settings(server_id)
            settings_dict = {}
            if settings:
                settings_dict = {
                    "protection_enabled": settings.protection_enabled,
                    "link_filter_enabled": settings.link_filter_enabled,
                    "media_filter_enabled": settings.media_filter_enabled,
                    "nsfw_filter_enabled": settings.nsfw_filter_enabled,
                    "restrict_new_users": settings.restrict_new_users,
                    "welcome_enabled": settings.welcome_enabled,
                    "goodbye_enabled": settings.goodbye_enabled,
                    "antiflood_enabled": settings.antiflood_enabled,
                    "antiflood_limit": settings.antiflood_limit,
                    "antiflood_time": settings.antiflood_time,
                    "logging_enabled": settings.logging_enabled
                }
            
            return {
                "server_id": server_id,
                "title": server.title,
                "member_count": len(members),
                "message_count": len(messages),
                "command_count": len(commands),
                "settings": settings_dict,
                "top_users": [
                    {
                        "user_id": member.user.telegram_id,
                        "username": member.user.username,
                        "message_count": member.message_count
                    }
                    for member in top_users
                ]
            }
        except SQLAlchemyError as e:
            logger.error(f"Error getting server stats for {server_id}: {e}")
            return {}

# Create a singleton instance
db_tracker = DatabaseTracker()