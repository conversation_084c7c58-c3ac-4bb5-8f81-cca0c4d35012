"""
Server statistics command handler.
"""
import logging
from telegram import Update
from telegram.ext import ContextTypes
from src.utils.decorators import admin_only, group_only
from src.database.tracker import db_tracker

logger = logging.getLogger(__name__)

@admin_only()
@group_only()
async def server_stats_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Get statistics for the current server.
    
    This command is only available to admins in groups.
    """
    if not update.effective_chat:
        return
    
    chat_id = update.effective_chat.id
    
    # Get server statistics
    stats = db_tracker.get_server_stats(chat_id)
    
    if not stats:
        await update.message.reply_text("❌ Failed to retrieve server statistics.")
        return
    
    # Format statistics message with witty, savage style using Markdown
    message = (
        f"📊 *Server Stats for {stats['title']}*\n\n"
        f"👥 *Members:* {stats['member_count']} — More heads than a rap cypher!\n"
        f"💬 *Total Messages:* {stats['message_count']} — Y'all talk more than reality TV!\n"
        f"🤖 *Commands Used:* {stats['command_count']} — Can't stop, won't stop pressing my buttons!\n\n"
    )

    # Add top users
    message += "👑 *Top Active Users:*\n"
    for i, user in enumerate(stats['top_users'][:5], 1):
        username = user['username'] or f"User {user['user_id']}"
        message += f"{i}. {username}: {user['message_count']} messages — Dropping more lines than a mixtape!\n"

    await update.message.reply_text(message, parse_mode="Markdown")

@admin_only()
@group_only()
async def user_activity_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Get activity for a specific user in the server.
    
    Usage: /useractivity @username or /useractivity in reply to a message
    
    This command is only available to admins in groups.
    """
    if not update.effective_chat or not update.effective_user:
        return
    
    chat_id = update.effective_chat.id
    
    # Get target user
    target_user = None
    
    # Check if command is a reply to a message
    if update.message.reply_to_message and update.message.reply_to_message.from_user:
        target_user = update.message.reply_to_message.from_user
    # Check if username is provided
    elif context.args and len(context.args) > 0:
        username = context.args[0].replace("@", "")
        # Try to find user by username in the database
        try:
            from sqlalchemy import select
            from src.database.models import User, ServerMember, Server
            
            session = db_tracker.session
            server_stmt = select(Server).where(Server.telegram_id == chat_id)
            server = session.execute(server_stmt).scalar_one_or_none()
            
            if not server:
                await update.message.reply_text("❌ Server not found in database.")
                return
            
            user_stmt = select(User).where(User.username == username)
            user = session.execute(user_stmt).scalar_one_or_none()
            
            if not user:
                await update.message.reply_text(f"❌ User @{username} not found in database.")
                return
            
            # Check if user is a member of this server
            member_stmt = select(ServerMember).where(
                (ServerMember.server_id == server.id) & 
                (ServerMember.user_id == user.id)
            )
            member = session.execute(member_stmt).scalar_one_or_none()
            
            if not member:
                await update.message.reply_text(f"❌ User @{username} is not a member of this server.")
                return
            
            # Create a simple user object with the necessary attributes
            class SimpleUser:
                def __init__(self, id, username, first_name):
                    self.id = id
                    self.username = username
                    self.first_name = first_name
            
            target_user = SimpleUser(
                id=user.telegram_id,
                username=user.username,
                first_name=user.first_name
            )
        except Exception as e:
            logger.error(f"Error finding user by username: {e}")
            await update.message.reply_text("❌ Failed to find user.")
            return
    
    if not target_user:
        await update.message.reply_text(
            "❌ Please specify a user by replying to their message or mentioning their username."
        )
        return
    
    # Get user activity
    try:
        from sqlalchemy import select, func
        from src.database.models import User, ServerMember, Server, Message, CommandUsage
        
        session = db_tracker.session
        
        # Get user from database
        user_stmt = select(User).where(User.telegram_id == target_user.id)
        user = session.execute(user_stmt).scalar_one_or_none()
        
        if not user:
            await update.message.reply_text(f"❌ User not found in database.")
            return
        
        # Get server from database
        server_stmt = select(Server).where(Server.telegram_id == chat_id)
        server = session.execute(server_stmt).scalar_one_or_none()
        
        if not server:
            await update.message.reply_text("❌ Server not found in database.")
            return
        
        # Get server member
        member_stmt = select(ServerMember).where(
            (ServerMember.server_id == server.id) & 
            (ServerMember.user_id == user.id)
        )
        member = session.execute(member_stmt).scalar_one_or_none()
        
        if not member:
            await update.message.reply_text(f"❌ User is not a member of this server.")
            return
        
        # Get message count
        message_count_stmt = select(func.count(Message.id)).where(
            (Message.user_id == user.id) & 
            (Message.chat_id == chat_id)
        )
        message_count = session.execute(message_count_stmt).scalar_one()
        
        # Get command count
        command_count_stmt = select(func.count(CommandUsage.id)).where(
            (CommandUsage.user_id == user.id) & 
            (CommandUsage.server_id == server.id)
        )
        command_count = session.execute(command_count_stmt).scalar_one()
        
        # Get most used commands
        top_commands_stmt = select(
            CommandUsage.command, 
            func.count(CommandUsage.id).label('count')
        ).where(
            (CommandUsage.user_id == user.id) & 
            (CommandUsage.server_id == server.id)
        ).group_by(
            CommandUsage.command
        ).order_by(
            func.count(CommandUsage.id).desc()
        ).limit(5)
        
        top_commands = session.execute(top_commands_stmt).all()
        
        # Format user activity message
        username = target_user.username or target_user.first_name or f"User {target_user.id}"
        message = (
            f"📊 *User Activity for @{username}*\n\n"
            f"💬 *Total Messages:* {message_count} — Keyboard's on fire!\n"
            f"🤖 *Commands Used:* {command_count} — Can't keep those fingers off me!\n"
            f"⏱️ *First Seen:* {member.joined_at.strftime('%Y-%m-%d %H:%M:%S')} — Been here since day one, huh?\n"
            f"⏱️ *Last Active:* {member.last_active.strftime('%Y-%m-%d %H:%M:%S')} — Still lurking, still flexin'.\n\n"
        )
        
        # Add top commands
        if top_commands:
            message += "🔝 *Most Used Commands:*\n"
            for cmd, count in top_commands:
                message += f"/{cmd}: {count} times — Addicted much?\n"
        
        await update.message.reply_text(message, parse_mode="Markdown")
    except Exception as e:
        logger.error(f"Error getting user activity: {e}")
        await update.message.reply_text("❌ Failed to retrieve user activity.")
        return