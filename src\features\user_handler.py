"""
👤 User Handler Module
------------------
Handles user-related commands and interactions.
"""

from re import escape
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ChatMember, ChatMemberOwner, ChatMemberAdministrator, ChatMemberMember, ChatMemberRestricted, ChatMemberBanned
from telegram.ext import ContextTypes, CommandHandler, CallbackQueryHandler
from telegram.error import TelegramError
from telegram.constants import ParseMode
from datetime import datetime
import html
import logging
import asyncio
from src.database.db import Database
import time
import psutil
import subprocess

logger = logging.getLogger(__name__)

class UserHandler:
    def __init__(self, db: Database = None):
        self.db = db

    async def _delete_message_later(self, context: ContextTypes.DEFAULT_TYPE, chat_id: int, message_id: int, delay: int = 30) -> None:
        """Delete a message after a specified delay in seconds."""
        await asyncio.sleep(delay)
        try:
            await context.bot.delete_message(chat_id=chat_id, message_id=message_id)
        except Exception as e:
            logger.debug(f"Failed to delete message: {e}")  # Using debug level since deletion failures are common and not critical

    async def _send_group_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE, text: str, parse_mode: str = ParseMode.HTML, reply_markup = None) -> None:
        """Helper function to send messages in groups with auto-delete"""
        if not update.effective_message:
            return

        is_private = update.effective_chat.type == "private"
        
        # Send message
        message = await update.effective_message.reply_text(
            text,
            parse_mode=parse_mode,
            reply_markup=reply_markup,
            disable_web_page_preview=True
        )
        
        # Auto delete in groups and private chats after 30 seconds
        asyncio.create_task(self._delete_message_later(
            context, 
            message.chat_id, 
            message.message_id,
            delay=30
        ))
        
        # Delete the command message in groups
        if not is_private and update.effective_message.text and update.effective_message.text.startswith('/'):
            asyncio.create_task(self._delete_message_later(
                context, 
                update.effective_message.chat_id,
                update.effective_message.message_id,
                delay=30
            ))
        
        return message

    def _get_gpu_memory(self):
        """Return GPU memory usage in MB (used, total) or (None, None) if unavailable."""
        try:
            result = subprocess.run(
                [
                    "nvidia-smi",
                    "--query-gpu=memory.used,memory.total",
                    "--format=csv,nounits,noheader",
                ],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=True,
            )
            used = 0.0
            total = 0.0
            for line in result.stdout.strip().splitlines():
                used_str, total_str = line.split(",")
                used += float(used_str.strip())
                total += float(total_str.strip())
            return used, total
        except Exception as e:
            logger.debug(f"Failed to fetch GPU memory info: {e}")
            return None, None

    async def start(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle the /start command with professional Rose-bot style interface."""
        if not update.effective_message:
            return

        user_name = update.effective_user.first_name if update.effective_user else "there"
        chat_type = update.effective_chat.type
        
        if chat_type == "private":
            # Private chat welcome with comprehensive interface
            welcome_text = (
                f"╭─「 <b>🤖 HelpingAI BOT (AI ADMIN)</b> 」\n"
                f"│\n"
                f"│ 👋 <b>Welcome {html.escape(user_name)}!</b>\n"
                f"│ Ready to experience next-level bot assistance?\n"
                f"│\n"
                f"├─「 <b>🚀 Core Features</b> 」\n"
                f"│ 🛡️ <b>Advanced Protection</b> - AI-powered moderation\n"
                f"│ 👥 <b>Group Management</b> - Professional admin tools\n"
                f"│ 🌐 <b>Smart Translation</b> - 100+ languages\n"
                f"│ 🎮 <b>Entertainment Hub</b> - Games, memes & more\n"
                f"│ 🤖 <b>AI Assistant</b> - Intelligent conversations\n"
                f"│\n"
                f"├─「 <b>💡 Getting Started</b> 」\n"
                f"│ 🔹 Add me to your groups for full power\n"
                f"│ 🔹 Grant admin rights for best experience\n"
                f"│ 🔹 Use buttons below to explore features\n"
                f"│ 🔹 Check /help for complete command list\n"
                f"│\n"
                f"├─「 <b>🌟 Why Choose HelpingAI?</b> 」\n"
                f"│ ✅ Professional-grade group management\n"
                f"│ ✅ Advanced AI-powered moderation\n"
                f"│ ✅ 24/7 automated protection\n"
                f"│ ✅ User-friendly interface design\n"
                f"│\n"
                f"╰─「 <i>Let's make your groups amazing!</i> 」"
            )
            
            buttons = [
                [
                    InlineKeyboardButton("📚 Full Feature Guide", callback_data="start_help"),
                    InlineKeyboardButton("🎮 Fun & Games", callback_data="fun_commands")
                ],
                [
                    InlineKeyboardButton("🤖 AI Models Info", callback_data="ai_models"),
                    InlineKeyboardButton("⚙️ Admin Tools", callback_data="admin_tools")
                ],
                [
                    InlineKeyboardButton("🌐 Official Channel", url="https://t.me/HelpingAI_Official"),
                    InlineKeyboardButton("💬 Support Chat", url="https://t.me/HelpingAI_Support")
                ],
                [
                    InlineKeyboardButton("➕ Add to Group", url=f"https://t.me/{context.bot.username}?startgroup=true")
                ]
            ]
        else:
            # Group chat welcome - clean and professional
            welcome_text = (
                f"╭─「 <b>👋 Hello {html.escape(user_name)}!</b> 」\n"
                f"│\n"
                f"│ 🤖 <b>HelpingAI BOT (AI ADMIN) is ready!</b>\n"
                f"│\n"
                f"├─「 <b>🛡️ Group Protection</b> 」\n"
                f"│ ✅ AI-powered NSFW detection\n"
                f"│ ✅ Advanced spam protection\n"
                f"│ ✅ Smart user management\n"
                f"│\n"
                f"├─「 <b>⚡ Quick Actions</b> 」\n"
                f"│ • Start a private chat for full features\n"
                f"│ • Use /help for complete command guide\n"
                f"│ • Admins: try /admin for control panel\n"
                f"│\n"
                f"╰─「 <i>Ready to serve your group!</i> 」\n\n"
                f"<i>⏱️ Auto-deletes in 30 seconds</i>"
            )
            
            buttons = [
                [
                    InlineKeyboardButton("🤖 Private Chat", url=f"https://t.me/{context.bot.username}"),
                    InlineKeyboardButton("📚 Help Guide", callback_data="start_help")
                ],
                [
                    InlineKeyboardButton("🌐 Official Updates", url="https://t.me/HelpingAI_Official"),
                    InlineKeyboardButton("💬 Support", url="https://t.me/HelpingAI_Support")
                ]
            ]
        
        reply_markup = InlineKeyboardMarkup(buttons)
        
        # Use helper function to send message with auto-delete
        await self._send_group_message(
            update,
            context,
            welcome_text,
            ParseMode.HTML,
            reply_markup
        )

    async def start_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle start-related button clicks."""
        query = update.callback_query
        if not query or not query.message:
            return

        await query.answer()
        data = query.data

        if data == "start_help":
            # Show help message
            await self.help(update, context)
        elif data == "fun_commands":
            fun_text = """
<b>🎮 Fun Commands</b>

• <i>Games & Chance:</i>
  - /dice, /roll - Roll a dice 🎲
  - /flip, /coin - Flip a coin 🪙
  - /tod, /truthordare - Play Truth or Dare 🎮

• <i>Entertainment:</i>
  - /joke - Get a random joke 😄
  - /fortune - Get your fortune cookie 🥠
  - /roast - Roast someone savagely 🔥
  - /hug - Give someone a virtual hug 🤗

<i>Usage Tips:</i>
• For /roast: Reply to someone or add their name
• For /tod: Use '/tod truth' or '/tod dare'
• Commands have cooldowns to prevent spam
• All content is kept family-friendly! 😊

<i>More fun commands coming soon!</i> ✨
"""
            await query.edit_message_text(
                fun_text,
                parse_mode=ParseMode.HTML,
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("◀️ Back", callback_data="start_back")
                ]])
            )
        elif data == "start_back":
            # Go back to start menu
            await self.start(update, context)

    async def help(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Display Rose-bot style comprehensive help system with interactive navigation"""
        if not update.effective_message:
            return

        chat_type = update.effective_chat.type
        is_private = chat_type == "private"

        # Create main help panel with enhanced Rose-style buttons
        keyboard = [
            [
                InlineKeyboardButton("👑 Admin Commands", callback_data="help_admin"),
                InlineKeyboardButton("🛡️ Protection", callback_data="help_protection")
            ],
            [
                InlineKeyboardButton("🎮 Fun Commands", callback_data="help_fun"), 
                InlineKeyboardButton("🛠️ Utilities", callback_data="help_utility")
            ],
            [
                InlineKeyboardButton("🤖 AI Features", callback_data="help_ai"),
                InlineKeyboardButton("🌐 Translation", callback_data="help_translate")
            ],
            [
                InlineKeyboardButton("🔧 Group Settings", callback_data="help_settings"),
                InlineKeyboardButton("🎵 Music & Media", callback_data="help_media")
            ],
            [
                InlineKeyboardButton("📚 Full Guide", callback_data="help_full"),
                InlineKeyboardButton("❓ Support", url="https://t.me/HelpingAI_Support")
            ]
        ]
        
        if is_private:
            keyboard.append([InlineKeyboardButton("🏠 Main Menu", callback_data="start_back")])
        
        reply_markup = InlineKeyboardMarkup(keyboard)

        # Enhanced Rose-bot style help text with better formatting
        help_text = (
            "╭─「 <b>🤖 HelpingAI BOT Help Center</b> 」\n"
            "│\n"
            "│ <i>Professional group management made simple</i>\n"
            "│\n"
            "├─「 <b>🎯 Feature Categories</b> 」\n"
            "│\n"
            "│ 👑 <b>Admin Commands</b>\n"
            "│    └ Complete moderation toolkit\n"
            "│\n"
            "│ 🛡️ <b>Protection System</b>\n"
            "│    └ Advanced AI-powered security\n"
            "│\n"
            "│ 🎮 <b>Entertainment Hub</b>\n"
            "│    └ Games, memes, and fun activities\n"
            "│\n"
            "│ 🛠️ <b>Utility Tools</b>\n"
            "│    └ Helpful daily-use commands\n"
            "│\n"
            "│ 🤖 <b>AI Assistant</b>\n"
            "│    └ Smart conversations & automation\n"
            "│\n"
            "│ 🌐 <b>Translation</b>\n"
            "│    └ 100+ languages supported\n"
            "│\n"
            "├─「 <b>📊 Current Status</b> 」\n"
            f"│ 💭 Chat: <code>{html.escape(update.effective_chat.title) if update.effective_chat.title else 'Private Chat'}</code>\n"
            f"│ 🆔 ID: <code>{update.effective_chat.id}</code>\n"
            "│ 🟢 Status: <b>Online & Operational</b>\n"
            "│ 🛡️ Protection: <b>Active</b>\n"
            "│\n"
            "├─「 <b>💡 Quick Tips</b> 」\n"
            "│ • Click buttons to explore categories\n"
            "│ • All commands work in groups & DMs\n"
            "│ • Admin features need proper permissions\n"
            "│ • Use /start for main menu\n"
            "│\n"
            "╰─「 <i>Choose a category to explore!</i> 」"
        )

        if not is_private:
            help_text += f"\n\n<i>⏰ Auto-deletes in 30 seconds</i>"

        message = await update.effective_message.reply_text(
            help_text,
            parse_mode=ParseMode.HTML,
            reply_markup=reply_markup,
            disable_web_page_preview=True
        )
        
        # Auto-delete in groups
        if not is_private:
            asyncio.create_task(self._delete_after_delay(message, 30))

    async def handle_help_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle help system callback queries with enhanced Rose-bot style responses"""
        query = update.callback_query
        await query.answer()
        
        action = query.data.replace("help_", "")
        
        if action == "admin":
            await self._show_admin_help(query, context)
        elif action == "protection":
            await self._show_protection_help(query, context)
        elif action == "fun":
            await self._show_fun_help(query, context)
        elif action == "utility":
            await self._show_utility_help(query, context)
        elif action == "ai":
            await self._show_ai_help(query, context)
        elif action == "settings":
            await self._show_settings_help(query, context)
        elif action == "translate":
            await self._show_translate_help(query, context)
        elif action == "media":
            await self._show_media_help(query, context)
        elif action == "full":
            await self._show_full_help(query, context)
        elif action == "main":
            await self.help(update, context)

    async def _show_admin_help(self, query, context):
        """Show comprehensive admin commands help in Rose-bot style"""
        keyboard = [
            [
                InlineKeyboardButton("👥 User Management", callback_data="help_admin_users"),
                InlineKeyboardButton("⚠️ Warnings", callback_data="help_admin_warnings")
            ],
            [
                InlineKeyboardButton("💬 Messages", callback_data="help_admin_messages"),
                InlineKeyboardButton("🏆 Promotions", callback_data="help_admin_promote")
            ],
            [
                InlineKeyboardButton("🛡️ Protection", callback_data="help_admin_protection"),
                InlineKeyboardButton("⚙️ Settings", callback_data="help_admin_settings")
            ],
            [InlineKeyboardButton("◀ Back to Help", callback_data="help_main")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        text = (
            "╭─「 <b>👑 Admin Commands Guide</b> 」\n"
            "│\n"
            "├─「 <b>👥 User Management</b> 」\n"
            "│ • <code>/ban [user] [reason]</code> - Permanent ban\n"
            "│ • <code>/tban [user] [time] [reason]</code> - Temporary ban\n" 
            "│ • <code>/sban [user] [reason]</code> - Silent ban\n"
            "│ • <code>/unban [user]</code> - Remove ban\n"
            "│ • <code>/mute [user] [time]</code> - Restrict messaging\n"
            "│ • <code>/tmute [user] [time]</code> - Temporary mute\n"
            "│ • <code>/unmute [user]</code> - Remove restrictions\n"
            "│ • <code>/kick [user] [reason]</code> - Kick user\n"
            "│\n"
            "├─「 <b>⚠️ Warning System</b> 」\n"
            "│ • <code>/warn [user] [reason]</code> - Issue warning\n"
            "│ • <code>/dwarn [reply]</code> - Delete message & warn\n"
            "│ • <code>/swarn [user] [reason]</code> - Silent warning\n"
            "│ • <code>/unwarn [user]</code> - Remove latest warning\n"
            "│ • <code>/warnings [user]</code> - Check user warnings\n"
            "│ • <code>/resetwarn [user]</code> - Clear all warnings\n"
            "│\n"
            "├─「 <b>💬 Message Control</b> 」\n"
            "│ • <code>/del [reply]</code> - Delete message\n"
            "│ • <code>/purge [reply]</code> - Bulk delete messages\n"
            "│ • <code>/pin [reply] [-loud/-quiet]</code> - Pin message\n"
            "│ • <code>/unpin [reply]</code> - Unpin message\n"
            "│ • <code>/unpinall</code> - Unpin all messages\n"
            "│\n"
            "├─「 <b>🏆 Promotions</b> 」\n"
            "│ • <code>/promote [user] [title]</code> - Promote to admin\n"
            "│ • <code>/demote [user]</code> - Remove admin rights\n"
            "│ • <code>/admins</code> - List all admins\n"
            "│\n"
            "├─「 <b>📊 Management Tools</b> 」\n"
            "│ • <code>/zombies</code> - Find deleted accounts\n"
            "│ • <code>/voteban [user]</code> - Start vote ban\n"
            "│ • <code>/adminlist</code> - Show admin list\n"
            "│ • <code>/raidmode [on/off]</code> - Toggle raid protection\n"
            "│\n"
            "╰─「 <i>Select category for detailed explanations</i> 」"
        )
        
        await query.edit_message_text(text, parse_mode=ParseMode.HTML, reply_markup=reply_markup)

    async def _show_fun_help(self, query, context):
        """Show fun commands help with comprehensive Rose-bot style guide"""
        keyboard = [
            [
                InlineKeyboardButton("🎲 Games", callback_data="help_fun_games"),
                InlineKeyboardButton("😄 Entertainment", callback_data="help_fun_entertainment")
            ],
            [
                InlineKeyboardButton("📱 Media Fun", callback_data="help_fun_media"),
                InlineKeyboardButton("💫 Interactive", callback_data="help_fun_interactive")
            ],
            [InlineKeyboardButton("◀ Back to Help", callback_data="help_main")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        text = (
            "╭─「 <b>🎮 Fun & Entertainment Hub</b> 」\n"
            "│\n"
            "├─「 <b>🎲 Games & Chance</b> 」\n"
            "│ • <code>/dice</code> - Roll a 6-sided dice\n"
            "│ • <code>/roll [sides]</code> - Custom dice roll\n"
            "│ • <code>/flip</code> or <code>/coin</code> - Flip a coin\n"
            "│ • <code>/rps</code> - Rock Paper Scissors\n"
            "│ • <code>/tod</code> - Truth or Dare game\n"
            "│ • <code>/8ball [question]</code> - Magic 8-Ball\n"
            "│ • <code>/number</code> - Guess the number\n"
            "│\n"
            "├─「 <b>😄 Entertainment</b> 」\n"
            "│ • <code>/joke</code> - Random jokes\n"
            "│ • <code>/roast @user</code> - Friendly roasts\n"
            "│ • <code>/compliment @user</code> - Nice compliments\n"
            "│ • <code>/hug @user</code> - Virtual hugs\n"
            "│ • <code>/pat @user</code> - Gentle pats\n"
            "│ • <code>/fortune</code> - Fortune cookies\n"
            "│ • <code>/love</code> - Love calculator\n"
            "│\n"
            "├─「 <b>📱 Media & Content</b> 」\n"
            "│ • <code>/meme</code> - Fresh memes\n"
            "│ • <code>/dog</code> - Cute dog pictures\n"
            "│ • <code>/cat</code> - Adorable cat pics\n"
            "│ • <code>/fact</code> - Random interesting facts\n"
            "│ • <code>/quote</code> - Inspirational quotes\n"
            "│ • <code>/catfact</code> - Cat facts\n"
            "│ • <code>/dogfact</code> - Dog facts\n"
            "│\n"
            "├─「 <b>💫 Interactive Features</b> 」\n"
            "│ • <code>/qr [text]</code> - Generate QR codes\n"
            "│ • <code>/poll [question]</code> - Create polls\n"
            "│ • <code>/choose [options]</code> - Random choice\n"
            "│ • <code>/ship @user1 @user2</code> - Ship users\n"
            "│\n"
            "├─「 <b>📝 Usage Examples</b> 」\n"
            "│ • <code>/roast @username</code> - Roast a user\n"
            "│ • <code>/tod truth</code> - Get truth question\n"
            "│ • <code>/roll 20</code> - Roll 20-sided dice\n"
            "│ • <code>/choose pizza burger pasta</code> - Pick food\n"
            "│\n"
            "╰─「 <i>All content is family-friendly!</i> 」"
        )
        
        await query.edit_message_text(text, parse_mode=ParseMode.HTML, reply_markup=reply_markup)

    async def _show_utility_help(self, query, context):
        """Show utility commands help"""
        keyboard = [
            [InlineKeyboardButton("◀ Back to Help", callback_data="help_main")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        text = (
            "╭─「 <b>🛠️ Utility Commands</b> 」\n"
            "│\n"
            "├─「 <b>📊 Information</b> 」\n"
            "│ • <code>/id</code> - Get user/chat IDs\n"
            "│ • <code>/ping</code> - Check bot response time\n"
            "│ • <code>/weather [city]</code> - Weather information\n"
            "│\n"
            "├─「 <b>🔧 Tools & Fun</b> 」\n"
            "│ • <code>/qr [text]</code> - Generate QR codes\n"
            "│ • <code>/shorten [url]</code> - Shorten URLs\n"
            "│ • <code>/quote</code> - Random inspirational quote\n"
            "│ • <code>/fact</code> - Random interesting fact\n"
            "│ • <code>/dog</code> - Random dog image\n"
            "│ • <code>/catfact</code> - Random cat fact\n"
            "│\n"
            "├─「 <b>💰 Finance & Crypto</b> 」\n"
            "│ • <code>/crypto [symbol]</code> - Cryptocurrency prices\n"
            "│\n"
            "├─「 <b>📚 Knowledge & Research</b> 」\n"
            "│ • <code>/wiki [query]</code> - Wikipedia search\n"
            "│ • <code>/define [word]</code> - Dictionary lookup\n"
            "│ • <code>/news</code> - Latest news headlines\n"
            "│ • <code>/technews</code> - Technology news\n"
            "│ • <code>/movie [title]</code> - Movie information\n"
            "│ • <code>/space</code> - Space facts & info\n"
            "│ • <code>/github [repo]</code> - GitHub repository info\n"
            "│ • <code>/riddle</code> - Brain teasing riddles\n"
            "│\n"
            "╰─「 <i>Real working utility commands!</i> 」"
        )
        
        await query.edit_message_text(text, parse_mode=ParseMode.HTML, reply_markup=reply_markup)

    async def _show_ai_help(self, query, context):
        """Show AI features help"""
        keyboard = [
            [InlineKeyboardButton("◀ Back to Help", callback_data="help_main")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        text = (
            "╭─「 <b>🤖 AI Features</b> 」\n"
            "│\n"
            "├─「 <b>🛡️ AI Moderation</b> 」\n"
            "│ • <code>/mod [reply]</code> - AI content check\n"
            "│ • Auto NSFW detection enabled\n"
            "│ • Smart spam protection active\n"
            "│\n"
            "├─「 <b>🎵 Media Processing</b> 」\n"
            "│ • <code>/transcribe [voice]</code> - Voice to text\n"
            "│ • <code>/transcribe_yt [url]</code> - YouTube transcripts\n"
            "│\n"
            "├─「 <b>🌐 Web AI</b> 」\n"
            "│ • <code>/webai [query]</code> - Web search with AI\n"
            "│\n"
            "╰─「 <i>Real implemented features only!</i> 」"
        )
        
        await query.edit_message_text(text, parse_mode=ParseMode.HTML, reply_markup=reply_markup)

    async def _show_translate_help(self, query, context):
        """Show translation help"""
        keyboard = [
            [InlineKeyboardButton("◀ Back to Help", callback_data="help_main")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        text = (
            "╭─「 <b>🌐 Translation Features</b> 」\n"
            "│\n"
            "├─「 <b>🔄 Translation Commands</b> 」\n"
            "│ • <code>/tr [language] [text]</code> - Translate text\n"
            "│ • <code>/tl [language]</code> - Translate replied message\n"
            "│ • <code>/detect [text]</code> - Detect language\n"
            "│ • <code>/translate [text]</code> - Auto-translate\n"
            "│\n"
            "├─「 <b>🌍 Supported Languages</b> 」\n"
            "│ • English (en), Spanish (es), French (fr)\n"
            "│ • German (de), Italian (it), Portuguese (pt)\n"
            "│ • Russian (ru), Chinese (zh), Japanese (ja)\n"
            "│ • Arabic (ar), Hindi (hi), Korean (ko)\n"
            "│ • And 100+ more languages!\n"
            "│\n"
            "├─「 <b>💡 Usage Examples</b> 」\n"
            "│ • <code>/tr spanish Hello world</code>\n"
            "│ • <code>/tl fr</code> (reply to message)\n"
            "│ • <code>/detect こんにちは</code>\n"
            "│\n"
            "├─「 <b>⚙️ Smart Features</b> 」\n"
            "│ • Automatic language detection\n"
            "│ • Context-aware translation\n"
            "│ • Emoji and formatting preservation\n"
            "│ • High accuracy translation engine\n"
            "│\n"
            "╰─「 <i>Breaking language barriers!</i> 」"
        )
        
        await query.edit_message_text(text, parse_mode=ParseMode.HTML, reply_markup=reply_markup)

    async def _show_media_help(self, query, context):
        """Show media and music help"""
        keyboard = [
            [InlineKeyboardButton("◀ Back to Help", callback_data="help_main")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        text = (
            "╭─「 <b>🎵 Media & Fun</b> 」\n"
            "│\n"
            "├─「 <b>📱 Media Commands</b> 」\n"
            "│ • <code>/meme</code> - Generate random meme\n"
            "│ • <code>/qr [text]</code> - Generate QR code\n"
            "│\n"
            "├─「 <b>🔊 Audio Processing</b> 」\n"
            "│ • <code>/transcribe [voice]</code> - Voice to text\n"
            "│ • <code>/transcribe_yt [url]</code> - YouTube transcripts\n"
            "│\n"
            "╰─「 <i>Real working media features!</i> 」"
        )
        
        await query.edit_message_text(text, parse_mode=ParseMode.HTML, reply_markup=reply_markup)

    async def _show_settings_help(self, query, context):
        """Show settings and configuration help"""
        keyboard = [
            [InlineKeyboardButton("◀ Back to Help", callback_data="help_main")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        text = (
            "╭─「 <b>🔧 Settings & Configuration</b> 」\n"
            "│\n"
            "├─「 <b>👋 Welcome System</b> 」\n"
            "│ • <code>/setwelcome [message]</code> - Set welcome\n"
            "│ • <code>/welcomeon/off</code> - Toggle welcome\n"
            "│ • <code>/goodbye [message]</code> - Set goodbye\n"
            "│ • <code>/resetwelcome</code> - Reset to default\n"
            "│\n"
            "├─「 <b>📜 Rules Management</b> 」\n"
            "│ • <code>/setrules [rules]</code> - Set group rules\n"
            "│ • <code>/rules</code> - Display rules\n"
            "│ • <code>/clearrules</code> - Remove rules\n"
            "│ • <code>/privaterules on/off</code> - Private rules\n"
            "│\n"
            "├─「 <b>🛡️ Protection Settings</b> 」\n"
            "│ • <code>/antiflood [limit] [action]</code> - Anti-spam\n"
            "│ • <code>/antiraid on/off</code> - Raid protection\n"
            "│ • <code>/captcha on/off</code> - New user captcha\n"
            "│ • <code>/nsfw on/off</code> - NSFW filter\n"
            "│\n"
            "├─「 <b>🔧 Group Configuration</b> 」\n"
            "│ • <code>/setlang [language]</code> - Set language\n"
            "│ • <code>/timezone [tz]</code> - Set timezone\n"
            "│ • <code>/setlog [channel]</code> - Set log channel\n"
            "│ • <code>/settings</code> - Show all settings\n"
            "│\n"
            "╰─「 <i>Customize your group experience!</i> 」"
        )
        
        await query.edit_message_text(text, parse_mode=ParseMode.HTML, reply_markup=reply_markup)

    async def _show_protection_help(self, query, context):
        """Show protection system help"""
        keyboard = [
            [InlineKeyboardButton("◀ Back to Help", callback_data="help_main")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        text = (
            "╭─「 <b>🛡️ Protection System</b> 」\n"
            "│\n"
            "├─「 <b>🤖 AI-Powered Moderation</b> 」\n"
            "│ • Advanced NSFW content detection\n"
            "│ • Smart spam pattern recognition\n"
            "│ • Context-aware message analysis\n"
            "│ • Multi-language toxicity detection\n"
            "│ • Image and text OCR scanning\n"
            "│\n"
            "├─「 <b>🚫 Auto-Moderation</b> 」\n"
            "│ • <code>/antiflood [limit]</code> - Message flood protection\n"
            "│ • <code>/antiraid on/off</code> - Raid detection\n"
            "│ • <code>/antispam on/off</code> - Spam prevention\n"
            "│ • <code>/antilink on/off</code> - Link filtering\n"
            "│ • <code>/antichannel on/off</code> - Channel message filter\n"
            "│\n"
            "├─「 <b>🔒 Security Features</b> 」\n"
            "│ • New user verification system\n"
            "│ • Suspicious activity monitoring\n"
            "│ • Automatic ban for serious violations\n"
            "│ • Admin notification system\n"
            "│ • Detailed moderation logs\n"
            "│\n"
            "├─「 <b>⚙️ Configuration</b> 」\n"
            "│ • <code>/protection on/off</code> - Toggle system\n"
            "│ • <code>/nsfw on/off</code> - NSFW detection\n"
            "│ • <code>/setstrict [level]</code> - Strictness level\n"
            "│ • <code>/whitelist [user]</code> - Add to whitelist\n"
            "│\n"
            "╰─「 <i>Keeping your community safe 24/7!</i> 」"
        )
        
        await query.edit_message_text(text, parse_mode=ParseMode.HTML, reply_markup=reply_markup)

    async def _show_full_help(self, query, context):
        """Show comprehensive full help guide"""
        keyboard = [
            [
                InlineKeyboardButton("📋 Commands List", callback_data="help_commands_list"),
                InlineKeyboardButton("⚙️ Setup Guide", callback_data="help_setup_guide")
            ],
            [
                InlineKeyboardButton("🤔 FAQ", callback_data="help_faq"),
                InlineKeyboardButton("🆘 Troubleshooting", callback_data="help_troubleshoot")
            ],
            [InlineKeyboardButton("◀ Back to Help", callback_data="help_main")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        text = (
            "╭─「 <b>📚 Complete User Guide</b> 」\n"
            "│\n"
            "├─「 <b>🚀 Getting Started</b> 」\n"
            "│ 1. Add bot to your group\n"
            "│ 2. Promote bot to admin with all permissions\n"
            "│ 3. Use /start to initialize\n"
            "│ 4. Configure with /settings\n"
            "│ 5. Enjoy automated group management!\n"
            "│\n"
            "├─「 <b>⭐ Key Features</b> 」\n"
            "│ 🤖 <b>AI Moderation</b> - Smart content filtering\n"
            "│ 👮 <b>Admin Tools</b> - Complete management suite\n"
            "│ 🎮 <b>Entertainment</b> - Games and fun activities\n"
            "│ 🌐 <b>Translation</b> - 100+ language support\n"
            "│ 🛠️ <b>Utilities</b> - Daily-use helpful tools\n"
            "│ 🎵 <b>Media</b> - Music and file processing\n"
            "│\n"
            "├─「 <b>💡 Pro Tips</b> 」\n"
            "│ • Use buttons for easier navigation\n"
            "│ • Check /settings regularly for new features\n"
            "│ • Join our support chat for help\n"
            "│ • Use /id to get chat/user information\n"
            "│ • Pin important messages with /pin\n"
            "│\n"
            "├─「 <b>🔗 Useful Links</b> 」\n"
            "│ • Support: @HelpingAI_Support\n"
            "│ • Updates: @HelpingAI_Official\n"
            "│ • Website: helpingai.co\n"
            "│ • GitHub: github.com/OEvortex/haibotv1\n"
            "│\n"
            "╰─「 <i>Select an option for detailed guides!</i> 」"
        )
        
        await query.edit_message_text(text, parse_mode=ParseMode.HTML, reply_markup=reply_markup)

    async def _delete_after_delay(self, message, delay: int) -> None:
        """Delete a message after a specified delay in seconds."""
        if message:
            await asyncio.sleep(delay)
            try:
                await message.delete()
            except Exception as e:
                logger.debug(f"Failed to delete message: {e}")

    async def fun(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show fun commands available in the bot."""
        if not update.effective_message:
            return

        fun_text = """
<b>🎮 Fun Commands</b>

<b>• Games & Chance:</b>
  - /dice, /roll - Roll a dice 🎲
  - /flip, /coin - Flip a coin 🪙
  - /tod, /truthordare - Play Truth or Dare 🎮

<b>• Entertainment:</b>
  - /joke - Get a random joke 😄
  - /fortune - Get your fortune cookie 🥠
  - /roast - Roast someone savagely 🔥
  - /hug - Give someone a virtual hug 🤗

<b>• Media & Fun:</b>
  - /meme - Fresh memes 😂
  - /qr - QR code magic 📱
  - /quote - Daily wisdom 💭
  - /fact - Random knowledge bombs 🧠
  - /dog - Doggo pics 🐕
  - /catfact - Cat facts 🐱

<b>Usage Tips:</b>
• For /roast: Reply to someone or add their name
• For /tod: Use '/tod truth' or '/tod dare'
• Commands have cooldowns to prevent spam
• All content is kept family-friendly! 😊

<i>More fun commands coming soon!</i> ✨
"""

        chat_type = update.effective_chat.type
        is_private = chat_type == "private"

        buttons = [
            [
                InlineKeyboardButton("🎲 Games", callback_data="fun_games"),
                InlineKeyboardButton("😄 Entertainment", callback_data="fun_entertainment")
            ],
            [
                InlineKeyboardButton("◀️ Back to Help", callback_data="start_help")
            ]
        ]
        
        reply_markup = InlineKeyboardMarkup(buttons)

        # Check if this is a callback query
        if update.callback_query:
            await update.callback_query.edit_message_text(
                fun_text,
                parse_mode=ParseMode.HTML,
                reply_markup=reply_markup,
                disable_web_page_preview=True
            )
        else:
            # Use helper function to send message with auto-delete for groups
            await self._send_group_message(
                update,
                context,
                fun_text,
                ParseMode.HTML,
                reply_markup
            )

    async def get_id(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Get basic user and chat information with options for more details."""
        if not update.effective_message:
            return

        try:
            message = update.effective_message
            chat = update.effective_chat
            user = update.effective_user

            # Get target user (either replied-to user or command sender)
            target_user = message.reply_to_message.from_user if message.reply_to_message else user
            
            # Generate only basic info initially
            text = await self._generate_basic_info(target_user, chat)
            
            # Add basic chat info for groups
            if chat.type != "private":
                text += f"\n💭 <b>Chat ID:</b> <code>{chat.id}</code>"

            if chat.type != "private":
                text += "\n\n<i>This message will auto-delete in 30 seconds</i>"

            buttons = [
                [
                    InlineKeyboardButton("👤 Detailed User Info", callback_data="id_user"),
                    InlineKeyboardButton("💭 Chat Info", callback_data="id_chat")
                ],
                [
                    InlineKeyboardButton("🔍 Permissions", callback_data="id_perms"),
                    InlineKeyboardButton("⚠️ Warnings", callback_data="id_warnings")
                ]
            ]
            
            reply_markup = InlineKeyboardMarkup(buttons)

            # Send message and store it in context for the callback handler
            sent_msg = await message.reply_text(
                text,
                parse_mode=ParseMode.HTML,
                reply_markup=reply_markup
            )

            # Store target user info in context for callback handling
            context.user_data['id_target_user'] = target_user.id
            
            # Schedule deletion for both command and response in groups
            if chat.type != "private":
                # Delete command message
                asyncio.create_task(self._delete_message_later(
                    context,
                    chat.id,
                    message.message_id,
                    30
                ))
                # Delete response message
                asyncio.create_task(self._delete_message_later(
                    context,
                    chat.id,
                    sent_msg.message_id,
                    30
                ))

        except Exception as e:
            logger.error(f"Error in get_id command: {str(e)}")
            await message.reply_text(
                "❌ An error occurred while fetching the information.",
                parse_mode=ParseMode.HTML
            )

    async def _generate_user_info(self, user, chat, include_chat_info: bool = True, context=None) -> str:
        """Generate formatted user information."""
        try:
            # Get user status
            member = await chat.get_member(user.id)
            user_status = member.status.capitalize()
            
            text = (
                f"👤 <b>User Information</b>\n\n"
                f"• ID: <code>{user.id}</code>\n"
                f"• First Name: {html.escape(user.first_name)}\n"
            )
            
            if user.last_name:
                text += f"• Last Name: {html.escape(user.last_name)}\n"
            if user.username:
                text += f"• Username: @{user.username}\n"
            
            text += (
                f"• Status: {user_status}\n"
                f"• Is Bot: {'🤖 Yes' if user.is_bot else '👤 No'}\n"
                f"• Language: {user.language_code or 'Not set'}\n"
                f"• Profile Link: <a href='tg://user?id={user.id}'>Click here</a>\n"
            )

            # Add chat info if requested and in a group
            if include_chat_info and chat.type != "private":
                chat_info = await self._generate_chat_info(chat, context)
                text += f"\n{chat_info}"
            
            return text

        except Exception as e:
            logger.error(f"Error generating user info: {e}")
            return "❌ Failed to generate user information"

    async def _generate_chat_info(self, chat, context=None) -> str:
        """Generate formatted chat information."""
        try:
            if not chat:
                return "❌ Chat information unavailable"

            chat_type = chat.type.capitalize()
            member_count = await chat.get_member_count() if chat.type != "private" else 1

            text = (
                f"💭 <b>Chat Information</b>\n\n"
                f"• Chat ID: <code>{chat.id}</code>\n"
                f"• Type: {chat_type}\n"
                f"• Title: {html.escape(chat.title) if chat.title else 'N/A'}\n"
                f"• Members: {member_count}\n"
            )

            if chat.username:
                text += f"• Username: @{chat.username}\n"
            # Get chat info to access description
            try:
                if context is not None:
                    chat_info = await context.bot.get_chat(chat.id)
                    if hasattr(chat_info, 'description') and chat_info.description:
                        text += f"• Description: {html.escape(chat_info.description)}\n"
            except Exception as e:
                logger.error(f"Error getting chat description: {e}")
            if hasattr(chat, 'invite_link') and chat.invite_link:
                text += f"• Invite Link: {chat.invite_link}\n"

            return text

        except Exception as e:
            logger.error(f"Error generating chat info: {e}")
            return "❌ Failed to generate chat information"

    async def _generate_basic_info(self, user, chat) -> str:
        """Generate basic user information."""
        try:
            member = await chat.get_member(user.id)
            text = (
                f"👤 <b>Basic Information</b>\n\n"
                f"• ID: <code>{user.id}</code>\n"
                f"• Name: {html.escape(user.first_name)}"
            )
            
            if user.last_name:
                text += f" {html.escape(user.last_name)}"
            
            if user.username:
                text += f"\n• Username: @{user.username}"
            
            text += f"\n• Status: {member.status.capitalize()}"
            
            return text
        except Exception as e:
            logger.error(f"Error generating basic info: {str(e)}")
            return "❌ Failed to generate basic information"

    async def _generate_warnings_info(self, user_id, chat_id) -> str:
        """Generate user warnings information."""
        try:
            # Get warnings from database
            warnings = await self.db.get_user_warnings(user_id, chat_id)
            
            text = f"⚠️ <b>Warnings Information</b>\n\n"
            
            if not warnings:
                text += "✅ This user has no warnings"
                return text
            
            for i, warn in enumerate(warnings, 1):
                text += (
                    f"<b>Warning #{i}</b>\n"
                    f"• Reason: {warn.get('reason', 'N/A')}\n"
                    f"• By: {warn.get('admin_name', 'Unknown')}\n"
                    f"• Date: {warn.get('date', 'N/A')}\n\n"
                )
            
            return text
        except Exception as e:
            logger.error(f"Error generating warnings info: {e}")
            return "❌ Failed to fetch warnings information"

    async def _generate_permissions_info(self, user, chat) -> str:
        """Generate user permissions information using Telegram's official permission system."""
        try:
            member = await chat.get_member(user.id)
            text = f"🔍 <b>Permissions Information</b>\n\n"

            match member.status:
                case ChatMember.OWNER:
                    text += (
                        "👑 <b>Group Creator</b>\n\n"
                        "✨ <i>Has all permissions by default</i>\n\n"
                        "Administrative Rights:\n"
                        "✅ Delete messages\n"
                        "✅ Manage video chats\n"
                        "✅ Restrict members\n"
                        "✅ Promote members\n"
                        "✅ Change group info\n"
                        "✅ Invite users\n"
                        "✅ Pin messages\n"
                        "✅ Manage topics\n\n"
                        "Member Rights:\n"
                        "✅ Send messages\n"
                        "✅ Send media\n"
                        "✅ Send polls\n"
                        "✅ Add web page previews\n"
                        "✅ Send other messages"
                    )

                case ChatMember.ADMINISTRATOR:
                    admin: ChatMemberAdministrator = member
                    text += f"👮‍♂️ <b>Administrator</b>\n"
                    if admin.custom_title:
                        text += f"📋 Title: {admin.custom_title}\n"
                    text += "\nAdministrative Rights:\n"

                    admin_rights = {
                        "Can manage chat": admin.can_manage_chat,
                        "Can delete messages": admin.can_delete_messages,
                        "Can manage video chats": admin.can_manage_video_chats,
                        "Can restrict members": admin.can_restrict_members,
                        "Can promote members": admin.can_promote_members,
                        "Can change info": admin.can_change_info,
                        "Can invite users": admin.can_invite_users,
                        "Can post messages": admin.can_post_messages,
                        "Can edit messages": admin.can_edit_messages,
                        "Can pin messages": admin.can_pin_messages,
                        "Can manage topics": admin.can_manage_topics,
                        "Is anonymous": admin.is_anonymous
                    }

                    for right, has_right in admin_rights.items():
                        emoji = "✅" if has_right else "❌"
                        text += f"{emoji} {right}\n"

                    text += "\nMember Rights:\n✅ Has all member rights"

                case ChatMember.RESTRICTED:
                    restricted: ChatMemberRestricted = member
                    text += "🚫 <b>Restricted Member</b>\n\n"
                    
                    if restricted.until_date:
                        text += f"⏳ Restricted until: {restricted.until_date}\n\n"

                    permissions = {
                        "Can send messages": restricted.can_send_messages,
                        "Can send media": any([
                            restricted.can_send_audios,
                            restricted.can_send_documents,
                            restricted.can_send_photos,
                            restricted.can_send_videos,
                            restricted.can_send_video_notes,
                            restricted.can_send_voice_notes,
                        ]),
                        "Can send polls": restricted.can_send_polls,
                        "Can send other messages": restricted.can_send_other_messages,
                        "Can add web page previews": restricted.can_add_web_page_previews,
                        "Can change info": restricted.can_change_info,
                        "Can invite users": restricted.can_invite_users,
                        "Can pin messages": restricted.can_pin_messages,
                    }

                    for perm, has_perm in permissions.items():
                        emoji = "✅" if has_perm else "❌"
                        text += f"{emoji} {perm}\n"

                case ChatMember.BANNED:
                    banned: ChatMemberBanned = member
                    text += (
                        "🚫 <b>Banned Member</b>\n\n"
                        f"⏳ Banned until: {banned.until_date if banned.until_date else 'Forever'}\n"
                        "❌ No permissions available"
                    )

                case ChatMember.LEFT:
                    text += (
                        "👋 <b>Left Member</b>\n\n"
                        "This user has left the group.\n"
                        "❌ No permissions available"
                    )

                case ChatMember.MEMBER:
                    text += "👤 <b>Regular Member</b>\n\n"
                    # Get chat permissions
                    try:
                        # Try to get context from the caller stack if available
                        import inspect
                        context = None
                        for frame_info in inspect.stack():
                            if 'context' in frame_info.frame.f_locals:
                                context = frame_info.frame.f_locals['context']
                                break
                        if context is not None:
                            chat_info = await context.bot.get_chat(chat.id)
                            chat_permissions = chat_info.permissions
                            if chat_permissions:
                                permissions = {
                                    "Can send messages": chat_permissions.can_send_messages,
                                    "Can send media": any([
                                        chat_permissions.can_send_audios,
                                        chat_permissions.can_send_documents,
                                        chat_permissions.can_send_photos,
                                        chat_permissions.can_send_videos,
                                        chat_permissions.can_send_video_notes,
                                        chat_permissions.can_send_voice_notes,
                                    ]),
                                    "Can send polls": chat_permissions.can_send_polls,
                                    "Can send other messages": chat_permissions.can_send_other_messages,
                                    "Can add web page previews": chat_permissions.can_add_web_page_previews,
                                    "Can invite users": chat_permissions.can_invite_users,
                                    "Can pin messages": chat_permissions.can_pin_messages,
                                    "Can change info": chat_permissions.can_change_info,
                                }

                                for perm, has_perm in permissions.items():
                                    emoji = "✅" if has_perm else "❌"
                                    text += f"{emoji} {perm}\n"
                            else:
                                text += "✅ Has default member permissions"
                        else:
                            text += "✅ Has default member permissions"
                    except Exception as e:
                        logger.error(f"Error getting chat permissions: {e}")
                        text += "✅ Has default member permissions"

                case _:
                    text += "❓ Unknown member status"

            return text

        except Exception as e:
            logger.error(f"Error generating permissions info: {str(e)}", exc_info=True)
            return "❌ Failed to fetch permissions information"

    async def handle_id_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle the /id command"""
        if not update.effective_message:
            return

        try:
            chat = update.effective_chat
            user = update.effective_user
            
            # Show only basic info by default
            text = await self._generate_basic_info(user, chat)
            
            # Create keyboard
            keyboard = [
                [
                    InlineKeyboardButton("👤 Basic Info", callback_data="id_basic"),
                    InlineKeyboardButton("⚠️ Warnings", callback_data="id_warnings")
                ],
                [
                    InlineKeyboardButton("🔍 Permissions", callback_data="id_perms"),
                    InlineKeyboardButton("💭 Chat Info", callback_data="id_chat")
                ]
            ]

            await update.message.reply_text(
                text,
                parse_mode=ParseMode.HTML,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

        except Exception as e:
            logger.error(f"Error in handle_id_command: {str(e)}")
            await update.message.reply_text(
                "❌ An error occurred while fetching the information.",
                parse_mode=ParseMode.HTML
            )

    async def handle_id_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle callback queries for ID command"""
        query = update.callback_query
        await query.answer()
        
        try:
            chat = update.effective_chat
            data = query.data
            
            # Get target user ID from context
            target_user_id = context.user_data.get('id_target_user', query.from_user.id)
            target_user = await chat.get_member(target_user_id)
            
            # Generate appropriate text based on callback data
            if data == "id_user":
                new_text = await self._generate_user_info(target_user.user, chat, include_chat_info=False, context=context)
            elif data == "id_chat":
                new_text = await self._generate_chat_info(chat, context)
            elif data == "id_perms":
                new_text = await self._generate_permissions_info(target_user.user, chat)
            elif data == "id_warnings":
                new_text = await self._generate_warnings_info(target_user_id, chat.id)
            else:
                return

            # Add auto-delete notice for groups
            if chat.type != "private":
                new_text += "\n\n<i>This message will auto-delete in 30 seconds</i>"

            # Keep the original buttons
            buttons = [
                [
                    InlineKeyboardButton("👤 Detailed User Info", callback_data="id_user"),
                    InlineKeyboardButton("💭 Chat Info", callback_data="id_chat")
                ],
                [
                    InlineKeyboardButton("🔍 Permissions", callback_data="id_perms"),
                    InlineKeyboardButton("⚠️ Warnings", callback_data="id_warnings")
                ]
            ]
            
            reply_markup = InlineKeyboardMarkup(buttons)

            # Only edit if the content is different
            current_text = query.message.text or query.message.caption
            if current_text != new_text:
                await query.edit_message_text(
                    text=new_text,
                    parse_mode=ParseMode.HTML,
                    reply_markup=reply_markup
                )

        except Exception as e:
            logger.error(f"Error in handle_id_callback: {str(e)}")
            try:
                await query.edit_message_text(
                    "❌ An error occurred while fetching the information.",
                    parse_mode=ParseMode.HTML
                )
            except:
                pass


    async def ping(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Check bot's response time and system status"""
        if not update.effective_message:
            return

        # Record start time
        start_time = time.time()
        
        # Send initial message
        message = await update.message.reply_text(
            "📊 <b>Running Diagnostics...</b>",
            parse_mode=ParseMode.HTML
        )
        
        # Calculate message latency
        message_latency = (time.time() - start_time) * 1000  # Convert to milliseconds
        
        # Get system metrics
        cpu_usage = psutil.cpu_percent()
        memory = psutil.virtual_memory()
        memory_usage = memory.percent
        mem_used_mb = memory.used // (1024 * 1024)
        mem_total_mb = memory.total // (1024 * 1024)
        gpu_used_mb, gpu_total_mb = self._get_gpu_memory()
        uptime = datetime.now() - context.bot_data.get('start_time', datetime.now())
        
        # Format uptime
        days = uptime.days
        hours, remainder = divmod(uptime.seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        uptime_str = f"{days}d {hours}h {minutes}m {seconds}s"
        
        # Determine response quality
        if message_latency < 100:
            speed_status = "🟢 Excellent"
        elif message_latency < 200:
            speed_status = "🟡 Good"
        else:
            speed_status = "🔴 Poor"

        # Generate status report
        if gpu_used_mb is not None:
            gpu_text = f"{gpu_used_mb:.0f}/{gpu_total_mb:.0f} MB"
        else:
            gpu_text = "N/A"

        status_text = (
            "🤖 <b>Bot Status Report</b>\n\n"
            f"📡 <b>Response Time:</b> {message_latency:.1f}ms\n"
            f"⚡ <b>Connection:</b> {speed_status}\n"
            f"💻 <b>CPU Usage:</b> {cpu_usage:.1f}%\n"
            f"🖥️ <b>CPU RAM:</b> {mem_used_mb}/{mem_total_mb} MB\n"
            f"🎮 <b>GPU RAM:</b> {gpu_text}\n"
            f"💾 <b>Memory Usage:</b> {memory_usage:.1f}%\n"
            f"⏱ <b>Uptime:</b> {uptime_str}\n\n"
            "<i>Auto-deleting in 30 seconds</i> 🧹"
        )
        
        # Edit the initial message with the complete status
        await message.edit_text(
            status_text,
            parse_mode=ParseMode.HTML
        )
        
        # Schedule message deletion
        asyncio.create_task(self._delete_message_later(
            context,
            update.effective_chat.id,
            message.message_id,
            delay=30
        ))
        
        # Delete the command message in groups
        if update.effective_chat.type != "private":
            asyncio.create_task(self._delete_message_later(
                context,
                update.effective_chat.id,
                update.effective_message.message_id,
                delay=30
            ))

    def register_handlers(self, application):
        """Register all user command handlers with enhanced callbacks"""
        application.add_handler(CommandHandler("start", self.start))
        application.add_handler(CommandHandler("help", self.help))
        application.add_handler(CommandHandler(["id", "userid", "chatid"], self.get_id))
        application.add_handler(CommandHandler("ping", self.ping))
        application.add_handler(CommandHandler("fun", self.fun))
        
        # Add callback handlers (Enhanced with help system)
        application.add_handler(CallbackQueryHandler(self.start_button, pattern=r"^start_"))
        application.add_handler(CallbackQueryHandler(self.handle_id_callback, pattern=r"^id_"))
        application.add_handler(CallbackQueryHandler(self.handle_help_callback, pattern=r"^help_"))
        # Add handlers for fun and utility callback queries
        application.add_handler(CallbackQueryHandler(self.fun, pattern=r"^fun_commands$"))
        application.add_handler(CallbackQueryHandler(self.help, pattern=r"^start_help$"))

