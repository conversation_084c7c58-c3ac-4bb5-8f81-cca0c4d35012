"""
Type definitions for the plugin system
"""
from dataclasses import dataclass
from enum import Enum
from typing import Callable, Dict, Any, Optional, Union, TypeVar, ParamSpec
from telegram import Update
from telegram.ext import ContextTypes

# Type definitions
CommandFunc = Callable[[Update, ContextTypes.DEFAULT_TYPE], Any]
P = ParamSpec('P')
T = TypeVar('T')

class PluginPermission(str, Enum):
    """Permission levels for plugin commands"""
    USER = "user"
    ADMIN = "admin"
    OWNER = "owner"
    SYSTEM = "system"

@dataclass
class CommandInfo:
    """Information about a plugin command"""
    name: str
    description: str
    permission: PluginPermission
    function: CommandFunc
    usage: Optional[str] = None
    aliases: tuple[str, ...] = ()
    enabled: bool = True

@dataclass
class PluginMetadata:
    """Plugin metadata"""
    name: str
    version: str
    description: str
    author: str
    requires: Dict[str, str] = None  # Plugin dependencies
    settings_schema: Dict[str, Any] = None  # JSON Schema for plugin settings
