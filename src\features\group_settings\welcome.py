"""Welcome message handler for group management 👋"""

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ChatPermissions
from telegram.ext import ContextTypes
from telegram.constants import ParseMode
from telegram.error import TelegramError
import html
import logging
import random
import traceback

from ...database.db import Database
from ...utils.decorators import admin_only, group_only
from .rules import <PERSON><PERSON><PERSON><PERSON>
try:
    from .rules import DEFAULT_RULES
except ImportError:
    DEFAULT_RULES = "No rules set for this group."

logger = logging.getLogger(__name__)

# List of welcome GIFs
WELCOME_GIFS = [
# Welcome wave
    "https://media.giphy.com/media/XD9o33QG9BoMis7iM4/giphy.gif",  # Party
    "https://media.giphy.com/media/l4JyOCNEfXvVYEqB2/giphy.gif",  # Welcome text
    "https://media.giphy.com/media/3o6ZtpxSZbQRRnwCKQ/giphy.gif",  # Celebration
    "https://media.giphy.com/media/ASd0Ukj0y3qMM/giphy.gif"  # Welcome dance
]

DEFAULT_WELCOME = """
╭─「 <b>🌟 Welcome {mention}!</b> 」
│
│ 🎉 <b>You've joined {chat_title}</b>
│ 
│ 🎯 You're our <b>{count}th member</b> - thanks for
│ being part of our awesome community!
│
├─「 <b>✨ About Us</b> 」
│ 🤖 AI-powered group with smart moderation
│ 💡 Share ideas, learn, and grow together
│ 🛡️ Safe, friendly environment for everyone
│ 🌐 Connect with like-minded people
│
├─「 <b>🚀 Quick Start</b> 」
│ 👋 Say hello to break the ice
│ 📚 Use /help to explore features
│ 📜 Read /rules to stay informed
│ 💭 Feel free to ask questions
│
├─「 <b>🔗 Useful Links</b> 」
│ 🌐 <a href="https://helpingai.co/">Official Website</a>
│ 📱 <a href="https://t.me/HelpingAI_Official">Support Channel</a>
│ 🔔 <a href="https://t.me/HelpingAI_Updates">Updates Channel</a>
│ 🎥 <a href="https://www.youtube.com/@HelpingAI-official">YouTube Channel</a>
│ 🤗 <a href="https://huggingface.co/HelpingAI">AI Models Hub</a>
│
├─「 <b>💪 Need Help?</b> 」
│ 🚨 Use /report to contact admins
│ ❓ Ask questions freely in chat
│ 💡 Share ideas and suggestions
│
╰─「 <i>Enjoy your stay and have fun! 🚀</i> 」"""

DEFAULT_GOODBYE = """
╭─「 <b>👋 Farewell {mention}!</b> 」
│
│ 😢 <i>Sorry to see you leave {chat_title}</i>
│
│ 💫 You'll always be welcome back here
│ 🚪 Our doors are open anytime
│
╰─「 <i>Until we meet again! 🌟</i> 」"""

class WelcomeHandler:
    def __init__(self, db: Database):
        self.db = db
        self.rules_handler = RulesHandler(db)

    def get_welcome_buttons(self, chat_title: str, chat_id: int = None) -> InlineKeyboardMarkup:
        """Create welcome message buttons"""
        logger.info(f"Getting welcome buttons for chat_id: {chat_id}, type: {type(chat_id)}")
        
        # Check all possible formats of the DevsDoCode server chat ID
        is_ddc_chat = (
            chat_id == -1002307106134 or  # Integer format
            str(chat_id) == "-1002307106134" or  # String format
            chat_id == int("-1002307106134") if isinstance(chat_id, (int, str)) else False  # Parsed string format
        )
        
        logger.info(f"Is DDC chat for buttons: {is_ddc_chat}")
        
        # Custom buttons for DevsDoCode server
        if is_ddc_chat:
            logger.info(f"Using custom DDC buttons for chat {chat_id}")
            keyboard = [
                [
                    InlineKeyboardButton("📜 Rules", callback_data="show_rules"),
                    InlineKeyboardButton("⚙️ Settings", callback_data="show_settings")
                ],
                [
                    InlineKeyboardButton("🎥 YouTube", url="https://www.youtube.com/@DevsDoCode"),
                    InlineKeyboardButton("📸 Instagram", url="https://www.instagram.com/sree.shades_/")
                ],
                [
                    InlineKeyboardButton("🐦 Twitter", url="https://x.com/Anand_Sreejan"),
                    InlineKeyboardButton("💬 Telegram", url="https://t.me/devsdocode")
                ],
                [
                    InlineKeyboardButton("🎮 Discord", url="https://discord.com/invite/4gGcqsWWde"),
                    InlineKeyboardButton("👨‍💼 LinkedIn", url="https://www.linkedin.com/in/developer-sreejan/")
                ],
                [
                    InlineKeyboardButton("💻 GitHub", url="https://github.com/SreejanPersonal")
                ]
            ]
        else:
            # Default buttons for other servers
            keyboard = [
                [
                    InlineKeyboardButton("📜 Rules", callback_data="show_rules"),
                    InlineKeyboardButton("⚙️ Settings", callback_data="show_settings")
                ],
                [
                    InlineKeyboardButton("🌐 Website", url="https://helpingai.co/"),
                    InlineKeyboardButton("📺 YouTube", url="https://www.youtube.com/@HelpingAI-official")
                ],
                [
                    InlineKeyboardButton("🤖 Our Models", url="https://huggingface.co/HelpingAI")
                ]
            ]
        return InlineKeyboardMarkup(keyboard)

    async def handle_new_member(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Welcome new members"""
        if not update.message or not update.effective_chat:
            return

        chat_id = update.effective_chat.id
        # Add debug logging to track chat ID
        logger.info(f"Handle new member for chat_id: {chat_id}, type: {type(chat_id)}")
        
        welcome_enabled = await self.db.get_group_setting(chat_id, "welcome_enabled", True)
        
        if not welcome_enabled:
            return

        # Check if captcha is enabled and if the user is pending verification
        captcha_enabled = await self.db.get_group_setting(chat_id, "captcha_enabled", False)
        
        for new_member in update.message.new_chat_members:
            if new_member.is_bot:
                continue

            # If this is a normal join and captcha is enabled, skip welcome message
            # The welcome message will be sent after captcha verification
            if captcha_enabled:
                from ..captcha_handler import CaptchaHandler
                captcha_handler = CaptchaHandler(self.db)
                # Skip welcome if user has pending captcha (needs verification)
                if hasattr(captcha_handler, 'pending_captchas') and new_member.id in captcha_handler.pending_captchas:
                    logger.info(f"Skipping welcome message for user {new_member.id} pending captcha verification")
                    continue
                
                # Since we can't use captcha_verified, just show welcome for new users when captcha is disabled
                # The welcome message for captcha users is handled in captcha_handler directly
                logger.info(f"Skipping welcome for new join with captcha enabled (user {new_member.id})")
                continue

            try:
                # Check all possible formats of the DevsDoCode server chat ID
                is_ddc_chat = (
                    chat_id == -1002307106134 or  # Integer format
                    str(chat_id) == "-1002307106134" or  # String format
                    chat_id == int("-1002307106134")  # Parsed string format
                )
                
                logger.info(f"Checking if chat is DDC: {is_ddc_chat}, chat_id: {chat_id}")
                
                # Special welcome message for DevsDoCode server
                if is_ddc_chat:
                    logger.info(f"Using custom DDC welcome message for chat {chat_id}")
                    welcome_msg = """
<b>🌟 Welcome {mention} to DevsDoCode! 🚀</b>

<i>You're our {count}th member! Let's build something amazing together! 💫</i>

<b>👨‍💻 ABOUT US:</b>
• A community of passionate developers 💻
• Learn, share, and grow together 📚
• Daily coding discussions and tips 💡
• Supportive environment for everyone ✨
• Zero toxicity, all good vibes 🌟

<b>🎯 GET STARTED:</b>
• Say "Hi" or "GM" to break the ice 👋
• Check out /help for all bot features 🛠️
• Read /rules to understand our guidelines 📜
• Share your projects and ideas 💭

<b>💪 NEED HELP?</b>
• Use /report to contact our friendly admins
• Ask questions in the chat - we're here to help!
• Share your ideas - we love community input!

<i>Welcome to the DevsDoCode family! Let's code something amazing! 💻</i>
"""
                    # Force a bypass of any database welcome message
                    await self.db.set_welcome_message(chat_id, welcome_msg)
                else:
                    # Get welcome message with fallback to default for other servers
                    welcome_msg = await self.db.get_welcome_message(chat_id)
                    if not welcome_msg:
                        welcome_msg = DEFAULT_WELCOME

                # Get member count
                chat = await context.bot.get_chat(chat_id)
                member_count = await chat.get_member_count()
                
                # Format welcome message
                formatted_msg = welcome_msg.format(
                    mention=new_member.mention_html(),
                    chat_title=html.escape(update.effective_chat.title),
                    count=member_count,
                    name=html.escape(new_member.first_name),
                    username=html.escape(new_member.username or "No username"),
                    first_name=html.escape(new_member.first_name),
                    last_name=html.escape(new_member.last_name or ""),
                    id=new_member.id
                )
                
                # Get random welcome GIF
                welcome_gif = random.choice(WELCOME_GIFS)
                
                # Check if it's DDC again for buttons
                is_ddc_chat = (
                    chat_id == -1002307106134 or
                    str(chat_id) == "-1002307106134" or
                    chat_id == int("-1002307106134")
                )
                
                # Check for custom media welcome
                media_data = await self.db.get_group_setting(chat_id, "welcome_media")
                
                # Get custom buttons if available
                custom_buttons = await self.db.get_group_setting(chat_id, "welcome_buttons")
                
                if custom_buttons:
                    # Create custom button layout
                    keyboard = []
                    for button in custom_buttons:
                        keyboard.append([InlineKeyboardButton(button["text"], url=button["url"])])
                    
                    # Add default buttons
                    keyboard.extend([
                        [
                            InlineKeyboardButton("📜 Rules", callback_data="show_rules"),
                            InlineKeyboardButton("⚙️ Settings", callback_data="show_settings")
                        ]
                    ])
                    reply_markup = InlineKeyboardMarkup(keyboard)
                else:
                    # Use default buttons
                    reply_markup = self.get_welcome_buttons(update.effective_chat.title, 
                                                           -1002307106134 if is_ddc_chat else chat_id)
                
                # Send welcome message based on type
                if media_data and media_data.get("type") == "media":
                    # Send custom media
                    caption = media_data.get("caption", formatted_msg)
                    formatted_caption = caption.format(
                        mention=new_member.mention_html(),
                        chat_title=html.escape(update.effective_chat.title),
                        count=member_count,
                        name=html.escape(new_member.first_name),
                        username=html.escape(new_member.username or "No username"),
                        first_name=html.escape(new_member.first_name),
                        last_name=html.escape(new_member.last_name or ""),
                        id=new_member.id
                    )
                    
                    if media_data["media_type"] == "photo":
                        await context.bot.send_photo(
                            chat_id=chat_id,
                            photo=media_data["file_id"],
                            caption=formatted_caption,
                            parse_mode=ParseMode.HTML,
                            reply_markup=reply_markup
                        )
                    elif media_data["media_type"] == "video":
                        await context.bot.send_video(
                            chat_id=chat_id,
                            video=media_data["file_id"],
                            caption=formatted_caption,
                            parse_mode=ParseMode.HTML,
                            reply_markup=reply_markup
                        )
                    elif media_data["media_type"] == "animation":
                        await context.bot.send_animation(
                            chat_id=chat_id,
                            animation=media_data["file_id"],
                            caption=formatted_caption,
                            parse_mode=ParseMode.HTML,
                            reply_markup=reply_markup
                        )
                    elif media_data["media_type"] == "document":
                        await context.bot.send_document(
                            chat_id=chat_id,
                            document=media_data["file_id"],
                            caption=formatted_caption,
                            parse_mode=ParseMode.HTML,
                            reply_markup=reply_markup
                        )
                else:
                    # Send default GIF with welcome message and buttons
                    await context.bot.send_animation(
                        chat_id=chat_id,
                        animation=welcome_gif,
                        caption=formatted_msg,
                        parse_mode=ParseMode.HTML,
                        reply_markup=reply_markup
                    )
                
                logger.info(f"Sent welcome message for user {new_member.id} in chat {chat_id}")
            except Exception as e:
                logger.error(f"Error sending welcome message: {e}")
                logger.error(traceback.format_exc())

    async def handle_member_left(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Say goodbye to leaving members"""
        if not update.message or not update.effective_chat:
            return

        chat_id = update.effective_chat.id
        left_member = update.message.left_chat_member

        if left_member.is_bot:
            return

        try:
            # Get goodbye message with fallback to default
            goodbye_msg = await self.db.get_goodbye_message(chat_id)
            if not goodbye_msg or "user_mention" in goodbye_msg:  # Check for old format
                goodbye_msg = DEFAULT_GOODBYE
                await self.db.set_goodbye_message(chat_id, goodbye_msg)

            chat = await context.bot.get_chat(chat_id)
            member_count = await chat.get_member_count()
            
            formatted_msg = goodbye_msg.format(
                mention=left_member.mention_html(),
                username=html.escape(left_member.username or "No username"),
                first_name=html.escape(left_member.first_name),
                last_name=html.escape(left_member.last_name or ""),
                id=left_member.id,
                count=member_count,
                chat_title=html.escape(update.effective_chat.title)
            )
            
            # Send goodbye message
            await context.bot.send_message(
                chat_id=chat_id,
                text=formatted_msg,
                parse_mode=ParseMode.HTML
            )
            
        except Exception as e:
            logger.error(f"Error sending goodbye message: {str(e)}")
            logger.error(traceback.format_exc())

    async def handle_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle callback queries from welcome message buttons"""
        query = update.callback_query
        
        if not query:
            return
            
        await query.answer()  # Always answer the callback query first
        
        # Get the chat_id
        chat_id = update.effective_chat.id
        logger.info(f"Handle welcome callback for chat_id: {chat_id}")
        
        # Check if this is the DevsDoCode server
        is_ddc_chat = (
            chat_id == -1002307106134 or  # Integer format
            str(chat_id) == "-1002307106134" or  # String format
            chat_id == int("-1002307106134") if isinstance(chat_id, (int, str)) else False # Parsed string format
        )
        logger.info(f"Is DDC chat for callback: {is_ddc_chat}")
        
        if query.data == "show_rules":
            # Send rules as a new message instead of editing the animation
            try:
                rules = await self.db.get_group_setting(chat_id, "rules") or DEFAULT_RULES
                
                # Create simplified buttons
                buttons = [
                    [
                        InlineKeyboardButton("Full Rules", callback_data="rules_full"),
                        InlineKeyboardButton("FAQ", callback_data="rules_faq")
                    ],
                    [
                        InlineKeyboardButton("Back to Welcome", callback_data="back_to_welcome")
                    ]
                ]

                # Add admin buttons if user is admin
                user_id = update.effective_user.id
                try:
                    member = await context.bot.get_chat_member(chat_id, user_id)
                    if member.status in ['creator', 'administrator']:
                        buttons.insert(1, [
                            InlineKeyboardButton("Edit Rules", callback_data="rules_edit"),
                            InlineKeyboardButton("Reset Rules", callback_data="rules_reset")
                        ])
                except Exception as e:
                    logger.error(f"Error checking admin status: {e}")

                reply_markup = InlineKeyboardMarkup(buttons)
                
                # Send new message with rules
                await context.bot.send_message(
                    chat_id=chat_id,
                    text=f"{rules}\n\n<i>Use buttons below to navigate</i>",
                    parse_mode=ParseMode.HTML,
                    reply_markup=reply_markup
                )
                
            except Exception as e:
                logger.error(f"Error showing rules: {str(e)}")
                await query.answer("Error showing rules. Please try again later.")
                
        elif query.data == "back_to_welcome":
            # For back to welcome, we need to send a new welcome message since we can't edit animation
            try:
                # Get welcome message
                welcome_msg = None
                
                # Special welcome message for DevsDoCode server
                if is_ddc_chat:
                    logger.info(f"Using custom DDC welcome message for chat {chat_id} in callback")
                    welcome_msg = """
<b>🌟 Welcome {mention} to DevsDoCode! 🚀</b>

<i>You're our {count}th member! Let's build something amazing together! 💫</i>

<b>👨‍💻 ABOUT US:</b>
• A community of passionate developers 💻
• Learn, share, and grow together 📚
• Daily coding discussions and tips 💡
• Supportive environment for everyone ✨
• Zero toxicity, all good vibes 🌟

<b>🎯 GET STARTED:</b>
• Say "Hi" or "GM" to break the ice 👋
• Check out /help for all bot features 🛠️
• Read /rules to understand our guidelines 📜
• Share your projects and ideas 💭

<b>💪 NEED HELP?</b>
• Use /report to contact our friendly admins
• Ask questions in the chat - we're here to help!
• Share your ideas - we love community input!

<i>Welcome to the DevsDoCode family! Let's code something amazing! 💻</i>
"""
                else:
                    # Get welcome message from database
                    welcome_msg = await self.db.get_welcome_message(chat_id)
                    if not welcome_msg:
                        welcome_msg = DEFAULT_WELCOME
                
                # Format welcome message
                user_mention = f"@{update.effective_user.username}" if update.effective_user.username else update.effective_user.mention_html()
                chat_title = update.effective_chat.title
                
                # Get member count
                chat = await context.bot.get_chat(chat_id)
                member_count = await chat.get_member_count()
                
                formatted_msg = welcome_msg.format(
                    mention=user_mention,
                    chat_title=chat_title,
                    count=member_count,
                    name=html.escape(update.effective_user.first_name),
                    username=html.escape(update.effective_user.username or "No username"),
                    first_name=html.escape(update.effective_user.first_name),
                    last_name=html.escape(update.effective_user.last_name or ""),
                    id=update.effective_user.id
                )
                
                # Send new welcome message
                await context.bot.send_message(
                    chat_id=chat_id,
                    text=formatted_msg,
                    parse_mode=ParseMode.HTML,
                    reply_markup=self.get_welcome_buttons(chat_title, -1002307106134 if is_ddc_chat else chat_id),
                    disable_web_page_preview=True
                )
                
            except Exception as e:
                logger.error(f"Error showing welcome message: {str(e)}")
                await query.answer("Error showing welcome message. Please try again later.")

    @admin_only()
    @group_only()
    async def set_welcome(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Set custom welcome message for the group with advanced features"""
        try:
            chat_id = update.effective_chat.id
            
            # Check if this is a reply to a media message
            if update.message.reply_to_message:
                await self._handle_media_welcome(update, context)
                return
            
            # Check for subcommands
            if context.args and context.args[0].lower() in ['help', 'options', 'features']:
                await self._show_welcome_help(update, context)
                return
            
            if context.args and context.args[0].lower() == 'reset':
                await self._reset_welcome(update, context)
                return
                
            if context.args and context.args[0].lower() == 'preview':
                await self._preview_welcome(update, context)
                return
                
            if context.args and context.args[0].lower() == 'buttons':
                await self._set_welcome_buttons(update, context)
                return

            message = " ".join(context.args) if context.args else None

            if not message:
                await self._show_welcome_help(update, context)
                return

            try:
                # Create a sample message to preview
                sample_msg = message.format(
                    mention=f'<a href="tg://user?id=123456789">@user</a>',
                    username="@username",
                    first_name=html.escape("John"),
                    last_name=html.escape("Doe"),
                    id="123456789",
                    count="100",
                    chat_title=html.escape(update.effective_chat.title)
                )

                # Test if the message can be parsed with HTML
                preview_msg = await update.message.reply_text(
                    f"<b>📋 Welcome Message Preview:</b>\n\n{sample_msg}\n\n"
                    "<i>✅ Message format is valid! Saving...</i>",
                    parse_mode=ParseMode.HTML,
                    disable_web_page_preview=True
                )
                
            except (ValueError, KeyError) as e:
                await update.message.reply_text(
                    "<b>❌ Invalid message format!</b>\n"
                    "Please check the placeholders and try again.\n\n"
                    "Use <code>/setwelcome help</code> for more information.",
                    parse_mode=ParseMode.HTML
                )
                return
            except TelegramError as e:
                await update.message.reply_text(
                    f"<b>❌ Error in welcome message format:</b>\n"
                    f"<code>{html.escape(str(e))}</code>\n\n"
                    "Please check your HTML formatting and try again.\n"
                    "Use <code>/setwelcome help</code> for more information.",
                    parse_mode=ParseMode.HTML
                )
                return

            # Save the welcome message
            await self.db.set_welcome_message(chat_id, message)
            await self.db.set_welcome_setting(chat_id, True)

            # Update the preview message
            await preview_msg.edit_text(
                f"<b>📋 Welcome Message Preview:</b>\n\n{sample_msg}\n\n"
                "<b>✅ Welcome Message Updated Successfully!</b>\n\n"
                "<i>💡 Tip: Use /setwelcome buttons to add custom buttons!</i>",
                parse_mode=ParseMode.HTML,
                disable_web_page_preview=True
            )

        except Exception as e:
            logger.error(f"Error setting welcome message: {str(e)}\n{traceback.format_exc()}")
            await update.message.reply_text(
                "<b>❌ Failed to set welcome message.</b>\n"
                "Please try again later.",
                parse_mode=ParseMode.HTML
            )

    async def _show_welcome_help(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show comprehensive help for welcome command"""
        help_text = """
<b>🎉 Welcome Message Configuration</b>

<b>📝 Basic Usage:</b>
<code>/setwelcome [message]</code> - Set welcome message
<code>/setwelcome help</code> - Show this help
<code>/setwelcome reset</code> - Reset to default
<code>/setwelcome preview</code> - Preview current message
<code>/setwelcome buttons</code> - Configure custom buttons

<b>🖼️ Media Support:</b>
Reply to a photo/video/GIF with <code>/setwelcome [caption]</code>

<b>🔧 Available Placeholders:</b>
• <code>{mention}</code> - Mentions the user (@username)
• <code>{username}</code> - User's username
• <code>{first_name}</code> - User's first name
• <code>{last_name}</code> - User's last name
• <code>{id}</code> - User's ID number
• <code>{count}</code> - Current member count
• <code>{chat_title}</code> - Group name

<b>🎨 HTML Formatting:</b>
• <code>&lt;b&gt;bold&lt;/b&gt;</code> - <b>Bold text</b>
• <code>&lt;i&gt;italic&lt;/i&gt;</code> - <i>Italic text</i>
• <code>&lt;u&gt;underline&lt;/u&gt;</code> - <u>Underlined text</u>
• <code>&lt;code&gt;code&lt;/code&gt;</code> - <code>Monospace text</code>
• <code>&lt;a href="url"&gt;link&lt;/a&gt;</code> - Clickable links

<b>🔘 Button Format:</b>
Use <code>/setwelcome buttons</code> to add custom buttons:
<code>Button Text - URL</code>
<code>Another Button - https://example.com</code>

<b>💡 Examples:</b>
<code>/setwelcome Welcome {mention} to {chat_title}! 🎉</code>
<code>/setwelcome &lt;b&gt;Hello {first_name}!&lt;/b&gt; You're member #{count}</code>

<b>⚙️ Additional Commands:</b>
• <code>/togglewelcome</code> - Enable/disable welcome messages
• <code>/welcomestats</code> - View welcome message statistics
"""
        
        await update.message.reply_text(
            help_text,
            parse_mode=ParseMode.HTML,
            disable_web_page_preview=True
        )

    async def _handle_media_welcome(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle setting welcome message with media"""
        try:
            chat_id = update.effective_chat.id
            reply_msg = update.message.reply_to_message
            caption = " ".join(context.args) if context.args else ""
            
            # Get media info
            media_type = None
            file_id = None
            
            if reply_msg.photo:
                media_type = "photo"
                file_id = reply_msg.photo[-1].file_id
            elif reply_msg.video:
                media_type = "video"
                file_id = reply_msg.video.file_id
            elif reply_msg.animation:
                media_type = "animation"
                file_id = reply_msg.animation.file_id
            elif reply_msg.document:
                media_type = "document"
                file_id = reply_msg.document.file_id
            else:
                await update.message.reply_text(
                    "<b>❌ Unsupported media type!</b>\n"
                    "Supported: Photo, Video, GIF, Document",
                    parse_mode=ParseMode.HTML
                )
                return
            
            # Save media welcome settings
            welcome_data = {
                "type": "media",
                "media_type": media_type,
                "file_id": file_id,
                "caption": caption or "Welcome {mention} to {chat_title}! 🎉"
            }
            
            await self.db.set_group_setting(chat_id, "welcome_media", welcome_data)
            await self.db.set_welcome_setting(chat_id, True)
            
            await update.message.reply_text(
                f"<b>✅ Media Welcome Message Set!</b>\n\n"
                f"<b>Media Type:</b> {media_type.title()}\n"
                f"<b>Caption:</b> {caption or 'Default welcome text'}\n\n"
                "<i>New members will receive this media with the welcome message!</i>",
                parse_mode=ParseMode.HTML
            )
            
        except Exception as e:
            logger.error(f"Error setting media welcome: {e}")
            await update.message.reply_text(
                "<b>❌ Failed to set media welcome message.</b>",
                parse_mode=ParseMode.HTML
            )

    async def _reset_welcome(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Reset welcome message to default"""
        try:
            chat_id = update.effective_chat.id
            
            # Clear custom settings
            await self.db.set_welcome_message(chat_id, None)
            await self.db.set_group_setting(chat_id, "welcome_media", None)
            await self.db.set_group_setting(chat_id, "welcome_buttons", None)
            
            await update.message.reply_text(
                "<b>✅ Welcome message reset to default!</b>\n\n"
                "<i>New members will receive the default welcome message.</i>",
                parse_mode=ParseMode.HTML
            )
            
        except Exception as e:
            logger.error(f"Error resetting welcome: {e}")
            await update.message.reply_text(
                "<b>❌ Failed to reset welcome message.</b>",
                parse_mode=ParseMode.HTML
            )

    async def _preview_welcome(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Preview current welcome message"""
        try:
            chat_id = update.effective_chat.id
            
            # Get current welcome message
            welcome_msg = await self.db.get_welcome_message(chat_id)
            if not welcome_msg:
                welcome_msg = DEFAULT_WELCOME
            
            # Format with sample data
            sample_msg = welcome_msg.format(
                mention=f'<a href="tg://user?id={update.effective_user.id}">@{update.effective_user.first_name}</a>',
                username=f"@{update.effective_user.username or 'username'}",
                first_name=html.escape(update.effective_user.first_name),
                last_name=html.escape(update.effective_user.last_name or ""),
                id=update.effective_user.id,
                count="100",
                chat_title=html.escape(update.effective_chat.title)
            )
            
            # Check for media welcome
            media_data = await self.db.get_group_setting(chat_id, "welcome_media")
            
            if media_data:
                # Send media preview
                if media_data["media_type"] == "photo":
                    await context.bot.send_photo(
                        chat_id=chat_id,
                        photo=media_data["file_id"],
                        caption=f"<b>📋 Welcome Preview (Media):</b>\n\n{sample_msg}",
                        parse_mode=ParseMode.HTML
                    )
                elif media_data["media_type"] == "video":
                    await context.bot.send_video(
                        chat_id=chat_id,
                        video=media_data["file_id"],
                        caption=f"<b>📋 Welcome Preview (Media):</b>\n\n{sample_msg}",
                        parse_mode=ParseMode.HTML
                    )
                elif media_data["media_type"] == "animation":
                    await context.bot.send_animation(
                        chat_id=chat_id,
                        animation=media_data["file_id"],
                        caption=f"<b>📋 Welcome Preview (Media):</b>\n\n{sample_msg}",
                        parse_mode=ParseMode.HTML
                    )
            else:
                # Send text preview
                await update.message.reply_text(
                    f"<b>📋 Welcome Message Preview:</b>\n\n{sample_msg}",
                    parse_mode=ParseMode.HTML,
                    disable_web_page_preview=True
                )
                
        except Exception as e:
            logger.error(f"Error previewing welcome: {e}")
            await update.message.reply_text(
                "<b>❌ Failed to preview welcome message.</b>",
                parse_mode=ParseMode.HTML
            )

    async def _set_welcome_buttons(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Set custom welcome buttons"""
        try:
            chat_id = update.effective_chat.id
            
            if not context.args[1:]:  # No button data provided
                await update.message.reply_text(
                    "<b>🔘 Custom Welcome Buttons</b>\n\n"
                    "<b>Format:</b>\n"
                    "<code>/setwelcome buttons</code>\n"
                    "<code>Button Text - URL</code>\n"
                    "<code>Another Button - https://example.com</code>\n\n"
                    "<b>Example:</b>\n"
                    "<code>/setwelcome buttons</code>\n"
                    "<code>📖 Rules - https://t.me/yourchannel</code>\n"
                    "<code>💬 Support - https://t.me/support</code>\n\n"
                    "<i>Send each button on a new line after the command.</i>",
                    parse_mode=ParseMode.HTML
                )
                return
            
            # Parse button data
            button_lines = " ".join(context.args[1:]).split('\n')
            buttons = []
            
            for line in button_lines:
                if ' - ' in line:
                    text, url = line.split(' - ', 1)
                    buttons.append({"text": text.strip(), "url": url.strip()})
            
            if not buttons:
                await update.message.reply_text(
                    "<b>❌ No valid buttons found!</b>\n"
                    "Use format: <code>Button Text - URL</code>",
                    parse_mode=ParseMode.HTML
                )
                return
            
            # Save custom buttons
            await self.db.set_group_setting(chat_id, "welcome_buttons", buttons)
            
            button_list = "\n".join([f"• {btn['text']} → {btn['url']}" for btn in buttons])
            
            await update.message.reply_text(
                f"<b>✅ Custom Welcome Buttons Set!</b>\n\n"
                f"<b>Buttons:</b>\n{button_list}\n\n"
                "<i>These buttons will appear with welcome messages.</i>",
                parse_mode=ParseMode.HTML
            )
            
        except Exception as e:
            logger.error(f"Error setting welcome buttons: {e}")
            await update.message.reply_text(
                "<b>❌ Failed to set welcome buttons.</b>",
                parse_mode=ParseMode.HTML
            )

    @admin_only()
    @group_only()
    async def toggle_welcome(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Toggle welcome messages"""
        chat_id = update.effective_chat.id
        
        try:
            current = await self.db.get_group_setting(chat_id, "welcome_enabled", True)
            new_value = not current
            
            await self.db.set_group_setting(chat_id, "welcome_enabled", new_value)
            status = "enabled" if new_value else "disabled"
            
            await update.message.reply_text(
                f"{'✅' if new_value else '❌'} Welcome messages are now {status}!",
                parse_mode=ParseMode.HTML
            )
        except Exception as e:
            await update.message.reply_text(
                f"❌ Failed to toggle welcome messages: {str(e)}",
                parse_mode=ParseMode.HTML
            )

    @admin_only()
    @group_only()
    async def welcome_stats(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show welcome message statistics and settings"""
        try:
            chat_id = update.effective_chat.id
            
            # Get current settings
            welcome_enabled = await self.db.get_group_setting(chat_id, "welcome_enabled", True)
            welcome_msg = await self.db.get_welcome_message(chat_id)
            media_data = await self.db.get_group_setting(chat_id, "welcome_media")
            custom_buttons = await self.db.get_group_setting(chat_id, "welcome_buttons")
            
            # Build stats message
            stats_text = f"""
<b>📊 Welcome Message Statistics</b>

<b>🔧 Current Settings:</b>
• Status: {'✅ Enabled' if welcome_enabled else '❌ Disabled'}
• Message Type: {'📱 Custom Media' if media_data else '📝 Text/GIF'}
• Custom Buttons: {'✅ Yes' if custom_buttons else '❌ No'}

<b>📝 Message Info:</b>
• Length: {len(welcome_msg) if welcome_msg else len(DEFAULT_WELCOME)} characters
• Custom Message: {'✅ Yes' if welcome_msg else '❌ Using Default'}

<b>🎨 Features:</b>
• Media Support: {'✅ Active' if media_data else '⚪ Available'}
• Custom Buttons: {'✅ Active' if custom_buttons else '⚪ Available'}
• HTML Formatting: ✅ Supported
• Placeholders: ✅ Supported

<b>⚙️ Quick Actions:</b>
• <code>/setwelcome help</code> - View all options
• <code>/setwelcome preview</code> - Preview current message
• <code>/togglewelcome</code> - Enable/disable welcome
• <code>/setwelcome reset</code> - Reset to default
"""
            
            if custom_buttons:
                button_list = "\n".join([f"  • {btn['text']}" for btn in custom_buttons[:3]])
                if len(custom_buttons) > 3:
                    button_list += f"\n  • ... and {len(custom_buttons) - 3} more"
                stats_text += f"\n<b>🔘 Custom Buttons:</b>\n{button_list}"
            
            await update.message.reply_text(
                stats_text,
                parse_mode=ParseMode.HTML,
                disable_web_page_preview=True
            )
            
        except Exception as e:
            logger.error(f"Error showing welcome stats: {e}")
            await update.message.reply_text(
                "<b>❌ Failed to get welcome statistics.</b>",
                parse_mode=ParseMode.HTML
            )

    @admin_only()
    @group_only()
    async def set_goodbye(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Set custom goodbye message for the group"""
        try:
            chat_id = update.effective_chat.id
            message = " ".join(context.args) if context.args else None

            if not message:
                await update.message.reply_text(
                    "<b>👋 Set Goodbye Message</b>\n\n"
                    "<b>Usage:</b> <code>/setgoodbye [message]</code>\n\n"
                    "<b>Available placeholders:</b>\n"
                    "• <code>{mention}</code> - Mentions the user\n"
                    "• <code>{username}</code> - User's username\n"
                    "• <code>{first_name}</code> - User's first name\n"
                    "• <code>{last_name}</code> - User's last name\n"
                    "• <code>{id}</code> - User's ID\n"
                    "• <code>{count}</code> - Member count\n"
                    "• <code>{chat_title}</code> - Group name\n\n"
                    "<b>Example:</b>\n"
                    "<code>/setgoodbye Goodbye {mention}! Thanks for being part of {chat_title}!</code>",
                    parse_mode=ParseMode.HTML
                )
                return

            try:
                # Create a sample message to preview
                sample_msg = message.format(
                    mention=f'<a href="tg://user?id=123456789">@user</a>',
                    username="@username",
                    first_name=html.escape("John"),
                    last_name=html.escape("Doe"),
                    id="123456789",
                    count="99",
                    chat_title=html.escape(update.effective_chat.title)
                )

                # Test if the message can be parsed with HTML
                preview_msg = await update.message.reply_text(
                    f"<b>📋 Goodbye Message Preview:</b>\n\n{sample_msg}\n\n"
                    "<i>✅ Message format is valid! Saving...</i>",
                    parse_mode=ParseMode.HTML,
                    disable_web_page_preview=True
                )
                
            except (ValueError, KeyError) as e:
                await update.message.reply_text(
                    "<b>❌ Invalid message format!</b>\n"
                    "Please check the placeholders and try again.",
                    parse_mode=ParseMode.HTML
                )
                return
            except TelegramError as e:
                await update.message.reply_text(
                    f"<b>❌ Error in goodbye message format:</b>\n"
                    f"<code>{html.escape(str(e))}</code>\n\n"
                    "Please check your HTML formatting and try again.",
                    parse_mode=ParseMode.HTML
                )
                return

            # Save the goodbye message
            await self.db.set_goodbye_message(chat_id, message)

            # Update the preview message
            await preview_msg.edit_text(
                f"<b>📋 Goodbye Message Preview:</b>\n\n{sample_msg}\n\n"
                "<b>✅ Goodbye Message Updated Successfully!</b>",
                parse_mode=ParseMode.HTML,
                disable_web_page_preview=True
            )

        except Exception as e:
            logger.error(f"Error setting goodbye message: {str(e)}\n{traceback.format_exc()}")
            await update.message.reply_text(
                "<b>❌ Failed to set goodbye message.</b>\n"
                "Please try again later.",
                parse_mode=ParseMode.HTML
            )
