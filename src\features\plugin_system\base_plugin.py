"""
Base Plugin Class for NexusAI Telegram Bot
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, ClassVar
import inspect
import logging
from telegram import Update
from telegram.ext import Application, CommandHandler, MessageHandler, filters
from .plugin_types import PluginMetadata, CommandInfo
from .decorators import command, event_handler

class BasePlugin(ABC):
    """Base class for all plugins"""
    
    # Class variables for plugin metadata
    metadata: ClassVar[PluginMetadata] = PluginMetadata(
        name="BasePlugin",
        version="1.0.0",
        description="Base plugin class",
        author="Unknown"
    )
    
    def __init__(self):
        self.logger = logging.getLogger(f"plugin.{self.metadata.name}")
        self.enabled: bool = True
        self.settings: Dict[str, Any] = {}
        self._command_handlers: Dict[str, CommandInfo] = {}
        self._event_handlers: Dict[str, List[Any]] = {}
        
        # Auto-discover commands and event handlers
        self._discover_handlers()
        
    def _discover_handlers(self) -> None:
        """Discover all command and event handlers in the plugin"""
        for _, method in inspect.getmembers(self, predicate=inspect.ismethod):
            # Check for command handlers
            if hasattr(method, '__command_info__'):
                cmd_info: CommandInfo = method.__command_info__
                self._command_handlers[cmd_info.name] = cmd_info
                for alias in cmd_info.aliases:
                    self._command_handlers[alias] = cmd_info
                    
            # Check for event handlers
            if hasattr(method, '__event_type__'):
                event_type = method.__event_type__
                if event_type not in self._event_handlers:
                    self._event_handlers[event_type] = []
                self._event_handlers[event_type].append(method)
    
    @abstractmethod
    async def setup(self, application: Application) -> None:
        """Set up the plugin with the application"""
        # Register all command handlers
        for cmd_name, cmd_info in self._command_handlers.items():
            application.add_handler(
                CommandHandler(cmd_name, cmd_info.function)
            )
            self.logger.info(f"Registered command: /{cmd_name}")
            
    @abstractmethod
    async def cleanup(self) -> None:
        """Clean up any resources when plugin is disabled"""
        pass
        
    def get_commands(self) -> Dict[str, CommandInfo]:
        """Get all commands provided by this plugin"""
        return self._command_handlers
        
    def get_event_handlers(self) -> Dict[str, List[Any]]:
        """Get all event handlers"""
        return self._event_handlers
        
    def enable(self) -> None:
        """Enable the plugin"""
        self.enabled = True
        self.logger.info(f"Plugin {self.metadata.name} enabled")
        
    def disable(self) -> None:
        """Disable the plugin"""
        self.enabled = False
        self.logger.info(f"Plugin {self.metadata.name} disabled")
        
    @property
    def is_enabled(self) -> bool:
        """Check if plugin is enabled"""
        return self.enabled
        
    def get_info(self) -> Dict[str, Any]:
        """Get plugin information"""
        return {
            "metadata": self.metadata.__dict__,
            "enabled": self.enabled,
            "commands": {
                name: {
                    "description": cmd.description,
                    "permission": cmd.permission.value,
                    "usage": cmd.usage,
                    "enabled": cmd.enabled
                }
                for name, cmd in self._command_handlers.items()
            }
        }
        
    async def update_settings(self, settings: Dict[str, Any]) -> None:
        """Update plugin settings"""
        if self.metadata.settings_schema:
            # TODO: Validate settings against schema
            self.settings.update(settings)
            self.logger.info(f"Updated settings for plugin {self.metadata.name}")
            
    @property
    def name(self) -> str:
        """Get plugin name"""
        return self.metadata.name
