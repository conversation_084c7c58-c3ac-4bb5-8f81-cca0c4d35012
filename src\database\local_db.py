"""
SQLite database manager for local storage.
"""
import sqlite3
import aiosqlite
import json
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class LocalDatabase:
    def __init__(self, db_path: str = "data/bot_database.db"):
        self.db_path = db_path
        self.setup_database()

    def setup_database(self):
        """Initialize database tables"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # User preferences table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_preferences (
                    user_id INTEGER PRIMARY KEY,
                    username TEXT,
                    settings TEXT,
                    last_active TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT TRUE
                )
            ''')

            # Reminders table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS reminders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    message TEXT,
                    remind_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    completed BOOLEAN DEFAULT FALSE,
                    completed_at TIMESTAMP
                )
            ''')

            # Notes table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS notes (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    title TEXT,
                    content TEXT,
                    tags TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Custom commands table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS custom_commands (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    chat_id INTEGER,
                    command TEXT,
                    response TEXT,
                    created_by INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Statistics table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS command_stats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    command TEXT,
                    user_id INTEGER,
                    chat_id INTEGER,
                    used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Bookmarks table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS bookmarks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    url TEXT,
                    title TEXT,
                    description TEXT,
                    tags TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Daily statistics table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS daily_stats (
                    date DATE PRIMARY KEY,
                    stats TEXT
                )
            ''')

            # Warnings table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS warnings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    chat_id INTEGER,
                    user_id INTEGER,
                    reason TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    admin_id INTEGER
                )
            ''')

            conn.commit()

    # User Preferences Methods
    async def set_user_preference(self, user_id: int, username: str, settings: Dict[str, Any]):
        """Set user preferences"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute('''
                INSERT OR REPLACE INTO user_preferences (user_id, username, settings, last_active)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            ''', (user_id, username, json.dumps(settings)))
            await db.commit()

    async def get_user_preference(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user preferences"""
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute(
                'SELECT settings FROM user_preferences WHERE user_id = ?',
                (user_id,)
            ) as cursor:
                row = await cursor.fetchone()
                return json.loads(row[0]) if row else None

    # Reminders Methods
    async def add_reminder(self, user_id: int, message: str, remind_at: datetime):
        """Add a new reminder"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute('''
                INSERT INTO reminders (user_id, message, remind_at)
                VALUES (?, ?, ?)
            ''', (user_id, message, remind_at.isoformat()))
            await db.commit()

    async def get_due_reminders(self) -> List[Dict[str, Any]]:
        """Get all due reminders"""
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute('''
                SELECT id, user_id, message, remind_at
                FROM reminders
                WHERE completed = FALSE AND remind_at <= CURRENT_TIMESTAMP
            ''') as cursor:
                reminders = await cursor.fetchall()
                return [
                    {
                        'id': r[0],
                        'user_id': r[1],
                        'message': r[2],
                        'remind_at': datetime.fromisoformat(r[3])
                    }
                    for r in reminders
                ]

    async def mark_reminder_completed(self, reminder_id: int):
        """Mark a reminder as completed"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute('''
                UPDATE reminders
                SET completed = TRUE, completed_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (reminder_id,))
            await db.commit()

    async def delete_old_reminders(self, before_date: datetime):
        """Delete old completed reminders"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute('''
                DELETE FROM reminders
                WHERE completed = TRUE
                AND completed_at < ?
            ''', (before_date.isoformat(),))
            await db.commit()

    # Notes Methods
    async def add_note(self, user_id: int, title: str, content: str, tags: List[str] = None):
        """Add a new note"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute('''
                INSERT INTO notes (user_id, title, content, tags)
                VALUES (?, ?, ?, ?)
            ''', (user_id, title, content, json.dumps(tags or [])))
            await db.commit()

    async def get_notes(self, user_id: int, tag: str = None) -> List[Dict[str, Any]]:
        """Get user's notes, optionally filtered by tag"""
        async with aiosqlite.connect(self.db_path) as db:
            query = 'SELECT id, title, content, tags FROM notes WHERE user_id = ?'
            params = [user_id]
            if tag:
                query += ' AND tags LIKE ?'
                params.append(f'%{tag}%')
            
            async with db.execute(query, params) as cursor:
                notes = await cursor.fetchall()
                return [
                    {
                        'id': n[0],
                        'title': n[1],
                        'content': n[2],
                        'tags': json.loads(n[3])
                    }
                    for n in notes
                ]

    # Custom Commands Methods
    async def add_custom_command(self, chat_id: int, command: str, response: str, created_by: int):
        """Add a custom command"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute('''
                INSERT INTO custom_commands (chat_id, command, response, created_by)
                VALUES (?, ?, ?, ?)
            ''', (chat_id, command, response, created_by))
            await db.commit()

    async def get_custom_command(self, chat_id: int, command: str) -> Optional[str]:
        """Get custom command response"""
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute(
                'SELECT response FROM custom_commands WHERE chat_id = ? AND command = ?',
                (chat_id, command)
            ) as cursor:
                row = await cursor.fetchone()
                return row[0] if row else None

    # Statistics Methods
    async def log_command_usage(self, command: str, user_id: int, chat_id: int):
        """Log command usage"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute('''
                INSERT INTO command_stats (command, user_id, chat_id)
                VALUES (?, ?, ?)
            ''', (command, user_id, chat_id))
            await db.commit()

    async def get_command_stats(self, chat_id: int = None) -> Dict[str, int]:
        """Get command usage statistics"""
        async with aiosqlite.connect(self.db_path) as db:
            query = 'SELECT command, COUNT(*) as count FROM command_stats'
            params = []
            if chat_id:
                query += ' WHERE chat_id = ?'
                params.append(chat_id)
            query += ' GROUP BY command'
            
            async with db.execute(query, params) as cursor:
                stats = await cursor.fetchall()
                return {row[0]: row[1] for row in stats}

    async def get_daily_stats(self) -> Dict[str, Any]:
        """Get daily usage statistics"""
        async with aiosqlite.connect(self.db_path) as db:
            stats = {}
            
            # Get command usage counts
            async with db.execute('''
                SELECT command, COUNT(*) as count
                FROM command_stats
                WHERE date(used_at) = date('now')
                GROUP BY command
            ''') as cursor:
                stats['commands'] = {row[0]: row[1] for row in await cursor.fetchall()}
            
            # Get active users count
            async with db.execute('''
                SELECT COUNT(DISTINCT user_id) as user_count
                FROM command_stats
                WHERE date(used_at) = date('now')
            ''') as cursor:
                stats['active_users'] = (await cursor.fetchone())[0]
            
            # Get new notes count
            async with db.execute('''
                SELECT COUNT(*) as note_count
                FROM notes
                WHERE date(created_at) = date('now')
            ''') as cursor:
                stats['new_notes'] = (await cursor.fetchone())[0]
            
            # Get new bookmarks count
            async with db.execute('''
                SELECT COUNT(*) as bookmark_count
                FROM bookmarks
                WHERE date(created_at) = date('now')
            ''') as cursor:
                stats['new_bookmarks'] = (await cursor.fetchone())[0]
            
            return stats

    async def store_daily_stats(self, stats: Dict[str, Any]):
        """Store daily statistics"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute('''
                INSERT INTO daily_stats (date, stats)
                VALUES (date('now'), ?)
                ON CONFLICT(date) DO UPDATE SET
                    stats = excluded.stats
            ''', (json.dumps(stats),))
            await db.commit()

    async def get_user_stats(self, chat_id: int, user_id: int) -> Dict[str, Any]:
        """Get user statistics for a specific chat"""
        async with aiosqlite.connect(self.db_path) as db:
            # Get user's join date and message count
            async with db.execute('''
                SELECT 
                    MIN(used_at) as join_date,
                    COUNT(*) as message_count
                FROM command_stats 
                WHERE chat_id = ? AND user_id = ?
            ''', (chat_id, user_id)) as cursor:
                result = await cursor.fetchone()
                if result:
                    return {
                        'join_date': result[0],
                        'message_count': result[1]
                    }
                return None

    # Bookmarks Methods
    async def add_bookmark(self, user_id: int, url: str, title: str, description: str = "", tags: List[str] = None):
        """Add a bookmark"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute('''
                INSERT INTO bookmarks (user_id, url, title, description, tags)
                VALUES (?, ?, ?, ?, ?)
            ''', (user_id, url, title, description, json.dumps(tags or [])))
            await db.commit()

    async def get_bookmarks(self, user_id: int, tag: str = None) -> List[Dict[str, Any]]:
        """Get user's bookmarks, optionally filtered by tag"""
        async with aiosqlite.connect(self.db_path) as db:
            query = 'SELECT id, url, title, description, tags FROM bookmarks WHERE user_id = ?'
            params = [user_id]
            if tag:
                query += ' AND tags LIKE ?'
                params.append(f'%{tag}%')
            
            async with db.execute(query, params) as cursor:
                bookmarks = await cursor.fetchall()
                return [
                    {
                        'id': b[0],
                        'url': b[1],
                        'title': b[2],
                        'description': b[3],
                        'tags': json.loads(b[4])
                    }
                    for b in bookmarks
                ]

    async def create_backup(self, backup_path: str):
        """Create a backup of the database"""
        async with aiosqlite.connect(self.db_path) as db:
            backup_conn = sqlite3.connect(backup_path)
            await db.backup(backup_conn)
            backup_conn.close()

    async def update_inactive_users(self, before_date: datetime):
        """Mark users as inactive if not seen since before_date"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute('''
                UPDATE user_preferences
                SET is_active = FALSE
                WHERE last_active < ?
            ''', (before_date.isoformat(),))
            await db.commit()

    async def check_health(self) -> Dict[str, Any]:
        """Check database health"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                # Check if we can write to the database
                await db.execute('CREATE TABLE IF NOT EXISTS health_check (id INTEGER PRIMARY KEY)')
                await db.execute('DROP TABLE health_check')
                
                # Check if all tables exist
                tables = ['user_preferences', 'reminders', 'notes', 'custom_commands',
                         'command_stats', 'bookmarks', 'daily_stats', 'warnings']
                missing_tables = []
                
                for table in tables:
                    async with db.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                                        (table,)) as cursor:
                        if not await cursor.fetchone():
                            missing_tables.append(table)
                
                return {
                    'healthy': len(missing_tables) == 0,
                    'message': 'All systems operational' if len(missing_tables) == 0 else f'Missing tables: {missing_tables}'
                }
        except Exception as e:
            return {
                'healthy': False,
                'message': f'Database error: {str(e)}'
            }

    async def get_user_warnings(self, chat_id: int, user_id: int) -> List[Dict[str, Any]]:
        """Get all warnings for a user in a chat"""
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute('''
                SELECT id, reason, timestamp, admin_id
                FROM warnings 
                WHERE chat_id = ? AND user_id = ?
                ORDER BY timestamp DESC
            ''', (chat_id, user_id)) as cursor:
                rows = await cursor.fetchall()
                warnings = []
                for row in rows:
                    warnings.append({
                        'id': row[0],
                        'reason': row[1],
                        'timestamp': row[2],
                        'admin_id': row[3]
                    })
                return warnings
