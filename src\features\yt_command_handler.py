from typing import Dict, List, Optional
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, Document
from telegram.ext import ContextTypes
from telegram.constants import ParseMode
from .youtube_transcriber import YouTubeTranscriber

class YouTubeCommandHandler:
    def __init__(self):
        self.transcriber = YouTubeTranscriber()

    async def transcribe_yt_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle the /transcribe_yt command for YouTube videos"""
        if not update.message:
            return

        # If no arguments provided, show help message with inline keyboard
        if not context.args:
            keyboard = [
                [
                    InlineKeyboardButton("📝 Summary", callback_data="yt_summary"),
                    InlineKeyboardButton("🔑 Key Points", callback_data="yt_key_points"),
                    InlineKeyboardButton("🔍 Insights", callback_data="yt_insights")
                ],
                [
                    InlineKeyboardButton("📄 Raw Transcript", callback_data="yt_raw")
                ]
            ]

            help_text = (
                "🎥 <b>YouTube Transcription</b>\n\n"
                "<b>Usage:</b>\n"
                "1️⃣ <code>/transcribe_yt [YouTube URL]</code>\n"
                "2️⃣ Add option (optional):\n"
                "• <code>--summary</code> or <code>-s</code>\n"
                "• <code>--key_points</code> or <code>-k</code>\n"
                "• <code>--insights</code> or <code>-i</code>\n"
                "• <code>--raw</code> or <code>-r</code>\n\n"
                "<b>Examples:</b>\n"
                "<code>/transcribe_yt https://youtu.be/example --summary</code>\n"
                "<code>/transcribe_yt https://youtu.be/example -k</code>\n\n"
                "Or click a button below to select analysis type:"
            )

            await update.message.reply_text(
                help_text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode=ParseMode.HTML
            )
            return

        # Parse command arguments
        video_url = None
        analysis_type = None  # Changed from "summary" to None to trigger the selection prompt
        
        for arg in context.args:
            if arg.startswith('--'):
                # Handle command options
                option = arg[2:].lower()
                if option in ["summary", "key_points", "insights", "raw"]:
                    analysis_type = option
            elif arg.startswith('-'):
                # Handle short options
                option = arg[1:].lower()
                if option in ["s", "k", "i", "r"]:
                    option_map = {"s": "summary", "k": "key_points", "i": "insights", "r": "raw"}
                    analysis_type = option_map[option]
            else:
                # First non-option argument is the URL
                if not video_url:
                    video_url = arg
        
        if not video_url:
            keyboard = [
                [
                    InlineKeyboardButton("📝 Summary", callback_data="yt_summary"),
                    InlineKeyboardButton("🔑 Key Points", callback_data="yt_key_points"),
                    InlineKeyboardButton("🔍 Insights", callback_data="yt_insights")
                ],
                [
                    InlineKeyboardButton("📄 Raw Transcript", callback_data="yt_raw")
                ]
            ]

            help_text = (
                "🎥 <b>YouTube Transcription</b>\n\n"
                "<b>Usage:</b>\n"
                "1️⃣ <code>/transcribe_yt [YouTube URL]</code>\n"
                "2️⃣ Add option (optional):\n"
                "• <code>--summary</code> or <code>-s</code>\n"
                "• <code>--key_points</code> or <code>-k</code>\n"
                "• <code>--insights</code> or <code>-i</code>\n"
                "• <code>--raw</code> or <code>-r</code>\n\n"
                "<b>Examples:</b>\n"
                "<code>/transcribe_yt https://youtu.be/example --summary</code>\n"
                "<code>/transcribe_yt https://youtu.be/example -k</code>\n\n"
                "Or click a button below to select analysis type:"
            )

            await update.message.reply_text(
                help_text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode=ParseMode.HTML
            )
            return

        # If URL is provided but no analysis type, show analysis type selection
        if analysis_type is None:
            keyboard = [
                [
                    InlineKeyboardButton("📝 Summary", callback_data=f"yt_summary_{video_url}"),
                    InlineKeyboardButton("🔑 Key Points", callback_data=f"yt_key_points_{video_url}"),
                    InlineKeyboardButton("🔍 Insights", callback_data=f"yt_insights_{video_url}")
                ],
                [
                    InlineKeyboardButton("📄 Raw Transcript", callback_data=f"yt_raw_{video_url}")
                ]
            ]

            help_text = (
                "🎥 <b>YouTube Transcription</b>\n\n"
                "<b>Please select the type of analysis you want:</b>\n\n"
                "• 📝 <b>Summary</b> - Concise video summary\n"
                "• 🔑 <b>Key Points</b> - Main takeaways\n"
                "• 🔍 <b>Insights</b> - Detailed analysis\n"
                "• 📄 <b>Raw Transcript</b> - Full transcript in JSON"
            )

            await update.message.reply_text(
                help_text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode=ParseMode.HTML
            )
            return

        # If analysis type is provided, proceed with processing
        analysis_type = analysis_type or "summary"  # Default to summary if somehow still None

        # Send processing message
        processing_message = await update.message.reply_text(
            "🔄 <b>Processing video...</b>",
            parse_mode=ParseMode.HTML
        )

        try:
            result = await self.transcriber.process_video(video_url, analysis_type)

            if result["status"] == "success":
                if analysis_type == "raw":
                    # Send the raw transcript file
                    with open(result["file_path"], 'rb') as transcript_file:
                        await update.message.reply_document(
                            document=transcript_file,
                            filename=f"transcript_{video_url.split('/')[-1]}.json",
                            caption="📄 Here's your raw transcript in JSON format."
                        )
                    await processing_message.delete()
                else:
                    # Format transcript excerpts (first few segments)
                    transcript_preview = "\n".join(
                        [f"[{seg.get('start', '0:00')}] {seg['text']}" 
                         for seg in result["transcript"][:5]]
                    )

                    response = (
                        f"🎥 <b>Video Transcript Analysis</b>\n\n"
                        f"<b>Analysis Type:</b> {analysis_type.replace('_', ' ').title()}\n\n"
                        f"<b>📊 Analysis:</b>\n{result['analysis']}\n\n"
                        f"<b>📝 Transcript Preview:</b>\n<pre>{transcript_preview}</pre>\n\n"
                        f"<i>... and more</i>"
                    )

                    await processing_message.edit_text(
                        response,
                        parse_mode=ParseMode.HTML
                    )
            else:
                await processing_message.edit_text(
                    f"❌ <b>Error:</b> {result['error']}",
                    parse_mode=ParseMode.HTML
                )

        except Exception as e:
            await processing_message.edit_text(
                f"❌ <b>Error:</b> {str(e)}",
                parse_mode=ParseMode.HTML
            )

    async def handle_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle callback queries for transcription analysis type selection"""
        query = update.callback_query
        if not query:
            return

        await query.answer()

        if query.data.startswith("yt_"):
            # Extract analysis type and video URL from callback data
            callback_data = query.data[3:]  # Remove 'yt_' prefix
            if '_' in callback_data:
                analysis_type, video_url = callback_data.split('_', 1)
                
                # Send processing message
                processing_message = await query.message.reply_text(
                    "🔄 <b>Processing video...</b>",
                    parse_mode=ParseMode.HTML
                )

                try:
                    result = await self.transcriber.process_video(video_url, analysis_type)

                    if result["status"] == "success":
                        if analysis_type == "raw":
                            # Send the raw transcript file
                            with open(result["file_path"], 'rb') as transcript_file:
                                await query.message.reply_document(
                                    document=transcript_file,
                                    filename=f"transcript_{video_url.split('/')[-1]}.json",
                                    caption="📄 Here's your raw transcript in JSON format."
                                )
                            await processing_message.delete()
                        else:
                            # Format transcript excerpts (first few segments)
                            transcript_preview = "\n".join(
                                [f"[{seg.get('start', '0:00')}] {seg['text']}" 
                                 for seg in result["transcript"][:5]]
                            )

                            response = (
                                f"🎥 <b>Video Transcript Analysis</b>\n\n"
                                f"<b>Analysis Type:</b> {analysis_type.replace('_', ' ').title()}\n\n"
                                f"<b>📊 Analysis:</b>\n{result['analysis']}\n\n"
                                f"<b>📝 Transcript Preview:</b>\n<pre>{transcript_preview}</pre>\n\n"
                                f"<i>... and more</i>"
                            )

                            await processing_message.edit_text(
                                response,
                                parse_mode=ParseMode.HTML
                            )
                    else:
                        await processing_message.edit_text(
                            f"❌ <b>Error:</b> {result['error']}",
                            parse_mode=ParseMode.HTML
                        )

                except Exception as e:
                    await processing_message.edit_text(
                        f"❌ <b>Error:</b> {str(e)}",
                        parse_mode=ParseMode.HTML
                    )
            else:
                # Handle case when no video URL is in callback data (initial help menu)
                help_text = (
                    f"🎥 <b>YouTube Transcription - {callback_data.replace('_', ' ').title()}</b>\n\n"
                    f"Please send the YouTube URL to get a {callback_data.replace('_', ' ')} analysis."
                )

                await query.edit_message_text(
                    help_text,
                    parse_mode=ParseMode.HTML
                )
