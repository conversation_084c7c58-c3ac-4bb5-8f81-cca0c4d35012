"""
🔍 Felo AI Search Engine
------------------------
Direct answer AI search engine (like Perplexity).
Powered by <PERSON><PERSON>
"""

from telegram import Update
from telegram.ext import ContextTypes
import re
import requests
from uuid import uuid4
import json
import html
import asyncio
from telegram.constants import ParseMode

class FeloAI:
    def __init__(
        self,
        timeout: int = 60,
        proxies: dict = {},
        history_offset: int = 10250,
    ):
        self.session = requests.Session()
        self.chat_endpoint = "https://api.felo.ai/search/threads"
        self.timeout = timeout
        self.last_response = {}
        self.headers = {
            "accept": "*/*",
            "accept-encoding": "gzip, deflate, br, zstd",
            "content-type": "application/json",
        }
        self.session.headers.update(self.headers)
        self.history_offset = history_offset
        self.session.proxies = proxies

    def ask(self, prompt: str, stream: bool = False, raw: bool = False) -> dict:
        payload = {
            "query": prompt,
            "search_uuid": uuid4().hex,
            "search_options": {"langcode": "en-US"},
            "search_video": True,
        }

        def for_stream():
            response = self.session.post(
                self.chat_endpoint, json=payload, stream=True, timeout=self.timeout
            )
            streaming_text = ""
            for line in response.iter_lines(decode_unicode=True):
                if line.startswith('data:'):
                    try:
                        data = json.loads(line[5:].strip())
                        if 'text' in data['data']:
                            new_text = data['data']['text']
                            delta = new_text[len(streaming_text):]
                            streaming_text = new_text
                            self.last_response.update(dict(text=streaming_text))
                            yield delta if not raw else line
                    except json.JSONDecodeError:
                        pass

        def for_non_stream():
            return ''.join([chunk for chunk in for_stream()])

        return for_stream() if stream else for_non_stream()

    def chat(self, prompt: str, stream: bool = False) -> str:
        return self.ask(prompt, stream=False)

    def get_message(self, response: dict) -> str:
        return re.sub(r'\[\[\d+\]\]', '', response.get("text", ""))

class FeloHandler:
    def __init__(self):
        """Initialize Felo handler."""
        self.ai = FeloAI()
        self.active_chats = {}  # Store active chat sessions

    async def handle_webai(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle the /webai command for AI search."""
        if not update.message or not update.message.text:
            await update.message.reply_text(
                "<i>🤔 Please provide a query after /webai</i>",
                parse_mode=ParseMode.HTML
            )
            return

        # Get the query from the message
        query = update.message.text.replace("/webai", "", 1).strip()
        if not query:
            help_message = (
                "<b>🤖 AI Search Assistant</b>\n\n"
                "<i>Ask me anything! For example:</i>\n\n"
                "• /webai What are the latest developments in AI? 🤖\n"
                "• /webai Explain quantum computing 🔬\n"
                "• /webai Current global tech trends 🌐"
            )
            await update.message.reply_text(help_message, parse_mode=ParseMode.HTML)
            return

        try:
            # Send initial processing message
            initial_message = await update.message.reply_text(
                "<i>🔄 Initializing AI search...</i>",
                parse_mode=ParseMode.HTML
            )

            # Send typing action
            await update.message.chat.send_action(action="typing")

            # Get the response from Felo AI
            response = self.ai.chat(query)
            
            # Clean and format the response
            cleaned_response = re.sub(r'\[\[\d+\]\]', '', response)
            
            # Convert markdown-style code blocks to HTML
            cleaned_response = re.sub(
                r'```(\w+)?\n(.*?)\n```',
                lambda m: f'<pre>{html.escape(m.group(2))}</pre>',
                cleaned_response,
                flags=re.DOTALL
            )
            
            # Convert inline code to HTML
            cleaned_response = re.sub(
                r'`(.*?)`',
                lambda m: f'<code>{html.escape(m.group(1))}</code>',
                cleaned_response
            )

            # Use HTML tags like <b> for bold text, <code> for code, etc.
            # Format stages for editing
            stages = [
                "<i>🔍 Searching...</i>",
                f"<b>🔍 AI Search Results</b>\n\n"
                f"<b>Query:</b>\n"
                f"<code>{html.escape(query)}</code>\n\n"
                f"<b>Response:</b>\n"
                f"{cleaned_response}\n\n"
                f"<i>⏳ This message will automatically delete in 60 seconds</i>\n"
                f"<i>📌 Tip: Save important information before it's deleted</i>"
            ]
            # Edit message through stages
            for stage in stages[:-1]:
                await initial_message.edit_text(
                    stage,
                    parse_mode=ParseMode.HTML
                )
                await asyncio.sleep(0.8)  # Slightly faster transitions

            # Final edit with the complete response
            sent_message = await initial_message.edit_text(
                stages[-1],
                parse_mode=ParseMode.HTML
            )
            
            # Schedule message deletion after 1 minute
            async def delete_messages():
                await asyncio.sleep(60)
                try:
                    await update.message.delete()
                    await sent_message.delete()
                except Exception:
                    pass
                    
            asyncio.create_task(delete_messages())

        except Exception as e:
            error_message = (
                "<b>❌ Error Occurred</b>\n\n"
                "<i>Details:</i>\n"
                "• The AI search engine encountered an issue\n"
                "• Your query could not be processed at this time\n\n"
                "<b>Suggestions:</b>\n"
                "• Try rephrasing your query\n"
                "• Wait a few moments and try again\n"
                "• Make sure your query is clear and specific"
            )
            if 'initial_message' in locals():
                await initial_message.edit_text(error_message, parse_mode=ParseMode.HTML)
            else:
                await update.message.reply_text(error_message, parse_mode=ParseMode.HTML)
