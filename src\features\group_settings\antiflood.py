import asyncio
import traceback
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor
from time import time
from typing import Dict, List, Optional
from datetime import datetime, timedelta

from telegram import Update, ChatPermissions, Message, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.constants import ParseMode
from telegram.ext import ContextTypes, CommandHandler, MessageHandler, filters, CallbackQueryHandler
from telegram.error import TelegramError
import logging

from ...database.db import Database
from ...utils.decorators import admin_only, group_only

logger = logging.getLogger(__name__)

# Predefined antiflood presets
ANTIFLOOD_PRESETS = {
    "default": {
        "name": "Default",
        "description": "Linear protection: 30 messages in 30 seconds",
        "limit": 30,
        "time": 30,
        "action": "mute",
        "mute_time": 300,  # 5 minutes
        "warn_first": False,  # No warning, immediate action
        "delete_messages": True  # Delete spam messages
    },
    "relaxed": {
        "name": "Relaxed",
        "description": "Basic protection: 30 messages in 30 seconds",
        "limit": 30,
        "time": 30,
        "action": "mute",
        "mute_time": 300,  # 5 minutes
        "warn_first": False,
        "delete_messages": True
    },
    "moderate": {
        "name": "Moderate",
        "description": "Balanced protection: 15 messages in 15 seconds",
        "limit": 15,
        "time": 15,
        "action": "mute",
        "mute_time": 300,  # 5 minutes
        "warn_first": False,
        "delete_messages": True
    },
    "strict": {
        "name": "Strict",
        "description": "Strong protection: 10 messages in 10 seconds",
        "limit": 10,
        "time": 10,
        "action": "kick",
        "mute_time": 300,  # 5 minutes
        "warn_first": False,
        "delete_messages": True
    },
    "savage": {
        "name": "Savage",
        "description": "Zero tolerance: 5 messages in 5 seconds",
        "limit": 5,
        "time": 5,
        "action": "ban",
        "mute_time": 300,  # 5 minutes
        "warn_first": False,
        "delete_messages": True
    },
    "custom": {
        "name": "Custom",
        "description": "User-defined settings",
        "limit": 20,
        "time": 20,
        "action": "mute",
        "mute_time": 300,  # 5 minutes
        "warn_first": False,
        "delete_messages": True
    }
}

class AntiFloodHandler:
    def __init__(self, db: Database):
        self.db = db
        self.user_messages = defaultdict(list)
        self.repeat_offenders = defaultdict(int)
        self.warned_users = {}
        self.chat_settings = {}
        self.settings_cache_time = {}
        self.SETTINGS_CACHE_DURATION = 300
        self.message_handlers = {}  # Store message handlers for cleanup
        # Notification tracking removed - we no longer send public spam reports
        self.message_ids = {}  # Track message IDs for deletion
        self.currently_punishing = set()  # Track users currently being punished
        self.executor = ThreadPoolExecutor(max_workers=3)  # Thread pool for concurrent operations - reduced to avoid overloading
        self.punishment_tasks = set()  # Track running punishment tasks
        self.thread_locks = {}  # Locks for thread synchronization

    async def initialize(self):
        """Initialize the anti-flood handler"""
        # Clear any stale data
        self.user_messages.clear()
        self.repeat_offenders.clear()
        self.warned_users.clear()
        self.chat_settings.clear()
        self.settings_cache_time.clear()
        self.message_ids.clear()  # Clear message ID tracking
        self.currently_punishing.clear()  # Clear currently punishing set

        # Clear punishment tasks
        for task in self.punishment_tasks.copy():
            if not task.done():
                task.cancel()
        self.punishment_tasks.clear()

        # Log initialization
        logger.info("AntiFloodHandler initialized with the following settings:")
        logger.info("Default preset: 30 messages in 30 seconds")

    async def get_chat_settings(self, chat_id: int) -> dict:
        current_time = time()
        if (chat_id not in self.chat_settings or
            current_time - self.settings_cache_time.get(chat_id, 0) > self.SETTINGS_CACHE_DURATION):

            preset = await self.db.get_group_setting(chat_id, "antiflood_preset", "default")
            custom_settings = preset == "custom"

            base_settings = ANTIFLOOD_PRESETS[preset].copy()

            if custom_settings:
                base_settings.update({
                    "limit": int(await self.db.get_group_setting(chat_id, "antiflood_limit", base_settings["limit"])),
                    "time": int(await self.db.get_group_setting(chat_id, "antiflood_time", base_settings["time"])),
                    "action": await self.db.get_group_setting(chat_id, "antiflood_action", base_settings["action"]),
                    "mute_time": int(await self.db.get_group_setting(chat_id, "antiflood_mute_time", base_settings["mute_time"])),
                    "warn_first": await self.db.get_group_setting(chat_id, "antiflood_warn_first", base_settings["warn_first"]),
                    "delete_messages": await self.db.get_group_setting(chat_id, "antiflood_delete_messages", base_settings.get("delete_messages", True))
                })

            base_settings["enabled"] = await self.db.get_group_setting(chat_id, "antiflood_enabled", True)
            self.chat_settings[chat_id] = base_settings
            self.settings_cache_time[chat_id] = current_time

        return self.chat_settings[chat_id]

    async def check_flood(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> bool:
        if not update.effective_message or not update.effective_chat:
            return False

        chat_id = update.effective_chat.id
        user_id = update.effective_user.id
        current_time = time()

        # Skip admin check for testing
        # Quick admin check with caching
        admin_key = f"admin:{chat_id}:{user_id}"
        is_admin = context.bot_data.get(admin_key)
        if is_admin is None:
            try:
                member = await context.bot.get_chat_member(chat_id, user_id)
                is_admin = member.status in ['administrator', 'creator']
                logger.info(f"User {user_id} in chat {chat_id} is admin: {is_admin}")
                context.bot_data[admin_key] = is_admin
                # Fix: Make the callback async
                async def clear_admin_cache(context):
                    context.bot_data.pop(admin_key, None)
                context.job_queue.run_once(clear_admin_cache, 3600)
            except TelegramError as e:
                logger.error(f"Error checking admin status: {e}")
                return False

        # For testing purposes, don't skip admins
        # if is_admin:
        #     logger.info(f"Skipping flood check for admin {user_id} in chat {chat_id}")
        #     return False

        settings = await self.get_chat_settings(chat_id)
        if not settings["enabled"]:
            return False

        msg_key = f"{chat_id}:{user_id}"
        messages = self.user_messages[msg_key]

        # Clean up old messages first
        cutoff_time = current_time - settings["time"]
        while messages and messages[0] < cutoff_time:
            old_time = messages.pop(0)
            # Also clean up the corresponding message ID
            self.message_ids.pop(f"{msg_key}:{old_time}", None)

        # Add the current message to the user's message history
        messages.append(current_time)
        msg_count = len(messages)

        # Store the message ID for potential deletion later
        if update.effective_message and update.effective_message.message_id:
            self.message_ids[f"{msg_key}:{current_time}"] = update.effective_message.message_id

        # Linear flood detection - check if user has sent too many messages in the time window
        if msg_count > settings["limit"]:
            logger.info(f"Flood detected: User {user_id} sent {msg_count} messages in {settings['time']} seconds in chat {chat_id}")
            # Start punishment in a separate thread to avoid blocking
            punishment_key = f"{chat_id}:{user_id}"
            if punishment_key not in self.currently_punishing:
                logger.info(f"Starting punishment for user {user_id} in chat {chat_id} for flood in Thread 2")
                self.currently_punishing.add(punishment_key)

                # Create a wrapper function that will run in the thread
                def run_punishment_in_thread():
                    # Create a new event loop for the thread
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        # Create a copy of the update and context to avoid shared state issues
                        # We'll just pass the essential information needed for punishment
                        punishment_data = {
                            'chat_id': chat_id,
                            'user_id': user_id,
                            'message_id': update.effective_message.message_id if update.effective_message else None,
                            'violation_type': "flood"
                        }

                        # Run the punishment in the thread's event loop
                        loop.run_until_complete(self._thread_safe_punish(punishment_data, settings, msg_key))
                        logger.info(f"Punishment thread completed for user {user_id} in chat {chat_id} for flood")
                    except Exception as e:
                        logger.error(f"Error in punishment thread: {e}")
                        logger.error(traceback.format_exc())
                    finally:
                        # Clean up
                        try:
                            loop.close()
                        except Exception as e:
                            logger.error(f"Error closing event loop: {e}")
                        self.currently_punishing.discard(punishment_key)

                # Submit the task to the thread pool
                self.executor.submit(run_punishment_in_thread)
                logger.info(f"Submitted flood punishment task to Thread 2 for user {user_id} in chat {chat_id}")
            else:
                logger.info(f"User {user_id} is already being punished, skipping duplicate punishment")
            return True



        # We already checked for flood above, so if we get here, it's not a flood
        return False

    async def punish_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE, settings: dict, msg_key: str, violation_type: str):
        chat_id = update.effective_chat.id
        user_id = update.effective_user.id
        current_time = time()

        # Check if this user is already being punished to prevent duplicate actions
        punishment_key = f"{chat_id}:{user_id}"

        # If this user is already being punished, just return
        if punishment_key in self.currently_punishing:
            logger.debug(f"User {user_id} in chat {chat_id} is already being punished, skipping duplicate punishment")
            return

        # Mark this user as currently being punished
        self.currently_punishing.add(punishment_key)

        try:
            # We still track offenses for logging purposes, but don't use it for mute time
            self.repeat_offenders[msg_key] += 1

            # Skip warnings - we want immediate action
            # We're keeping this code commented out for reference
            # if settings["warn_first"] and offense_count == 1:
            #     last_warning = self.warned_users.get(msg_key, 0)
            #     if current_time - last_warning > 300:  # 5 minutes between warnings
            #         self.warned_users[msg_key] = current_time
            #         await context.bot.send_message(
            #             chat_id=chat_id,
            #             text=f"⚠️ FINAL WARNING! {update.effective_user.mention_html()}\n"
            #                  f"Stop flooding or face immediate consequences!",
            #             parse_mode=ParseMode.HTML
            #         )
            #         self.user_messages[msg_key].clear()
            #         return

            action = settings["action"]
            mute_time = settings["mute_time"]  # Always use the base mute time

            # Get the violation text
            violation_text = {
                "flood": "FLOOD DETECTED! 🌊"
            }.get(violation_type, "FLOOD DETECTED! 🌊")

            # Step 1: Apply the punishment
            try:
                logger.info(f"Attempting to {action} user {user_id} in chat {chat_id} for {violation_type}")
                if action == "mute":
                    logger.info(f"Muting user {user_id} until {int(current_time + mute_time)} (current time: {int(current_time)})")
                    # Create permissions object with all permissions set to False
                    permissions = ChatPermissions(
                        can_send_messages=False,
                        can_send_polls=False,
                        can_send_other_messages=False,
                        can_add_web_page_previews=False,
                        can_change_info=False,
                        can_invite_users=False,
                        can_pin_messages=False,
                        can_manage_topics=False,
                        can_send_audios=False,
                        can_send_documents=False,
                        can_send_photos=False,
                        can_send_videos=False,
                        can_send_video_notes=False,
                        can_send_voice_notes=False,
                    )
                    await context.bot.restrict_chat_member(
                        chat_id, user_id,
                        permissions=permissions,
                        until_date=int(current_time + mute_time)
                    )
                elif action == "kick":
                    logger.info(f"Kicking user {user_id} from chat {chat_id}")
                    await context.bot.ban_chat_member(
                        chat_id, user_id,
                        until_date=int(current_time + 30)
                    )
                elif action == "ban":
                    logger.info(f"Banning user {user_id} from chat {chat_id}")
                    await context.bot.ban_chat_member(chat_id, user_id)

                # Log the action for debugging
                offense_count = self.repeat_offenders[msg_key]
                logger.info(f"Anti-flood action: {action} for user {user_id} in chat {chat_id} - Violation: {violation_type} - Offense count: {offense_count}")
            except TelegramError as e:
                logger.error(f"Error punishing user: {e}")
                logger.error(f"Failed to {action} user {user_id} in chat {chat_id} for {violation_type}")
                # If we can't punish the user, we still continue to try to delete messages

            # Step 3: Silently delete spam messages if enabled
            if settings.get("delete_messages", True):
                try:
                    # Delete the current message first
                    if update.effective_message:
                        try:
                            await update.effective_message.delete()
                        except Exception as e:
                            logger.debug(f"Failed to delete current message: {e}")

                    # Get message IDs from our tracking
                    message_ids_to_delete = []
                    time_window = settings["time"]

                    # Find messages from this user in our tracking
                    for msg_time in self.user_messages[msg_key]:
                        if current_time - msg_time <= time_window:
                            # If we have the message ID stored, add it to deletion list
                            if f"{msg_key}:{msg_time}" in self.message_ids:
                                message_ids_to_delete.append(self.message_ids[f"{msg_key}:{msg_time}"])

                    # Delete messages in batches to avoid rate limits
                    if message_ids_to_delete:
                        logger.info(f"Silently deleting {len(message_ids_to_delete)} spam messages from user {user_id} in chat {chat_id}")

                        # Try to use the bulk delete API if available
                        try:
                            # Check if the bot has the delete_messages method (newer versions of python-telegram-bot)
                            if hasattr(context.bot, 'delete_messages'):
                                await context.bot.delete_messages(chat_id=chat_id, message_ids=message_ids_to_delete)
                                logger.info(f"Successfully deleted {len(message_ids_to_delete)} messages using bulk API")
                            else:
                                # Fall back to individual deletion
                                raise AttributeError("delete_messages method not available")
                        except Exception as e:
                            logger.warning(f"Bulk message deletion failed: {e}. Falling back to individual deletion.")

                            # Delete all messages in one go (still in batches for rate limits)
                            for i in range(0, len(message_ids_to_delete), 10):
                                batch = message_ids_to_delete[i:i+10]
                                for msg_id in batch:
                                    try:
                                        await context.bot.delete_message(chat_id=chat_id, message_id=msg_id)
                                        await asyncio.sleep(0.05)  # Smaller delay to speed up deletion
                                    except Exception as e:
                                        logger.debug(f"Failed to delete message {msg_id}: {e}")
                except Exception as e:
                    logger.error(f"Failed to delete spam messages: {e}")

            # Clear all tracking data for this user
            self.user_messages[msg_key].clear()
        finally:
            # Always remove the user from the currently punishing set, even if an error occurs
            self.currently_punishing.discard(punishment_key)
            logger.info(f"Removed user {user_id} from currently_punishing set")
            # Schedule removal from the set after a short delay as an extra precaution
            try:
                context.job_queue.run_once(
                    lambda ctx: self.currently_punishing.discard(punishment_key),
                    10  # Remove after 10 seconds as a safety measure
                )
            except Exception as e:
                logger.error(f"Error scheduling cleanup job: {e}")

    async def _delete_after_delay(self, message, delay: int) -> None:
        """Delete a message after a specified delay in seconds."""
        if message:
            await asyncio.sleep(delay)
            try:
                await message.delete()
            except TelegramError as e:
                logging.debug(f"Failed to delete message: {e}")

    @admin_only()
    @group_only()
    async def set_antiflood(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        chat_id = update.effective_chat.id
        messages_to_delete = []

        # Add command message to deletion list
        if update.message:
            messages_to_delete.append(update.message)

        if not context.args:
            # Show preset selection keyboard
            keyboard = []
            for preset_id, preset in ANTIFLOOD_PRESETS.items():
                keyboard.append([InlineKeyboardButton(
                    f"{preset['name']} - {preset['description']}",
                    callback_data=f"antiflood_preset_{preset_id}"
                )])

            current_settings = await self.get_chat_settings(chat_id)

            await update.message.reply_text(
                "🌊 *AntiFlood Settings*\n\n"
                f"Current Preset: `{current_settings.get('name', 'Default')}`\n"
                f"• Enabled: `{current_settings['enabled']}`\n"
                f"• Message Limit: `{current_settings['limit']} messages`\n"
                f"• Time Window: `{current_settings['time']} seconds`\n"
                f"• Action: `{current_settings['action']}`\n"
                f"• Mute Duration: `{current_settings['mute_time']} seconds`\n"
                f"• Warn First: `{current_settings['warn_first']}`\n"
                f"• Delete Messages: `{current_settings.get('delete_messages', True)}`\n\n"
                "*Select a preset or use:*\n"
                "`/setflood [messages] [seconds] [action] [mute_time] [warn_first] [delete_messages]`\n"
                "Example: `/setflood 20 20 mute 300 false true`\n\n"
                "*Default is 20 messages in 20 seconds with immediate action and 5 minute mute*",
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode=ParseMode.MARKDOWN
            )
            return

        try:
            # Custom settings
            limit = max(1, min(50, int(context.args[0])))
            time_window = max(1, min(300, int(context.args[1])))
            action = context.args[2] if len(context.args) > 2 else "mute"
            mute_time = max(30, min(86400, int(context.args[3]))) if len(context.args) > 3 else 300
            warn_first = context.args[4].lower() == "true" if len(context.args) > 4 else False  # Default to no warning
            delete_messages = context.args[5].lower() == "true" if len(context.args) > 5 else True  # Default to deleting messages

            if action not in ["mute", "kick", "ban"]:
                action = "mute"

            # Save custom settings
            await self.db.set_group_setting(chat_id, "antiflood_preset", "custom")
            await self.db.set_group_setting(chat_id, "antiflood_limit", limit)
            await self.db.set_group_setting(chat_id, "antiflood_time", time_window)
            await self.db.set_group_setting(chat_id, "antiflood_action", action)
            await self.db.set_group_setting(chat_id, "antiflood_mute_time", mute_time)
            await self.db.set_group_setting(chat_id, "antiflood_warn_first", warn_first)
            await self.db.set_group_setting(chat_id, "antiflood_delete_messages", delete_messages)

            # Clear cache to force refresh
            self.chat_settings.pop(chat_id, None)

            await update.message.reply_text(
                "✅ Custom AntiFlood settings updated!\n\n"
                f"• Message Limit: {limit} messages\n"
                f"• Time Window: {time_window} seconds\n"
                f"• Action: {action}\n"
                f"• Mute Duration: {mute_time} seconds\n"
                f"• Warn First: {warn_first}\n"
                f"• Delete Messages: {delete_messages}"
            )

        except ValueError:
            await update.message.reply_text(
                "❌ Invalid arguments! Please use numbers for messages, seconds, and mute time."
            )

    async def handle_preset_selection(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        query = update.callback_query
        await query.answer()

        chat_id = update.effective_chat.id
        preset_id = query.data.replace("antiflood_preset_", "")

        if preset_id not in ANTIFLOOD_PRESETS:
            await query.message.edit_text("❌ Invalid preset selection!")
            return

        preset = ANTIFLOOD_PRESETS[preset_id]

        # Save preset settings
        await self.db.set_group_setting(chat_id, "antiflood_preset", preset_id)

        # Clear cache to force refresh
        self.chat_settings.pop(chat_id, None)

        await query.message.edit_text(
            f"✅ AntiFlood preset set to: {preset['name']}\n\n"
            f"• Message Limit: {preset['limit']} messages\n"
            f"• Time Window: {preset['time']} seconds\n"
            f"• Action: {preset['action']}\n"
            f"• Mute Duration: {preset['mute_time']} seconds\n"
            f"• Warn First: {preset['warn_first']}\n"
            f"• Delete Messages: {preset.get('delete_messages', True)}\n\n"
            "Settings have been updated!"
        )

    def register_handlers(self, application):
        """Register all handlers"""
        application.add_handler(CommandHandler("setflood", self.set_antiflood, filters.ChatType.GROUPS))
        application.add_handler(CallbackQueryHandler(self.handle_preset_selection, pattern=r"^antiflood_preset_"))

        # Message handlers with highest priority (group=1)
        handlers = [
            (filters.TEXT, "text"),  # Include all text messages including commands
            (filters.PHOTO | filters.VIDEO | filters.AUDIO | filters.VOICE | filters.Document.ALL | filters.VIDEO_NOTE, "media"),
            (filters.Sticker.ALL, "sticker")
        ]

        # Log registration
        logger.info("Registering AntiFloodHandler message handlers with highest priority")

        for filter_type, handler_type in handlers:
            handler = MessageHandler(
                filters.ChatType.GROUPS & filter_type & ~filters.StatusUpdate.ALL,
                self.check_flood
            )
            # Use group=1 for highest priority (lower number = higher priority)
            application.add_handler(handler, group=1)
            self.message_handlers[handler_type] = handler

        # Schedule periodic cleanup of tracking data
        if hasattr(application, 'job_queue') and application.job_queue:
            application.job_queue.run_repeating(
                callback=self._cleanup_tracking,
                interval=300,  # Run every 5 minutes
                first=300      # Start after 5 minutes
            )

        logger.info("AntiFloodHandler message handlers registered successfully")

    async def _cleanup_tracking(self, context) -> None:
        """Clean up old tracking entries to prevent memory leaks"""
        try:
            current_time = time()
            cleanup_threshold = current_time - 3600  # Remove entries older than 1 hour


            # Find message ID keys to remove
            message_keys_to_remove = []
            for key in self.message_ids.keys():
                # Message ID keys are in format "chat_id:user_id:timestamp"
                try:
                    parts = key.split(":")
                    if len(parts) >= 3:
                        msg_timestamp = float(parts[-1])
                        if current_time - msg_timestamp > 3600:  # 1 hour
                            message_keys_to_remove.append(key)
                except (ValueError, IndexError):
                    # If the key format is invalid, remove it
                    message_keys_to_remove.append(key)

            # Remove old message ID entries
            for key in message_keys_to_remove:
                del self.message_ids[key]

            if message_keys_to_remove:
                logger.debug(f"Cleaned up {len(message_keys_to_remove)} old message ID tracking entries")

            # Clear the currently_punishing set as a safety measure
            # This should normally be empty or very small, as entries are removed after punishment
            if self.currently_punishing:
                logger.debug(f"Cleaning up {len(self.currently_punishing)} stale punishment tracking entries")
                self.currently_punishing.clear()

            # Clean up any completed punishment tasks
            completed_tasks = set()
            for task in self.punishment_tasks:
                if task.done():
                    completed_tasks.add(task)

            for task in completed_tasks:
                self.punishment_tasks.discard(task)

            if completed_tasks:
                logger.debug(f"Cleaned up {len(completed_tasks)} completed punishment tasks")
        except Exception as e:
            logger.error(f"Error cleaning up tracking data: {e}")

    async def _thread_safe_punish(self, punishment_data, settings, msg_key):
        """Thread-safe version of punish_user that doesn't rely on Update or Context objects"""
        chat_id = punishment_data['chat_id']
        user_id = punishment_data['user_id']
        violation_type = punishment_data['violation_type']
        punishment_key = f"{chat_id}:{user_id}"

        try:
            current_time = time()

            # Increment the repeat offender count
            self.repeat_offenders[msg_key] += 1
            offense_count = self.repeat_offenders[msg_key]

            # Get the action from settings
            action = settings.get("action", "mute")
            mute_time = settings.get("mute_time", 300)  # Default to 5 minutes

            # Create a bot instance for this thread
            from telegram import Bot
            import os

            # Get the bot token from config
            from src.config import config
            token = config.TELEGRAM_BOT_TOKEN
            if not token:
                logger.error("Bot token not found in configuration")
                return

            # Create a bot instance
            bot = Bot(token)

            # Configure the connection pool size if possible
            if hasattr(bot.request, "connection_pool_size"):
                bot.request.connection_pool_size = 10

            # Apply the punishment
            try:
                logger.info(f"Thread-safe punishment: Attempting to {action} user {user_id} in chat {chat_id} for {violation_type}")

                if action == "mute":
                    # Create permissions object with all permissions set to False
                    from telegram import ChatPermissions
                    permissions = ChatPermissions(
                        can_send_messages=False,
                        can_send_polls=False,
                        can_send_other_messages=False,
                        can_add_web_page_previews=False,
                        can_change_info=False,
                        can_invite_users=False,
                        can_pin_messages=False,
                        can_manage_topics=False,
                        can_send_audios=False,
                        can_send_documents=False,
                        can_send_photos=False,
                        can_send_videos=False,
                        can_send_video_notes=False,
                        can_send_voice_notes=False,
                    )

                    await bot.restrict_chat_member(
                        chat_id, user_id,
                        permissions=permissions,
                        until_date=int(current_time + mute_time)
                    )
                    logger.info(f"Successfully muted user {user_id} in chat {chat_id} for {violation_type}")

                elif action == "kick":
                    await bot.ban_chat_member(
                        chat_id, user_id,
                        until_date=int(current_time + 30)
                    )
                    logger.info(f"Successfully kicked user {user_id} from chat {chat_id} for {violation_type}")

                elif action == "ban":
                    await bot.ban_chat_member(chat_id, user_id)
                    logger.info(f"Successfully banned user {user_id} from chat {chat_id} for {violation_type}")


                # Delete spam messages if enabled
                if settings.get("delete_messages", True):
                    # Get message IDs from our tracking
                    message_ids_to_delete = []
                    time_window = settings["time"]

                    # Find messages from this user in our tracking
                    for msg_time in self.user_messages[msg_key]:
                        if current_time - msg_time <= time_window:
                            # If we have the message ID stored, add it to deletion list
                            if f"{msg_key}:{msg_time}" in self.message_ids:
                                message_ids_to_delete.append(self.message_ids[f"{msg_key}:{msg_time}"])

                    # Delete messages in batches to avoid rate limits
                    if message_ids_to_delete:
                        logger.info(f"Thread-safe punishment: Deleting {len(message_ids_to_delete)} spam messages from user {user_id} in chat {chat_id}")

                        # Delete all messages in one go (still in batches for rate limits)
                        for i in range(0, len(message_ids_to_delete), 10):
                            batch = message_ids_to_delete[i:i+10]
                            for msg_id in batch:
                                try:
                                    await bot.delete_message(chat_id=chat_id, message_id=msg_id)
                                    await asyncio.sleep(0.05)  # Smaller delay to speed up deletion
                                except Exception as e:
                                    logger.debug(f"Failed to delete message {msg_id}: {e}")
            except Exception as e:
                logger.error(f"Error in thread-safe punishment: {e}")
                logger.error(traceback.format_exc())
        finally:
            # Always remove the user from the currently punishing set, even if an error occurs
            self.currently_punishing.discard(punishment_key)

if __name__ == "__main__":
    print("Enhanced AntiFloodHandler loaded with presets.")

