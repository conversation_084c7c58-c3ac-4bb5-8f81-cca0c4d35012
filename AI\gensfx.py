from typing import Dict, Optional, Union, Set
import json
import requests
from pydantic import BaseModel


class SoundEffectRequest(BaseModel):
    text: str
    duration: Optional[float] = None
    prompt_influence: float = 0.3


class SoundEffectResponse(BaseModel):
    status: str
    message: Optional[str] = None
    audio_data: Optional[bytes] = None


class GenSFX:
    """A class to interact with the GenSFX Sound Effect API.
    
    Supports:
    - Text to sound effect generation
    - Custom duration control
    - Prompt influence adjustment
    - MP3 file output
    """
    
    label: str = "GenSFX Sound Effect Generator"
    url: str = "https://gensfx.com"
    working: bool = True
    
    # API endpoint
    api_endpoint: str = "https://gensfx.com/api/sound-effects"
    
    # Default headers
    headers = {
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json',
        'Origin': 'https://gensfx.com',
        'Pragma': 'no-cache',
        'Referer': 'https://gensfx.com/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
    }

    @classmethod
    def generate_sound(
        cls,
        text: str,
        duration: Optional[float] = None,
        prompt_influence: float = 0.3,
        output_file: Optional[str] = None
    ) -> Union[bytes, str]:
        """Generate a sound effect from text description.
        
        Args:
            text: Text description of the desired sound
            duration: Optional duration in seconds
            prompt_influence: How much the prompt affects generation (0-1)
            output_file: Optional path to save the MP3 file
            
        Returns:
            If output_file is provided, saves to file and returns path
            Otherwise returns the raw audio bytes
            
        Example:
            ```python
            # Generate and save to file
            GenSFX.generate_sound("brum brum", output_file="car.mp3")
            
            # Get raw audio bytes
            audio_data = GenSFX.generate_sound("brum brum")
            ```
        """
        # Validate and prepare request
        request = SoundEffectRequest(
            text=text,
            duration=duration,
            prompt_influence=prompt_influence
        )
        
        # Make API request
        response = requests.post(
            cls.api_endpoint,
            headers=cls.headers,
            json=request.model_dump(exclude_none=True)
        )
        response.raise_for_status()
        
        # Check content type
        content_type = response.headers.get("Content-Type", "").lower()
        
        if "audio" in content_type:
            audio_data = response.content
            if output_file:
                cls._save_audio(audio_data, output_file)
                return output_file
            return audio_data
        
        # If not audio, try to parse JSON response
        try:
            data = response.json()
            return SoundEffectResponse(**data)
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid response format: {e}")

    @staticmethod
    def _save_audio(audio_data: bytes, output_file: str) -> None:
        """Save audio data to an MP3 file.
        
        Args:
            audio_data: Raw audio bytes
            output_file: Path to save the MP3 file
        """
        try:
            with open(output_file, 'wb') as f:
                f.write(audio_data)
        except IOError as e:
            raise IOError(f"Failed to save audio file: {e}")


if __name__ == "__main__":
    # Example usage
    try:
        # Example 1: Generate and save to file
        print("Generating car sound effect...")
        output_path = GenSFX.generate_sound(
            text="car engine revving sound",
            duration=2.0,
            output_file="car_rev.mp3"
        )
        print(f"Sound effect saved to: {output_path}")
        
        # Example 2: Get raw audio bytes
        print("\nGenerating thunder sound effect...")
        audio_data = GenSFX.generate_sound(
            text="loud thunder crack",
            duration=3.0
        )
        print(f"Received {len(audio_data)} bytes of audio data")
        
    except Exception as e:
        print(f"Error: {e}")
