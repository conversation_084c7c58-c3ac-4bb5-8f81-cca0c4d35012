"""
Command handlers for API-based features.
"""
from telegram import Update
from telegram.ext import ContextTypes
from telegram.constants import ParseMode, ChatAction
from ..api_services.public_apis import PublicAPIs
import html
import logging
import time
from datetime import datetime
import asyncio

logger = logging.getLogger(__name__)

class APICommandHandlers:
    def __init__(self):
        self.api_service = PublicAPIs()

    async def weather(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Get weather information for a location"""
        if not context.args:
            error_msg = await update.message.reply_text(
                "<b>🌤️ YO CHECK THE WEATHER LIKE THIS:</b>\n\n"
                "• /weather New York 🗽\n"
                "• /weather Tokyo, Japan 🗼\n"
                "• /weather London, UK 🇬🇧\n\n"
                "<i>Pro tip: Add the country for better accuracy! 🌍</i>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()
            try:
                await update.message.delete()
            except Exception:
                pass
            return

        user_command_msg = update.message
        location = " ".join(context.args)
        
        # Send loading message with style
        loading_msg = await update.message.reply_text(
            "<i>🔍 CHECKING THE VIBE IN THE SKY...</i>",
            parse_mode=ParseMode.HTML
        )
        
        start_time = time.time()
        
        try:
            weather_data = await self.api_service.get_weather(location)
            
            # Calculate response time
            response_time = round((time.time() - start_time) * 1000)  # in milliseconds

            # Get weather condition emoji based on data
            def get_weather_emoji(data):
                if data['precipitation'] > 0:
                    return "🌧️" if data['temp_c'] > 0 else "🌨️"
                elif data['cloud_cover'] > 80:
                    return "☁️"
                elif data['cloud_cover'] > 20:
                    return "⛅"
                else:
                    return "☀️" if data['is_day'] else "🌙"
            
            main_emoji = get_weather_emoji(weather_data)
            
            # Format the forecast data
            forecast_text = ""
            for day in weather_data['forecast']:
                emoji = "🌧️" if day['rain_chance'] > 50 else "☀️"
                forecast_text += (
                    f"\n<b>{day['date']}</b> {emoji}\n"
                    f"🌡️ {day['min_temp']}°C to {day['max_temp']}°C\n"
                    f"💧 {day['rain_chance']}% chance, {day['rain_amount']}mm\n"
                )

            # Format alerts if any
            alerts_text = ""
            if weather_data['alerts']:
                alerts_text = "\n<b>⚠️ WEATHER ALERTS</b>\n" + "\n".join(weather_data['alerts']) + "\n"

            # Create the weather report with modern formatting
            weather_msg = (
                f"<b>{main_emoji} WEATHER REPORT FOR {weather_data['location'].upper()}</b>\n"
                f"📍 {weather_data['lat']}, {weather_data['lon']}\n"
                f"⏱️ Response: {response_time}ms\n"
                f"{'🌞' if weather_data['is_day'] else '🌙'} {datetime.now().strftime('%I:%M %p')}\n"
                f"▔▔▔▔▔▔▔▔▔▔▔▔▔▔▔▔▔▔▔\n\n"
                
                f"<b>🌡️ TEMPERATURE</b>\n"
                f"📊 Current: {weather_data['temp_c']}°C ({weather_data['temp_f']}°F)\n"
                f"🤔 Feels Like: {weather_data['feels_like_c']}°C ({weather_data['feels_like_f']}°F)\n"
                f"📈 High/Low: {weather_data['max_temp']}°C / {weather_data['min_temp']}°C\n\n"
                
                f"<b>🌪️ WIND & PRESSURE</b>\n"
                f"💨 Speed: {weather_data['wind_speed']} km/h {weather_data['wind_dir']}\n"
                f"💫 Gusts: {weather_data['wind_gusts']} km/h\n"
                f"📊 Pressure: {weather_data['pressure']} hPa\n\n"
                
                f"<b>💧 CONDITIONS</b>\n"
                f"💦 Humidity: {weather_data['humidity']}%\n"
                f"☁️ Clouds: {weather_data['cloud_cover']}%\n"
                f"🌧️ Rain: {weather_data['rain_amount']}mm ({weather_data['rain_chance']}%)\n"
                f"⏳ Rain Hours: {weather_data['rain_hours']}h today\n"
                f"👁️ Visibility: {weather_data['visibility']}\n\n"
                
                f"<b>🌍 AIR QUALITY</b>\n"
                f"📊 AQI: {weather_data['aqi']}\n"
                f"🔍 PM2.5: {weather_data['pm25']} μg/m³\n"
                f"🔎 PM10: {weather_data['pm10']} μg/m³\n"
                f"🌫️ Ozone: {weather_data['o3']} μg/m³\n\n"
                
                f"<b>☀️ UV & SUN</b>\n"
                f"🔆 UV Index: {weather_data['uv_index']}\n"
                f"🌅 Sunrise: {weather_data['sunrise']}\n"
                f"🌇 Sunset: {weather_data['sunset']}\n"
            )

            if alerts_text:
                weather_msg += f"\n{alerts_text}"

            weather_msg += (
                f"\n<b>🗓️ FORECAST</b>{forecast_text}\n"
                f"▔▔▔▔▔▔▔▔▔▔▔▔▔▔▔▔▔▔▔\n"
                f"<i>⏳ This weather report will self-destruct in 45s!</i>"
            )
            
            # Edit the loading message with the weather info
            result_msg = await loading_msg.edit_text(
                weather_msg,
                parse_mode=ParseMode.HTML
            )
            
            # Schedule synchronized deletion
            async def delete_messages():
                await asyncio.sleep(45)
                try:
                    await asyncio.gather(
                        result_msg.delete(),
                        user_command_msg.delete()
                    )
                except Exception as e:
                    logger.error(f"Error deleting weather messages: {e}")
            
            asyncio.create_task(delete_messages())
            
        except Exception as e:
            logger.error(f"Error in weather command: {str(e)}")
            error_msg = await loading_msg.edit_text(
                "❌ <b>BRUH! SOMETHING AIN'T RIGHT!</b>\n\n"
                "<i>Couldn't get that weather info! Make sure you:</i>\n"
                "• Check the spelling 📝\n"
                "• Add country name for better results 🌍\n"
                "• Try a nearby major city 🏙️\n\n"
                f"<i>Error: {str(e)}</i>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            try:
                await asyncio.gather(
                    error_msg.delete(),
                    user_command_msg.delete()
                )
            except Exception:
                pass

    async def quote(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Send a random quote"""
        try:
            
            # Send loading message
            loading_msg = await update.message.reply_text(
                "📜 <i>Finding an inspiring quote...</i>",
                parse_mode=ParseMode.HTML
            )

            quote_data = await self.api_service.get_quote()
            message = (
                f"<b>Daily Inspiration</b> ✨\n\n"
                f"<i>{html.escape(quote_data['quote'])}</i>\n\n"
                f"— <b>{html.escape(quote_data['author'])}</b>\n\n"
                f"<i>⏳ This message will self-destruct in 30s!</i>"
            )
            
            result_msg = await update.message.reply_text(
                message,
                parse_mode=ParseMode.HTML
            )
            await loading_msg.delete()
            
            # Delete command message
            try:
                await update.message.delete()
            except Exception:
                pass

            # Schedule deletion
            async def delete_messages():
                await asyncio.sleep(30)
                try:
                    await result_msg.delete()
                except Exception:
                    pass
            
            asyncio.create_task(delete_messages())

        except Exception as e:
            logger.error(f"Error getting quote: {e}")
            error_msg = await update.message.reply_text(
                "<b>❌ Oops! Something went wrong!</b>\n"
                "<i>Couldn't fetch that quote fam!</i>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()

    async def joke(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Send a random programming joke"""
        try:
            # Send loading message
            loading_msg = await update.message.reply_text(
                "😄 <i>Finding a funny one...</i>",
                parse_mode=ParseMode.HTML
            )

            joke = await self.api_service.get_joke()
            message = (
                f"<b>🎯 Tech Humor Time!</b>\n\n"
                f"<i>{html.escape(joke)}</i>\n\n"
                f"<i>⏳ This message will self-destruct in 30s!</i>"
            )
            
            result_msg = await update.message.reply_text(
                message,
                parse_mode=ParseMode.HTML
            )
            await loading_msg.delete()
            
            # Delete command message
            try:
                await update.message.delete()
            except Exception:
                pass

            # Schedule deletion
            async def delete_messages():
                await asyncio.sleep(30)
                try:
                    await result_msg.delete()
                except Exception:
                    pass
            
            asyncio.create_task(delete_messages())

        except Exception as e:
            logger.error(f"Error getting joke: {e}")
            error_msg = await update.message.reply_text(
                "<b>❌ Oops! Something went wrong!</b>\n"
                "<i>Couldn't fetch that joke fam!</i>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()

    async def fact(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Send a random fact"""
        try:
            # Send loading message
            loading_msg = await update.message.reply_text(
                "🤔 <i>Finding an interesting fact...</i>",
                parse_mode=ParseMode.HTML
            )

            fact = await self.api_service.get_fact()
            message = (
                f"<b>📚 Did You Know?</b>\n\n"
                f"<i>{html.escape(fact)}</i>\n\n"
                f"<i>⏳ This message will self-destruct in 30s!</i>"
            )
            
            result_msg = await update.message.reply_text(
                message,
                parse_mode=ParseMode.HTML
            )
            await loading_msg.delete()
            
            # Delete command message
            try:
                await update.message.delete()
            except Exception:
                pass

            # Schedule deletion
            async def delete_messages():
                await asyncio.sleep(30)
                try:
                    await result_msg.delete()
                except Exception:
                    pass
            
            asyncio.create_task(delete_messages())

        except Exception as e:
            logger.error(f"Error getting fact: {e}")
            error_msg = await update.message.reply_text(
                "<b>❌ Oops! Something went wrong!</b>\n"
                "<i>Couldn't fetch that fact fam!</i>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()

    async def dog(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Send a random dog image"""
        try:
            # Send loading message
            loading_msg = await update.message.reply_text(
                "🐕 <i>Finding a cute doggo...</i>",
                parse_mode=ParseMode.HTML
            )

            image_url = await self.api_service.get_dog_image()
            result_msg = await update.message.reply_photo(
                image_url,
                caption="<b>🐕 Woof! Here's your daily dose of cuteness!</b>\n\n<i>⏳ This message will self-destruct in 30s!</i>",
                parse_mode=ParseMode.HTML
            )
            await loading_msg.delete()
            
            # Delete command message
            try:
                await update.message.delete()
            except Exception:
                pass

            # Schedule deletion
            async def delete_messages():
                await asyncio.sleep(30)
                try:
                    await result_msg.delete()
                except Exception:
                    pass
            
            asyncio.create_task(delete_messages())

        except Exception as e:
            logger.error(f"Error getting dog image: {e}")
            error_msg = await update.message.reply_text(
                "<b>❌ Oops! Something went wrong!</b>\n"
                "<i>Couldn't fetch that doggo pic fam!</i>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()

    async def catfact(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Send a random cat fact"""
        try:
            # Send loading message
            loading_msg = await update.message.reply_text(
                "🐱 <i>Finding a purrfect fact...</i>",
                parse_mode=ParseMode.HTML
            )

            fact = await self.api_service.get_cat_fact()
            message = (
                f"<b>🐱 Cat Facts</b>\n\n"
                f"<i>{html.escape(fact)}</i>\n\n"
                f"<i>⏳ This message will self-destruct in 30s!</i>"
            )
            
            result_msg = await update.message.reply_text(
                message,
                parse_mode=ParseMode.HTML
            )
            await loading_msg.delete()
            
            # Delete command message
            try:
                await update.message.delete()
            except Exception:
                pass

            # Schedule deletion
            async def delete_messages():
                await asyncio.sleep(30)
                try:
                    await result_msg.delete()
                except Exception:
                    pass
            
            asyncio.create_task(delete_messages())

        except Exception as e:
            logger.error(f"Error getting cat fact: {e}")
            error_msg = await update.message.reply_text(
                "<b>❌ Oops! Something went wrong!</b>\n"
                "<i>Couldn't fetch that cat fact fam!</i>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()

    async def exchange(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle the exchange command"""
        if not context.args:
            error_msg = await update.message.reply_text(
                "<b>💱 Currency Exchange Guide</b>\n\n"
                "<b>Available Formats:</b>\n"
                "• <code>/exchange USD</code> - Show all rates\n"
                "• <code>/exchange USD EUR</code> - Convert 1 USD to EUR\n"
                "• <code>/exchange 100 USD EUR</code> - Convert 100 USD to EUR\n\n"
                "<i>Pro tip: Use standard 3-letter codes (USD, EUR, GBP)</i>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()
            return

        try:
            # Send loading message
            loading_msg = await update.message.reply_text(
                "💱 <i>Checking those exchange rates...</i>",
                parse_mode=ParseMode.HTML
            )

            # Parse arguments
            args = context.args
            if len(args) == 1:
                # Just show rates for base currency
                base_currency = args[0].upper()
                exchange_data = await self.api_service.get_exchange_rate(base_currency)
                
                # Format the response
                message = (
                    f"<b>💱 Exchange Rates for {exchange_data['base_currency']}</b>\n"
                    f"<code>Last Updated: {exchange_data['date']}</code>\n\n"
                    f"<b>Popular Currencies:</b>\n"
                )
                
                # Show popular currencies first
                popular = ['EUR', 'USD', 'GBP', 'JPY', 'AUD', 'CAD', 'CHF', 'INR']
                for currency in popular:
                    if currency != exchange_data['base_currency'] and currency in exchange_data['rates']:
                        rate = exchange_data['rates'][currency]
                        message += f"<code>{currency}: {rate:.4f}</code>\n"
                
                message += "\n<i>⏳ This message will self-destruct in 30s!</i>"
                
            elif len(args) >= 2:
                # Convert between currencies
                try:
                    # Check if first arg is amount
                    amount = float(args[0])
                    base_currency = args[1].upper()
                    target_currency = args[2].upper() if len(args) > 2 else None
                except ValueError:
                    # First arg is currency
                    amount = 1.0
                    base_currency = args[0].upper()
                    target_currency = args[1].upper()
                
                exchange_data = await self.api_service.get_exchange_rate(
                    base_currency, target_currency, amount
                )
                
                if "conversion" in exchange_data:
                    conv = exchange_data["conversion"]
                    message = (
                        f"<b>💱 Currency Conversion</b>\n"
                        f"<code>Last Updated: {exchange_data['date']}</code>\n\n"
                        f"<code>{conv['formula']}</code>\n\n"
                        f"<b>Exchange Rate:</b>\n"
                        f"<code>1 {conv['from']} = {conv['rate']:.4f} {conv['to']}</code>\n\n"
                        f"<i>⏳ This message will self-destruct in 30s!</i>"
                    )
            
            result_msg = await update.message.reply_text(
                message,
                parse_mode=ParseMode.HTML
            )
            await loading_msg.delete()
            
            # Delete command message
            try:
                await update.message.delete()
            except Exception:
                pass

            # Schedule deletion
            async def delete_messages():
                await asyncio.sleep(30)
                try:
                    await result_msg.delete()
                except Exception:
                    pass
            
            asyncio.create_task(delete_messages())
            
        except Exception as e:
            logger.error(f"Error with exchange rates: {e}")
            error_msg = await update.message.reply_text(
                "<b>❌ Oops! Something went wrong!</b>\n\n"
                "<i>Make sure to:</i>\n"
                "• Use valid currency codes (USD, EUR, etc.)\n"
                "• Check your formatting\n"
                "• Try again in a few minutes",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()

    async def numberfact(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Get a fact about a number"""
        if not context.args:
            error_msg = await update.message.reply_text(
                "<b>🔢 Number Facts Guide</b>\n\n"
                "<b>Usage:</b>\n"
                "• <code>/numberfact 42</code>\n"
                "• <code>/numberfact 7</code>\n"
                "• <code>/numberfact 365</code>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()
            return

        try:
            # Send loading message
            loading_msg = await update.message.reply_text(
                "🔢 <i>Finding a cool number fact...</i>",
                parse_mode=ParseMode.HTML
            )

            number = int(context.args[0])
            fact = await self.api_service.get_number_fact(number)
            message = (
                f"<b>🔢 Number Facts</b>\n\n"
                f"<i>{html.escape(fact)}</i>\n\n"
                f"<i>⏳ This message will self-destruct in 30s!</i>"
            )
            
            result_msg = await update.message.reply_text(
                message,
                parse_mode=ParseMode.HTML
            )
            await loading_msg.delete()
            
            # Delete command message
            try:
                await update.message.delete()
            except Exception:
                pass

            # Schedule deletion
            async def delete_messages():
                await asyncio.sleep(30)
                try:
                    await result_msg.delete()
                except Exception:
                    pass
            
            asyncio.create_task(delete_messages())

        except ValueError:
            error_msg = await update.message.reply_text(
                "<b>❌ Invalid Number!</b>\n"
                "<i>Please provide a valid number like:</i>\n"
                "• <code>/numberfact 42</code>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()
        except Exception as e:
            logger.error(f"Error getting number fact: {e}")
            error_msg = await update.message.reply_text(
                "<b>❌ Oops! Something went wrong!</b>\n"
                "<i>Couldn't fetch that number fact fam!</i>",
                parse_mode=ParseMode.HTML
            )
            await asyncio.sleep(5)
            await error_msg.delete()
