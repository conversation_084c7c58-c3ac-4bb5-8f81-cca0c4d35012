
from typing import Dict, Set, Optional
from datetime import datetime, timedelta
from telegram import Update, Chat, ChatPermissions
from telegram.ext import ContextTypes, CallbackContext, Job
from telegram.error import TelegramError

class RaidProtection:
    def __init__(self):
        # Track new members: chat_id -> set of user_ids
        self.new_members: Dict[int, Set[int]] = {}
        # Track join times: chat_id -> {user_id -> join_time}
        self.join_times: Dict[int, Dict[int, datetime]] = {}
        
        # Raid detection thresholds
        self.RAID_MEMBER_THRESHOLD = 5          # Number of new members
        self.RAID_TIME_THRESHOLD = 60           # Time window in seconds
        self.RAID_COOLDOWN = 300                # Cooldown period in seconds (5 minutes)
        self.AUTO_DISABLE_AFTER = 1800          # Auto-disable after 30 minutes

        # Track raid mode status: chat_id -> raid_start_time
        self.raid_mode_chats: Dict[int, datetime] = {}

        # Store alert message IDs to update countdowns
        self.raid_alert_messages: Dict[int, int] = {}
        # Store jobs for countdown updates
        self.countdown_jobs: Dict[int, Job] = {}

    def _format_time(self, seconds: int) -> str:
        """Format seconds into MM:SS."""
        minutes, secs = divmod(max(0, int(seconds)), 60)
        return f"{minutes:02d}:{secs:02d}"

    def _clean_old_data(self, chat_id: int) -> None:
        """Clean up old member data."""
        current_time = datetime.now()
        if chat_id in self.join_times:
            # Remove members who joined more than threshold seconds ago
            self.join_times[chat_id] = {
                user_id: join_time
                for user_id, join_time in self.join_times[chat_id].items()
                if (current_time - join_time).total_seconds() <= self.RAID_TIME_THRESHOLD
            }
            # Update new members set
            self.new_members[chat_id] = set(self.join_times[chat_id].keys())
    
    async def handle_new_member(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle new member joins and detect potential raids."""
        if not update.effective_chat or not update.effective_message:
            return

        chat_id = update.effective_chat.id
        current_time = datetime.now()

        if self.is_raid_mode_active(chat_id):
            for member in update.effective_message.new_chat_members:
                try:
                    await context.bot.restrict_chat_member(
                        chat_id,
                        member.id,
                        ChatPermissions(can_send_messages=False)
                    )
                except TelegramError:
                    pass
            return

        # Initialize data structures for this chat if needed
        if chat_id not in self.new_members:
            self.new_members[chat_id] = set()
        if chat_id not in self.join_times:
            self.join_times[chat_id] = {}

        # Clean old data for this chat
        self._clean_old_data(chat_id)

        # Add new non-bot members
        for member in update.effective_message.new_chat_members:
            if not member.is_bot:
                self.new_members[chat_id].add(member.id)
                self.join_times[chat_id][member.id] = current_time

        # Check for raid conditions: if the number of new non-bot joins meets threshold
        if len(self.new_members[chat_id]) >= self.RAID_MEMBER_THRESHOLD:
            # Verify that the joins happened within the allowed time window
            oldest_join = min(self.join_times[chat_id].values())
            time_window = (current_time - oldest_join).total_seconds()

            if time_window <= self.RAID_TIME_THRESHOLD:
                await self._enable_raid_mode(chat=update.effective_chat, context=context)
    
    async def _enable_raid_mode(self, chat: Chat, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Enable raid mode for a chat and schedule auto-disable."""
        chat_id = chat.id
        current_time = datetime.now()

        # If raid mode is already active, do nothing
        if chat_id in self.raid_mode_chats:
            return
            
        # Initialize data structures for this chat if needed
        if chat_id not in self.new_members:
            self.new_members[chat_id] = set()
        if chat_id not in self.join_times:
            self.join_times[chat_id] = {}

        try:
            # Restrict all member actions during raid mode
            permissions = ChatPermissions(
                can_send_messages=False,
                can_send_polls=False,
                can_send_other_messages=False,
                can_add_web_page_previews=False,
                can_change_info=False,
                can_invite_users=False,
                can_pin_messages=False
            )
            await context.bot.set_chat_permissions(chat_id=chat_id, permissions=permissions)
            self.raid_mode_chats[chat_id] = current_time

            # Schedule auto-disable of raid mode using the job queue (if available)
            if hasattr(context, "job_queue") and context.job_queue:
                context.job_queue.run_once(
                    callback=self._auto_disable_raid_mode,
                    when=self.AUTO_DISABLE_AFTER,
                    data=chat_id,
                    name=f"auto-disable-{chat_id}"
                )

            # Prepare message with join information if available
            join_info = ""
            if len(self.new_members[chat_id]) > 0:
                join_info = f"<i>Detected {len(self.new_members[chat_id])} new joins within {self.RAID_TIME_THRESHOLD} seconds!</i>\n\n"
            else:
                join_info = "<i>Manually activated by an administrator.</i>\n\n"

            # Send raid alert notification
            alert_msg = await context.bot.send_message(
                chat_id=chat_id,
                text=(
                    "<b>🚨 RAID ALERT!</b>\n\n"
                    f"{join_info}"
                    "• <b>Raid Mode:</b> <code>Enabled</code>\n"
                    f"• <b>Auto-disable in:</b> <code>{self._format_time(self.AUTO_DISABLE_AFTER)}</code>\n"
                    "• <b>Manual disable:</b> <code>/raidmode</code>"
                ),
                parse_mode='HTML'
            )

            self.raid_alert_messages[chat_id] = alert_msg.message_id

            # Schedule countdown updates if job queue is available
            end_time = current_time + timedelta(seconds=self.AUTO_DISABLE_AFTER)
            if hasattr(context, "job_queue") and context.job_queue:
                self.countdown_jobs[chat_id] = context.job_queue.run_repeating(
                    self._update_raid_message,
                    interval=30,
                    first=30,
                    data={"chat_id": chat_id, "message_id": alert_msg.message_id, "end_time": end_time},
                    name=f"raid-countdown-{chat_id}"
                )

        except TelegramError as e:
            await context.bot.send_message(
                chat_id=chat_id,
                text=(
                    f"<b>❌ Failed to enable Raid Mode!</b>\n"
                    f"<i>Error: {str(e)}</i>"
                ),
                parse_mode='HTML'
            )

    async def _update_raid_message(self, context: CallbackContext) -> None:
        """Update the raid alert message with a live countdown."""
        job = context.job
        if not job:
            return

        data = job.data or {}
        chat_id = data.get("chat_id")
        message_id = data.get("message_id")
        end_time = data.get("end_time")
        if not chat_id or not message_id or not end_time:
            return

        remaining = (end_time - datetime.now()).total_seconds()
        if remaining <= 0:
            job.schedule_removal()
            return

        try:
            await context.bot.edit_message_text(
                chat_id=chat_id,
                message_id=message_id,
                text=(
                    "<b>🚨 RAID ALERT!</b>\n\n"
                    "• <b>Raid Mode:</b> <code>Enabled</code>\n"
                    f"• <b>Auto-disable in:</b> <code>{self._format_time(remaining)}</code>\n"
                    "• <b>Manual disable:</b> <code>/raidmode</code>"
                ),
                parse_mode='HTML'
            )
        except TelegramError:
            pass

    async def _auto_disable_raid_mode(self, context: CallbackContext) -> None:
        """Automatically disable raid mode after the timeout."""
        job = context.job
        if not job:
            return

        chat_id = job.data
        if chat_id not in self.raid_mode_chats:
            return

        try:
            # Restore full permissions
            permissions = ChatPermissions(
                can_send_messages=True,
                can_send_polls=True,
                can_send_other_messages=True,
                can_add_web_page_previews=True,
                can_change_info=True,
                can_invite_users=True,
                can_pin_messages=True
            )
            await context.bot.set_chat_permissions(chat_id=chat_id, permissions=permissions)
            del self.raid_mode_chats[chat_id]

            # Cancel countdown job
            if chat_id in self.countdown_jobs:
                self.countdown_jobs[chat_id].schedule_removal()
                del self.countdown_jobs[chat_id]

            # Delete raid alert message
            if chat_id in self.raid_alert_messages:
                try:
                    await context.bot.delete_message(chat_id, self.raid_alert_messages[chat_id])
                except TelegramError:
                    pass
                del self.raid_alert_messages[chat_id]

            # Clear tracked member data for this chat
            if chat_id in self.new_members:
                self.new_members[chat_id].clear()
            if chat_id in self.join_times:
                self.join_times[chat_id].clear()

            await context.bot.send_message(
                chat_id=chat_id,
                text=(
                    "<b>✅ Raid Mode Auto-Disabled</b>\n"
                    "<i>Group settings have been restored to normal.</i>"
                ),
                parse_mode='HTML'
            )

        except TelegramError as e:
            await context.bot.send_message(
                chat_id=chat_id,
                text=(
                    f"<b>❌ Failed to disable Raid Mode!</b>\n"
                    f"<i>Error: {str(e)}</i>"
                ),
                parse_mode='HTML'
            )

    def is_raid_mode_active(self, chat_id: int) -> bool:
        """Check if raid mode is active for a chat."""
        return chat_id in self.raid_mode_chats
    
    async def manual_toggle(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Manually toggle raid mode."""
        if not update.effective_chat or not update.effective_message:
            return

        chat_id = update.effective_chat.id
        bot_id = context.bot.id

        # Check if the user is an administrator
        user_id = update.effective_user.id if update.effective_user else None
        if not user_id:
            return

        try:
            member = await context.bot.get_chat_member(chat_id=chat_id, user_id=user_id)
            if member.status not in ['creator', 'administrator']:
                await update.effective_message.reply_text(
                    text="❌ Only administrators can use this command!",
                    parse_mode='HTML'
                )
                return

            # Verify that the bot has proper admin rights to restrict members
            bot_member = await context.bot.get_chat_member(chat_id=chat_id, user_id=bot_id)
            if bot_member.status != 'administrator' or not bot_member.can_restrict_members:
                await update.effective_message.reply_text(
                    text="❌ I must be an administrator with permission to restrict members!",
                    parse_mode='HTML'
                )
                return
        except TelegramError as e:
            await update.effective_message.reply_text(
                text=f"❌ Error checking permissions: {str(e)}",
                parse_mode='HTML'
            )
            return

        if self.is_raid_mode_active(chat_id):
            # Disable raid mode manually
            try:
                permissions = ChatPermissions(
                    can_send_messages=True,
                    can_send_polls=True,
                    can_send_other_messages=True,
                    can_add_web_page_previews=True,
                    can_change_info=True,
                    can_invite_users=True,
                    can_pin_messages=True
                )
                await context.bot.set_chat_permissions(chat_id=chat_id, permissions=permissions)
                if chat_id in self.raid_mode_chats:
                    del self.raid_mode_chats[chat_id]

                # Cancel scheduled auto-disable job if it exists
                if hasattr(context, "job_queue") and context.job_queue:
                    current_jobs = context.job_queue.get_jobs_by_name(f"auto-disable-{chat_id}")
                    for job in current_jobs:
                        job.schedule_removal()

                if chat_id in self.countdown_jobs:
                    self.countdown_jobs[chat_id].schedule_removal()
                    del self.countdown_jobs[chat_id]

                if chat_id in self.raid_alert_messages:
                    try:
                        await context.bot.delete_message(chat_id, self.raid_alert_messages[chat_id])
                    except TelegramError:
                        pass
                    del self.raid_alert_messages[chat_id]

                await update.effective_message.reply_text(
                    text=(
                        "<b>✅ RAID MODE DISABLED!</b>\n"
                        "<i>Group settings restored to normal.</i>"
                    ),
                    parse_mode='HTML'
                )

            except TelegramError as e:
                await update.effective_message.reply_text(
                    text=f"<b>❌ Error!</b>\n<i>{str(e)}</i>",
                    parse_mode='HTML'
                )
        else:
            # Enable raid mode manually
            await self._enable_raid_mode(chat=update.effective_chat, context=context)

# End of RaidProtection class

if __name__ == "__main__":
    # This block is for standalone testing purposes.
    # In actual deployment, the bot's dispatcher and job queue will handle these functions.
    import asyncio
    from telegram.ext import ApplicationBuilder

    async def main():
        from src.config import config
        application = ApplicationBuilder().token(config.TELEGRAM_BOT_TOKEN).build()
        raid_protection = RaidProtection()

        print("RaidProtection module loaded successfully.")

    asyncio.run(main())
