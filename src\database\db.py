"""
Database Module
-------------
Handles database operations with SQLite.
"""

import os
import sqlite3
import aiosqlite
from typing import Optional, Dict, List, Any
from datetime import datetime, timedelta
import logging
import json
import traceback

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class Database:
    def __init__(self, db_path: str = "data/bot_database.db"):
        """Initialize SQLite database connection"""
        self.db_path = db_path
        # Create data directory if it doesn't exist
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        # Keep memory storage for compatibility
        self.memory_storage = {
            'warnings': {},
            'settings': {},
            'notes': {},
            'rules': {},
            'banned': set(),
            'chats': {},
            'groups': {},
            'muted': {}
        }
        self.default_settings = {
            "log_channel": -1002345906730,
            "disabled_log_events": [],
            "welcome_message": "Welcome {user} to {chat}! \n\nPlease read our rules and enjoy your stay!",
            "rules_message": " Group Rules:\n\n1. Be respectful to others\n2. No spam or self-promotion\n3. Stay on topic\n4. Follow Telegram's Terms of Service",
            "protection": {
                "spam": True,
                "flood": True,
                "url": True,
                "forward": False,
                "sticker": False,
                "nsfw": False
            }
        }
        self._create_tables()

    def _create_tables(self):
        """Create necessary database tables"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Group settings table
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS group_settings (
                    chat_id INTEGER PRIMARY KEY,
                    welcome_enabled BOOLEAN DEFAULT 1,
                    welcome_message TEXT DEFAULT NULL,
                    goodbye_message TEXT DEFAULT NULL,
                    rules TEXT DEFAULT NULL,
                    nsfw_filter_enabled BOOLEAN DEFAULT 0,
                    antiflood_enabled BOOLEAN DEFAULT 1,
                    antiflood_limit INTEGER DEFAULT 5,
                    antiflood_time INTEGER DEFAULT 30,
                    captcha_enabled BOOLEAN DEFAULT 0,
                    logging_enabled BOOLEAN DEFAULT 0,
                    log_channel_id INTEGER DEFAULT NULL,
                    protection_enabled BOOLEAN DEFAULT 1,
                    restrict_new_users BOOLEAN DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """)

                # Users table
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    user_id INTEGER PRIMARY KEY,
                    username TEXT,
                    first_name TEXT,
                    last_name TEXT,
                    language_code TEXT,
                    is_bot BOOLEAN DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    data TEXT DEFAULT '{}',
                    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """)

                # Warnings table
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS warnings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    chat_id INTEGER NOT NULL,
                    user_id INTEGER NOT NULL,
                    admin_id INTEGER NOT NULL,
                    reason TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """)

                # Notes table
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS notes (
                    chat_id INTEGER NOT NULL,
                    note_name TEXT NOT NULL,
                    content TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (chat_id, note_name)
                )
                """)

                # Filters table
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS filters (
                    chat_id INTEGER NOT NULL,
                    filter_name TEXT NOT NULL,
                    filter_data TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (chat_id, filter_name)
                )
                """)

                # Admin actions table
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS admin_actions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    chat_id INTEGER NOT NULL,
                    admin_id INTEGER NOT NULL,
                    action TEXT NOT NULL,
                    target_user_id INTEGER,
                    reason TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """)

                # Appeals table
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS appeals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    chat_id INTEGER NOT NULL,
                    message TEXT NOT NULL,
                    status TEXT NOT NULL DEFAULT 'pending',
                    admin_id INTEGER,
                    decision TEXT,
                    decision_reason TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    decision_timestamp TIMESTAMP
                )
                """)

                # User activity table
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_activity (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    chat_id INTEGER NOT NULL,
                    activity_type TEXT NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """)

                # Command stats table
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS command_stats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    command TEXT NOT NULL,
                    user_id INTEGER NOT NULL,
                    chat_id INTEGER NOT NULL,
                    chat_type TEXT NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """)

                # Chats table
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS chats (
                    chat_id INTEGER PRIMARY KEY,
                    title TEXT,
                    chat_type TEXT,
                    joined_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_admin BOOLEAN DEFAULT 0
                )
                """)

                # Reports table
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS reports (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    chat_id INTEGER NOT NULL,
                    reporter_id INTEGER NOT NULL,
                    reported_user_id INTEGER,
                    message_id INTEGER,
                    reason TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """)

                # Bans table
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS bans (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    chat_id INTEGER NOT NULL,
                    user_id INTEGER NOT NULL,
                    admin_id INTEGER NOT NULL,
                    reason TEXT,
                    ban_type TEXT NOT NULL DEFAULT 'ban',
                    duration INTEGER,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    active BOOLEAN DEFAULT 1
                )
                """)

                # Auto responses table
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS auto_responses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    chat_id INTEGER NOT NULL,
                    trigger TEXT NOT NULL,
                    response TEXT NOT NULL,
                    created_by INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(chat_id, trigger)
                )
                """)

                # Birthday wishes table
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS birthday_wishes (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    celebrant_id INTEGER,
                    celebrant_username TEXT,
                    chat_id INTEGER NOT NULL,
                    last_wish_timestamp TIMESTAMP NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """)

                conn.commit()
                logger.info("SQLite database tables created successfully")
        except Exception as e:
            logger.error(f"Error creating database tables: {e}")
            logger.error(traceback.format_exc())

    def setup_database(self):
        """Initialize database tables"""
        self._create_tables()

    def connect(self):
        """Establish database connection"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.execute("PRAGMA foreign_keys = ON")
            conn.commit()
            return conn
        except Exception as e:
            logger.error(f"Error connecting to database: {e}")
            raise

    def is_connected(self) -> bool:
        """Check if database is connected"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("SELECT 1")
                return True
        except:
            return False

    # Warning management methods
    async def add_warning(self, chat_id: int, user_id: int, admin_id: int, reason: str = None) -> int:
        """Add warning to user and return total warnings"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                INSERT INTO warnings (chat_id, user_id, admin_id, reason, timestamp)
                VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
                """, (chat_id, user_id, admin_id, reason))
                conn.commit()

                # Get updated warning count
                cursor.execute("""
                SELECT COUNT(*) FROM warnings
                WHERE chat_id = ? AND user_id = ?
                """, (chat_id, user_id))
                return cursor.fetchone()[0]
        except Exception as e:
            logger.error(f"Error adding warning: {e}")
            return 0

    async def add_warning_sqlite(self, chat_id: int, user_id: int, admin_id: int, reason: str = None) -> int:
        """Alias for add_warning - for backward compatibility"""
        return await self.add_warning(chat_id, user_id, admin_id, reason)

    async def log_admin_action(self, chat_id: int, admin_id: int, action: str, target_user_id: int, reason: str = None):
        """Log an admin action"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                INSERT INTO admin_actions (chat_id, admin_id, action, target_user_id, reason, timestamp)
                VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                """, (chat_id, admin_id, action, target_user_id, reason))
                conn.commit()
        except Exception as e:
            logger.error(f"Error logging admin action: {e}")

    async def get_user_warn_count(self, chat_id: int, user_id: int) -> int:
        """Get the number of warnings for a user in a chat"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                SELECT COUNT(*) FROM warnings
                WHERE chat_id = ? AND user_id = ?
                """, (chat_id, user_id))
                return cursor.fetchone()[0]
        except Exception as e:
            logger.error(f"Error getting warning count: {e}")
            return 0

    async def get_warnings(self, chat_id: int, user_id: int) -> int:
        """Alias for get_user_warn_count - for backward compatibility"""
        return await self.get_user_warn_count(chat_id, user_id)

    async def remove_warning(self, chat_id: int, user_id: int) -> bool:
        """Remove the latest warning from a user"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Get the latest warning ID
                cursor.execute("""
                SELECT id FROM warnings
                WHERE chat_id = ? AND user_id = ?
                ORDER BY timestamp DESC LIMIT 1
                """, (chat_id, user_id))

                result = cursor.fetchone()
                if not result:
                    return False

                # Delete the warning
                cursor.execute("DELETE FROM warnings WHERE id = ?", (result[0],))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error removing warning: {e}")
            return False

    async def get_user_warnings(self, chat_id: int, user_id: int) -> List[Dict]:
        """Get all warnings for a user in a chat"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                SELECT w.id, w.reason, w.timestamp, w.admin_id, u.first_name as admin_name
                FROM warnings w
                LEFT JOIN users u ON w.admin_id = u.user_id
                WHERE w.chat_id = ? AND w.user_id = ?
                ORDER BY w.timestamp DESC
                """, (chat_id, user_id))

                warnings = []
                for row in cursor.fetchall():
                    warnings.append({
                        'id': row[0],
                        'reason': row[1],
                        'timestamp': row[2],
                        'admin_id': row[3],
                        'admin_name': row[4] if row[4] else "Unknown"
                    })
                return warnings
        except Exception as e:
            logger.error(f"Error getting user warnings: {e}")
            return []

    async def clear_warnings(self, chat_id: int, user_id: int) -> bool:
        """Clear all warnings for a user in a chat"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM warnings WHERE chat_id = ? AND user_id = ?", (chat_id, user_id))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error clearing warnings: {e}")
            return False

    async def add_appeal(self, user_id: int, chat_id: int, message: str) -> int:
        """Add a new appeal to the database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                INSERT INTO appeals (user_id, chat_id, message, status, timestamp)
                VALUES (?, ?, ?, 'pending', CURRENT_TIMESTAMP)
                """, (user_id, chat_id, message))
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            logger.error(f"Error adding appeal: {e}")
            return None

    async def update_appeal_status(self, appeal_id: int, status: str, admin_id: int, decision: str = None, reason: str = None) -> bool:
        """Update the status of an appeal"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                UPDATE appeals
                SET status = ?, admin_id = ?, decision = ?, decision_reason = ?, decision_timestamp = CURRENT_TIMESTAMP
                WHERE id = ?
                """, (status, admin_id, decision, reason, appeal_id))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error updating appeal: {e}")
            return False

    async def get_appeal(self, appeal_id: int) -> Dict:
        """Get appeal details by ID"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM appeals WHERE id = ?", (appeal_id,))
                result = cursor.fetchone()
                if result:
                    return {
                        'id': result[0],
                        'user_id': result[1],
                        'chat_id': result[2],
                        'message': result[3],
                        'status': result[4],
                        'admin_id': result[5],
                        'decision': result[6],
                        'decision_reason': result[7],
                        'timestamp': result[8],
                        'decision_timestamp': result[9]
                    }
                return None
        except Exception as e:
            logger.error(f"Error getting appeal: {e}")
            return None

    async def get_user_appeals(self, user_id: int, chat_id: int = None) -> List[Dict]:
        """Get all appeals by a user, optionally filtered by chat"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                if chat_id:
                    cursor.execute("""
                    SELECT * FROM appeals
                    WHERE user_id = ? AND chat_id = ?
                    ORDER BY timestamp DESC
                    """, (user_id, chat_id))
                else:
                    cursor.execute("""
                    SELECT * FROM appeals
                    WHERE user_id = ?
                    ORDER BY timestamp DESC
                    """, (user_id,))

                results = cursor.fetchall()
                return [{
                    'id': row[0],
                    'user_id': row[1],
                    'chat_id': row[2],
                    'message': row[3],
                    'status': row[4],
                    'admin_id': row[5],
                    'decision': row[6],
                    'decision_reason': row[7],
                    'timestamp': row[8],
                    'decision_timestamp': row[9]
                } for row in results]
        except Exception as e:
            logger.error(f"Error getting user appeals: {e}")
            return []

    async def get_group_setting(self, chat_id: int, setting: str, default_value: Any = None) -> Any:
        """Get a group setting"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Ensure chat exists
                cursor.execute("INSERT OR IGNORE INTO group_settings (chat_id) VALUES (?)", (chat_id,))
                conn.commit()

                # Check if column exists
                cursor.execute("PRAGMA table_info(group_settings)")
                columns = [row[1] for row in cursor.fetchall()]

                if setting not in columns:
                    # Add column with appropriate type
                    col_type = "INTEGER" if isinstance(default_value, bool) else "TEXT"
                    cursor.execute(f"ALTER TABLE group_settings ADD COLUMN {setting} {col_type}")
                    conn.commit()

                    # Set default value
                    db_value = None
                    if isinstance(default_value, bool):
                        db_value = 1 if default_value else 0
                    elif isinstance(default_value, (list, dict)):
                        db_value = json.dumps(default_value)
                    else:
                        db_value = default_value

                    cursor.execute(f"UPDATE group_settings SET {setting} = ? WHERE chat_id = ?", (db_value, chat_id))
                    conn.commit()
                    return default_value

                # Get setting value
                cursor.execute(f"SELECT {setting} FROM group_settings WHERE chat_id = ?", (chat_id,))
                result = cursor.fetchone()

                if result is None or result[0] is None:
                    # Set default value if not found
                    db_value = None
                    if isinstance(default_value, bool):
                        db_value = 1 if default_value else 0
                    elif isinstance(default_value, (list, dict)):
                        db_value = json.dumps(default_value)
                    else:
                        db_value = default_value

                    cursor.execute(f"UPDATE group_settings SET {setting} = ? WHERE chat_id = ?", (db_value, chat_id))
                    conn.commit()
                    return default_value

                value = result[0]
                # Convert stored value back to appropriate type
                if isinstance(default_value, bool):
                    return value == 1
                elif isinstance(default_value, (list, dict)):
                    try:
                        return json.loads(value) if value else default_value
                    except (json.JSONDecodeError, TypeError):
                        return default_value
                return value

        except Exception as e:
            logger.error(f"Error getting group setting: {str(e)}")
            logger.error(traceback.format_exc())
            return default_value

    async def set_group_setting(self, chat_id: int, setting: str, value: Any) -> bool:
        """Set a group setting"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Ensure chat exists
                cursor.execute("INSERT OR IGNORE INTO group_settings (chat_id) VALUES (?)", (chat_id,))

                # Check if column exists
                cursor.execute("PRAGMA table_info(group_settings)")
                columns = [row[1] for row in cursor.fetchall()]

                if setting not in columns:
                    # Add column with appropriate type
                    col_type = "INTEGER" if isinstance(value, bool) else "TEXT"
                    cursor.execute(f"ALTER TABLE group_settings ADD COLUMN {setting} {col_type}")

                # Convert value to appropriate storage format
                db_value = None
                if isinstance(value, bool):
                    db_value = 1 if value else 0
                elif isinstance(value, (list, dict)):
                    db_value = json.dumps(value)
                else:
                    db_value = value

                # Update setting
                cursor.execute(f"UPDATE group_settings SET {setting} = ? WHERE chat_id = ?", (db_value, chat_id))
                conn.commit()
                return True

        except Exception as e:
            logger.error(f"Error setting group setting: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    async def get_welcome_message(self, chat_id: int) -> str:
        """Get welcome message for a chat"""
        try:
            return await self.get_group_setting(chat_id, "welcome_message", None)
        except Exception as e:
            logger.error(f"Error getting welcome message: {e}")
            return None

    async def set_welcome_message(self, chat_id: int, message: str) -> bool:
        """Set welcome message for a chat"""
        try:
            return await self.set_group_setting(chat_id, "welcome_message", message)
        except Exception as e:
            logger.error(f"Error setting welcome message: {e}")
            return False

    async def get_welcome_setting(self, chat_id: int) -> bool:
        """Get welcome enabled setting for a chat"""
        try:
            return await self.get_group_setting(chat_id, "welcome_enabled", True)
        except Exception as e:
            logger.error(f"Error getting welcome setting: {e}")
            return True

    async def set_welcome_setting(self, chat_id: int, enabled: bool) -> bool:
        """Set welcome enabled setting for a chat"""
        try:
            return await self.set_group_setting(chat_id, "welcome_enabled", enabled)
        except Exception as e:
            logger.error(f"Error setting welcome enabled: {e}")
            return False

    async def get_goodbye_message(self, chat_id: int) -> str:
        """Get goodbye message for a chat"""
        try:
            return await self.get_group_setting(chat_id, "goodbye_message", None)
        except Exception as e:
            logger.error(f"Error getting goodbye message: {e}")
            return None

    async def set_goodbye_message(self, chat_id: int, message: str) -> bool:
        """Set goodbye message for a chat"""
        try:
            return await self.set_group_setting(chat_id, "goodbye_message", message)
        except Exception as e:
            logger.error(f"Error setting goodbye message: {e}")
            return False

    async def get_group_rules(self, chat_id: int) -> str:
        """Get the rules for a group"""
        return await self.get_group_setting(chat_id, "rules", "No rules set for this group.")

    async def set_group_rules(self, chat_id: int, rules: str) -> bool:
        """Set the rules for a group"""
        return await self.set_group_setting(chat_id, "rules", rules)

    async def save_note(self, chat_id: int, note_name: str, content: str):
        """Save a note"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                INSERT OR REPLACE INTO notes (chat_id, note_name, content)
                VALUES (?, ?, ?)
                """, (chat_id, note_name, content))
                conn.commit()
        except Exception as e:
            logger.error(f"Error saving note: {e}")

    async def get_note(self, chat_id: int, note_name: str) -> Optional[str]:
        """Get a note"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT content FROM notes WHERE chat_id = ? AND note_name = ?", (chat_id, note_name))
                result = cursor.fetchone()
                return result[0] if result else None
        except Exception as e:
            logger.error(f"Error getting note: {e}")
            return None

    async def add_filter(self, chat_id: int, filter_name: str, filter_data: Dict[str, Any]):
        """Add a filter to a chat"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                INSERT OR REPLACE INTO filters (chat_id, filter_name, filter_data)
                VALUES (?, ?, ?)
                """, (chat_id, filter_name, json.dumps(filter_data)))
                conn.commit()
        except Exception as e:
            logger.error(f"Error adding filter: {e}")

    async def remove_filter(self, chat_id: int, filter_name: str):
        """Remove a filter from a chat"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM filters WHERE chat_id = ? AND filter_name = ?", (chat_id, filter_name))
                conn.commit()
        except Exception as e:
            logger.error(f"Error removing filter: {e}")

    async def get_filters(self, chat_id: int) -> List[Dict[str, Any]]:
        """Get all filters for a chat"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT filter_name, filter_data FROM filters WHERE chat_id = ?", (chat_id,))
                filters = []
                for row in cursor.fetchall():
                    filters.append({
                        'name': row[0],
                        'data': json.loads(row[1])
                    })
                return filters
        except Exception as e:
            logger.error(f"Error getting filters: {e}")
            return []

    async def track_user_activity(self, chat_id: int, user_id: int, activity_type: str) -> None:
        """Track user activity in a chat"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                INSERT INTO user_activity (user_id, chat_id, activity_type, timestamp)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
                """, (user_id, chat_id, activity_type))
                conn.commit()
        except Exception as e:
            logger.error(f"Error tracking user activity: {e}")

    async def log_user_activity(self, user_id, chat_id, chat_type, activity_type="message"):
        """Alias for track_user_activity - for backward compatibility"""
        return await self.track_user_activity(chat_id, user_id, activity_type)

    async def log_command(self, command, user_id, chat_id, chat_type):
        """Log command usage"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                INSERT INTO command_stats (command, user_id, chat_id, chat_type, timestamp)
                VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
                """, (command, user_id, chat_id, chat_type))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error logging command: {e}")
            return False

    async def get_all_chats(self) -> List[int]:
        """Get all chat IDs where the bot is present."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT chat_id FROM group_settings")
                return [row[0] for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"Error getting all chats: {str(e)}")
            return []

    async def get_all_groups(self) -> List[Dict[str, Any]]:
        """Get all group chats for morning greetings."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT chat_id FROM group_settings WHERE chat_id < 0")
                return [{"chat_id": row[0]} for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"Error getting all groups: {e}")
            return []

    async def add_chat(self, chat_id: int, title: str, chat_type: str, is_admin: bool = False) -> bool:
        """Add a chat to the database and ensure group settings exist."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Ensure group settings exist
                cursor.execute("INSERT OR IGNORE INTO group_settings (chat_id) VALUES (?)", (chat_id,))

                # Add to chats collection
                cursor.execute("""
                INSERT OR REPLACE INTO chats (chat_id, title, chat_type, joined_date, is_admin)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP, ?)
                """, (chat_id, title, chat_type, is_admin))

                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error adding chat to database: {e}")
            return False

    async def remove_chat(self, chat_id: int) -> bool:
        """Remove a chat from the database and related group settings."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM chats WHERE chat_id = ?", (chat_id,))
                cursor.execute("DELETE FROM group_settings WHERE chat_id = ?", (chat_id,))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error removing chat from database: {e}")
            return False

    async def add_report(self, report_data: Dict[str, Any]) -> None:
        """Add a new report to the database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                INSERT INTO reports (chat_id, reporter_id, reported_user_id, message_id, reason, timestamp)
                VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                """, (
                    report_data['chat_id'],
                    report_data['reporter_id'],
                    report_data.get('reported_user_id'),
                    report_data.get('message_id'),
                    report_data.get('reason')
                ))
                conn.commit()
        except Exception as e:
            logger.error(f"Error adding report: {e}")

    async def get_report_chat_id(self, message_id: int) -> Optional[int]:
        """Get chat ID for a report by message ID"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT chat_id FROM reports WHERE message_id = ?", (message_id,))
                result = cursor.fetchone()
                return result[0] if result else None
        except Exception as e:
            logger.error(f"Error getting report chat ID: {e}")
            return None

    async def add_ban(self, chat_id: int, user_id: int, admin_id: int, reason: str = None,
                     duration: int = None, ban_type: str = "ban") -> bool:
        """Add a ban record to the database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                INSERT INTO bans (chat_id, user_id, admin_id, reason, ban_type, duration, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                """, (chat_id, user_id, admin_id, reason, ban_type, duration))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error adding ban record: {e}")
            return False

    async def remove_ban(self, chat_id: int, user_id: int) -> bool:
        """Remove a ban record from the database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                UPDATE bans SET active = 0
                WHERE chat_id = ? AND user_id = ? AND active = 1
                """, (chat_id, user_id))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error removing ban record: {e}")
            return False

    async def get_bot_stats(self):
        """Get comprehensive bot statistics"""
        stats = {}
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Total users
                cursor.execute("SELECT COUNT(DISTINCT user_id) FROM user_activity")
                result = cursor.fetchone()
                stats['total_users'] = result[0] if result else 0
                
                # Total groups
                cursor.execute("SELECT COUNT(*) FROM group_settings")
                result = cursor.fetchone()
                stats['total_groups'] = result[0] if result else 0
                
                # Active users in last 7 days
                cursor.execute("""
                SELECT COUNT(DISTINCT user_id) FROM user_activity
                WHERE timestamp > datetime('now', '-7 days')
                """)
                result = cursor.fetchone()
                stats['active_users_7d'] = result[0] if result else 0
                
                # Messages processed
                cursor.execute("SELECT COUNT(*) FROM user_activity WHERE activity_type = 'message'")
                result = cursor.fetchone()
                stats['total_messages'] = result[0] if result else 0
                
                # Commands processed
                cursor.execute("SELECT COUNT(*) FROM command_stats")
                result = cursor.fetchone()
                stats['total_commands'] = result[0] if result else 0
                
                # Top 5 commands
                cursor.execute("""
                SELECT command, COUNT(*) as count
                FROM command_stats
                GROUP BY command
                ORDER BY count DESC
                LIMIT 5
                """)
                top_commands = cursor.fetchall()
                stats['top_commands'] = {row[0]: row[1] for row in top_commands}
                
                return stats
        except Exception as e:
            logger.error(f"Error getting bot stats: {e}")
            return stats

    async def clear_all_chats(self) -> bool:
        """Clear all chats from the database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM chats")
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error clearing chats: {e}")
            return False

    async def reset_warnings(self, chat_id: int, user_id: int) -> bool:
        """Reset all warnings for a user in a specific chat."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM warnings WHERE chat_id = ? AND user_id = ?", (chat_id, user_id))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error resetting warnings: {e}")
            return False

    # Auto-response methods
    async def add_auto_response(self, chat_id: int, trigger: str, response: str, created_by: int):
        """Add an auto-response trigger"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                INSERT OR REPLACE INTO auto_responses (chat_id, trigger, response, created_by)
                VALUES (?, ?, ?, ?)
                """, (chat_id, trigger.lower(), response, created_by))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error adding auto response: {e}")
            return False

    async def remove_auto_response(self, chat_id: int, trigger: str):
        """Remove an auto-response trigger"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM auto_responses WHERE chat_id = ? AND trigger = ?", (chat_id, trigger.lower()))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            logger.error(f"Error removing auto response: {e}")
            return False

    async def get_auto_response(self, chat_id: int, trigger: str):
        """Get auto-response for a trigger"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT response FROM auto_responses WHERE chat_id = ? AND trigger = ?", (chat_id, trigger.lower()))
                result = cursor.fetchone()
                return result[0] if result else None
        except Exception as e:
            logger.error(f"Error getting auto response: {e}")
            return None

    async def get_all_auto_responses(self, chat_id: int):
        """Get all auto-responses for a chat"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT trigger, response, created_by, created_at FROM auto_responses WHERE chat_id = ? ORDER BY trigger", (chat_id,))
                responses = []
                for row in cursor.fetchall():
                    responses.append({
                        'trigger': row[0],
                        'response': row[1],
                        'created_by': row[2],
                        'created_at': row[3]
                    })
                return responses
        except Exception as e:
            logger.error(f"Error getting all auto responses: {e}")
            return []

    async def search_auto_responses(self, chat_id: int, text: str):
        """Search for auto-response triggers in text"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT trigger, response FROM auto_responses WHERE chat_id = ?", (chat_id,))
                text_lower = text.lower()
                for row in cursor.fetchall():
                    trigger = row[0]
                    if trigger in text_lower:
                        return row[1]
                return None
        except Exception as e:
            logger.error(f"Error searching auto responses: {e}")
            return None

    # Birthday wishes methods
    async def has_been_wished_today(self, user_id: int, username: str, chat_id: int):
        """Check if user has already been wished today"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                # Check if wished today
                cursor.execute("""
                SELECT id FROM birthday_wishes 
                WHERE (celebrant_id = ? OR celebrant_username = ?) 
                AND chat_id = ? 
                AND date(last_wish_timestamp) = date('now')
                """, (user_id, username, chat_id))
                return cursor.fetchone() is not None
        except Exception as e:
            logger.error(f"Error checking birthday wish status: {e}")
            return False

    async def record_birthday_wish(self, user_id: int, username: str, chat_id: int):
        """Record that we've wished someone a happy birthday"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                INSERT OR REPLACE INTO birthday_wishes (celebrant_id, celebrant_username, chat_id, last_wish_timestamp)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
                """, (user_id, username, chat_id))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error recording birthday wish: {e}")
            return False

    async def get_chat_settings(self, chat_id: int) -> Dict[str, Any]:
        """Get settings for a chat"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM group_settings WHERE chat_id = ?", (chat_id,))
                result = cursor.fetchone()
                if result:
                    # Convert row to dict (simple approach)
                    columns = [description[0] for description in cursor.description]
                    return dict(zip(columns, result))
                return {}
        except Exception as e:
            logger.error(f"Error getting chat settings: {e}")
            return {}

    async def set_chat_settings(self, chat_id: int, settings: Dict[str, Any]):
        """Set settings for a chat"""
        try:
            # Use set_group_setting for each setting
            for key, value in settings.items():
                await self.set_group_setting(chat_id, key, value)
        except Exception as e:
            logger.error(f"Error setting chat settings: {e}")

    async def get_user_data(self, user_id: int) -> Dict[str, Any]:
        """Get data for a user"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT data FROM users WHERE user_id = ?", (user_id,))
                result = cursor.fetchone()
                return json.loads(result[0]) if result and result[0] else {}
        except Exception as e:
            logger.error(f"Error getting user data: {e}")
            return {}

    async def set_user_data(self, user_id: int, data: Dict[str, Any]):
        """Set data for a user"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                INSERT OR REPLACE INTO users (user_id, data, last_active)
                VALUES (?, ?, CURRENT_TIMESTAMP)
                """, (user_id, json.dumps(data)))
                conn.commit()
        except Exception as e:
            logger.error(f"Error setting user data: {e}")

    # Additional compatibility methods
    async def update_chat_settings(self, chat_id: int, settings: Dict):
        """Update chat settings - alias for set_chat_settings"""
        return await self.set_chat_settings(chat_id, settings)

    async def set_rules(self, chat_id: int, rules: str):
        """Set rules - alias for set_group_rules"""
        return await self.set_group_rules(chat_id, rules)

    async def get_rules(self, chat_id: int) -> Optional[str]:
        """Get rules - alias for get_group_rules"""
        return await self.get_group_rules(chat_id)

    # Stats compatibility methods
    async def get_user_stats(self, chat_id: int, user_id: int) -> Dict[str, Any]:
        """Get user statistics for a chat"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get user's first activity and message count
                cursor.execute("""
                SELECT MIN(timestamp) as first_seen, COUNT(*) as message_count
                FROM user_activity 
                WHERE chat_id = ? AND user_id = ?
                """, (chat_id, user_id))
                result = cursor.fetchone()
                
                if result and result[0]:
                    return {
                        'first_seen': result[0],
                        'message_count': result[1],
                        'last_active': result[0]  # Simplified
                    }
                return {}
        except Exception as e:
            logger.error(f"Error getting user stats: {e}")
            return {}

    async def update_user_weekly_activity(self) -> None:
        """Update weekly activity counts - compatibility method"""
        pass

    async def update_days_active(self) -> None:
        """Update days active count - compatibility method"""
        pass

    async def get_all_users(self):
        """Get all users from the database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM users")
                users = []
                for row in cursor.fetchall():
                    columns = [description[0] for description in cursor.description]
                    users.append(dict(zip(columns, row)))
                return users
        except Exception as e:
            logger.error(f"Error getting all users: {e}")
            return []

    async def get_all_groups_full(self):
        """Get all groups and settings from the database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM group_settings")
                groups = []
                for row in cursor.fetchall():
                    columns = [description[0] for description in cursor.description]
                    groups.append(dict(zip(columns, row)))
                return groups
        except Exception as e:
            logger.error(f"Error getting all groups: {e}")
            return []

    async def get_active_users(self, days=7):
        """Get users who were active in the last X days"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                SELECT DISTINCT user_id FROM user_activity
                WHERE timestamp > datetime('now', '-{} days')
                """.format(days))
                return [{"user_id": row[0]} for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"Error getting active users: {e}")
            return []

    async def get_user_count_by_chat_type(self):
        """Get count of users by chat type"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                SELECT 
                    CASE WHEN chat_id < 0 THEN 'group' ELSE 'private' END as chat_type,
                    COUNT(DISTINCT user_id) as count
                FROM user_activity
                GROUP BY CASE WHEN chat_id < 0 THEN 'group' ELSE 'private' END
                """)
                return {row[0]: row[1] for row in cursor.fetchall()}
        except Exception as e:
            logger.error(f"Error getting user count by chat type: {e}")
            return {}

    async def get_command_stats(self, limit=None):
        """Get command usage statistics"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                query = """
                SELECT command, COUNT(*) as count
                FROM command_stats
                GROUP BY command
                ORDER BY count DESC
                """
                if limit:
                    query += f" LIMIT {limit}"
                
                cursor.execute(query)
                return {row[0]: row[1] for row in cursor.fetchall()}
        except Exception as e:
            logger.error(f"Error getting command stats: {e}")
            return {}