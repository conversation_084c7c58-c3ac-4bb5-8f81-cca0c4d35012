"""Music bot implementation for Telegram."""

import os
import logging
import async<PERSON>
from typing import Dict
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes, CommandHandler
from webscout import Search, Video, Handler
import json
from src.database.models import init_db, User, Server, MusicPlayback
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
from sqlalchemy import select

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Create downloads directory if it doesn't exist
DOWNLOADS_DIR = os.path.join(os.path.dirname(__file__), 'downloads')
os.makedirs(DOWNLOADS_DIR, exist_ok=True)

class MusicBot:
    def __init__(self, bot):
        """Initialize the music bot."""
        self.bot = bot
        self.active_chats = {}
        self.db_session = init_db()

    async def play(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle the /play command."""
        if not update.message.chat.type in ['group', 'supergroup']:
            await update.message.reply_text("This command can only be used in groups!")
            return

        if len(context.args) < 1:
            await update.message.reply_text("Please provide a song name or URL!")
            return

        chat_id = update.message.chat_id
        user_id = update.effective_user.id
        query = ' '.join(context.args)
        status_msg = await update.message.reply_text("🔍 Searching for your song...")

        try:
            # Search for the song using webscout
            video_ids = Search.videos(query)
            if not video_ids:
                await status_msg.edit_text("❌ No results found! Try a different search term.")
                return

            # Get the first video ID and create YouTube URL
            video_id = video_ids[0]
            url = f"https://www.youtube.com/watch?v={video_id}"

            # Get video metadata using webscout
            video = Video(url)
            metadata = video.metadata
            
            title = metadata.get('title', 'Unknown Title')
            duration = metadata.get('duration', 0)
            thumbnail = metadata.get('thumbnail', '')
            channel = metadata.get('channel', 'Unknown Artist')

            # Create inline keyboard with video link
            keyboard = [
                [InlineKeyboardButton("🎥 Watch on YouTube", url=url)],
                [InlineKeyboardButton("❌ Stop", callback_data=f"stop_{chat_id}")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            # Send audio file as voice message
            await status_msg.edit_text(
                f"🎵 Found: {title}\n"
                f"👤 Channel: {channel}\n"
                f"⏱️ Duration: {duration} seconds\n"
                f"🔗 <a href='{url}'>YouTube Link</a>",
                parse_mode='HTML',
                reply_markup=reply_markup
            )

            # Record music playback in database
            try:
                # Get or create server
                server_stmt = select(Server).where(Server.telegram_id == chat_id)
                server = self.db_session.execute(server_stmt).scalar_one_or_none()
                
                if not server:
                    server = Server(
                        telegram_id=chat_id,
                        title=update.effective_chat.title,
                        description=update.effective_chat.description if hasattr(update.effective_chat, 'description') else None
                    )
                    self.db_session.add(server)
                    self.db_session.flush()
                
                # Get or create user
                user_stmt = select(User).where(User.telegram_id == user_id)
                user = self.db_session.execute(user_stmt).scalar_one_or_none()
                
                if not user:
                    user = User(
                        telegram_id=user_id,
                        username=update.effective_user.username,
                        first_name=update.effective_user.first_name,
                        last_name=update.effective_user.last_name
                    )
                    self.db_session.add(user)
                    self.db_session.flush()
                
                # Record music playback
                music_playback = MusicPlayback(
                    server_id=server.id,
                    user_id=user.id,
                    song_title=title,
                    artist=channel,
                    url=url
                )
                self.db_session.add(music_playback)
                self.db_session.commit()
            except SQLAlchemyError as e:
                logger.error(f"Database error recording music playback: {e}")
                self.db_session.rollback()

            # Download and send the audio file
            audio_file = await self._get_audio_file(query, url)
            if audio_file:
                await context.bot.send_audio(
                    chat_id=chat_id,
                    audio=open(audio_file, 'rb'),
                    title=title,
                    performer=channel,
                    duration=duration,
                    thumb=thumbnail,
                    caption=f"🎵 {title}\n👤 {channel}"
                )
                # Clean up downloaded file
                try:
                    os.remove(audio_file)
                except:
                    pass
            else:
                await status_msg.edit_text("❌ Failed to process audio. Please try another song.")

        except Exception as e:
            logger.error(f"Error in play command: {e}")
            await status_msg.edit_text(
                "❌ Sorry, I couldn't find or process that song!\n"
                "Try providing a different song name or YouTube URL."
            )

    async def _get_audio_file(self, query: str, url: str) -> str:
        """Download and process audio file."""
        try:
            # Create a unique filename based on query
            safe_filename = "".join(x for x in query if x.isalnum() or x in (' ', '-', '_'))[:50]
            filename = f"audio_{safe_filename}.mp3"
            filepath = os.path.join(DOWNLOADS_DIR, filename)
            
            # Initialize downloader
            downloader = Handler(
                query=url,
                timeout=30,
                confirm=False,
                unique=False,
                thread=0
            )
            
            # Run the download process
            for entry in downloader.run(format='mp3', quality='.m4a', limit=1):
                if entry and 'dlink' in entry:
                    # Save the file
                    downloader.save(
                        entry,
                        dir=DOWNLOADS_DIR,
                        progress_bar=True,
                        naming_format=filename
                    )
                    
                    # Return the file path if it exists
                    if os.path.exists(filepath):
                        return filepath
                    
                    # If the file wasn't saved with our naming_format, find it
                    downloaded_files = [f for f in os.listdir(DOWNLOADS_DIR) if f.endswith('.mp3')]
                    if downloaded_files:
                        downloaded_file = os.path.join(DOWNLOADS_DIR, downloaded_files[0])
                        # Rename to our desired filename
                        os.rename(downloaded_file, filepath)
                        return filepath
            
            return None
            
        except Exception as e:
            logger.error(f"Error downloading audio: {e}")
            return None

    async def stop(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle the /stop command."""
        if not update.message.chat.type in ['group', 'supergroup']:
            await update.message.reply_text("This command can only be used in groups!")
            return

        chat_id = update.message.chat_id
        await update.message.reply_text("✅ Music playback stopped!")

def setup_music_bot(application):
    """Set up the music bot and register its commands."""
    try:
        music_bot = MusicBot(application.bot)
        application.add_handler(CommandHandler("play", music_bot.play))
        application.add_handler(CommandHandler("stop", music_bot.stop))
        return music_bot
    except Exception as e:
        logger.error(f"Failed to setup music bot: {e}")
        raise e